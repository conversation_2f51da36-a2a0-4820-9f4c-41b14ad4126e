// vite.config.js
import react from "file:///home/<USER>/git/github/proxy-shop/shop-web/node_modules/.pnpm/@vitejs+plugin-react@4.4.1_vite@5.4.19_@types+node@20.17.46_/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { codeInspectorPlugin } from "file:///home/<USER>/git/github/proxy-shop/shop-web/node_modules/.pnpm/code-inspector-plugin@0.20.10/node_modules/code-inspector-plugin/dist/index.mjs";
import path from "path";
import { defineConfig, loadEnv } from "file:///home/<USER>/git/github/proxy-shop/shop-web/node_modules/.pnpm/vite@5.4.19_@types+node@20.17.46/node_modules/vite/dist/node/index.js";
var __vite_injected_original_dirname = "/home/<USER>/git/github/proxy-shop/shop-web";
var vite_config_default = defineConfig(function(_a) {
  var mode = _a.mode;
  var env = loadEnv(mode, process.cwd());
  return {
    plugins: [
      react(),
      // 添加 code-inspector-plugin，仅在开发环境中启用
      process.env.NODE_ENV !== "production" && codeInspectorPlugin({
        bundler: "vite",
        editor: "cursor",
        behavior: {
          locate: false,
          copy: true
        }
      })
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    server: {
      port: parseInt(env.VITE_SHOP_WEB_PORT || "32070", 10)
      // 支持环境变量覆盖
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
