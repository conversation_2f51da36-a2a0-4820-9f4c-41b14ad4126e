import HeaderBalance from "@/components/wallet/HeaderBalance";
import { useUserStore } from "@/store/user";
import {
  ChevronDown,
  ChevronUp,
  LayoutDashboard,
  LogOut,
  Menu,
  Receipt,
  ShoppingBag,
  User,
  Wallet,
  Wifi,
  X,
} from "lucide-react";
import { useEffect, useState, type ReactNode } from "react";
import { Link, useLocation } from "react-router-dom";

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isStaticProxyOpen, setIsStaticProxyOpen] = useState(true);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const location = useLocation();
  const pathname = location.pathname;
  const { user, logout } = useUserStore();

  // Close mobile sidebar when clicking outside or on route change
  useEffect(() => {
    const handleResize = () => {
      // 使用更精确的断点判断
      if (window.innerWidth >= 1024) {
        // lg 断点
        setIsMobileSidebarOpen(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (isUserMenuOpen) {
        const target = event.target as Element;
        if (!target.closest("[data-user-menu]")) {
          setIsUserMenuOpen(false);
        }
      }
    };

    window.addEventListener("resize", handleResize);
    document.addEventListener("click", handleClickOutside);

    return () => {
      window.removeEventListener("resize", handleResize);
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isUserMenuOpen]);

  useEffect(() => {
    setIsMobileSidebarOpen(false);
    setIsUserMenuOpen(false);
  }, [pathname]);

  return (
    <div className="flex min-h-screen bg-background text-foreground">
      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-screen bg-card shadow-lg border-r border-border z-50 transform transition-transform duration-300 ease-in-out
          w-64
          ${isMobileSidebarOpen ? "translate-x-0" : "-translate-x-full"}
          lg:translate-x-0 lg:static lg:z-auto lg:shadow-none`}
      >
        {/* Logo */}
        <div className="h-[60px] sm:h-[64px] lg:h-[70px] flex items-center justify-between px-3 sm:px-4 lg:px-6 border-b border-border">
          <div className="flex items-center min-w-0">
            <img
              src="/GoMaskIP_logo.svg"
              alt="GoMaskIP"
              className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-[#80DEEA] mr-2 sm:mr-3 flex-shrink-0"
            />
            <div className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-primary truncate">
              Gomaskip
            </div>
          </div>
          {/* Close button for mobile */}
          <button
            onClick={() => setIsMobileSidebarOpen(false)}
            className="lg:hidden p-1.5 sm:p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors duration-150 flex-shrink-0"
            aria-label="关闭菜单"
          >
            <X className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>
        {/* Navigation */}
        <nav className="p-3 sm:p-4 lg:p-6 h-[calc(100vh-60px)] sm:h-[calc(100vh-64px)] lg:h-[calc(100vh-70px)] overflow-y-auto scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
          <div className="space-y-1 sm:space-y-2">
            <Link
              to="/dashboard"
              className={`flex items-center px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 rounded-lg transition-all duration-150 ${
                pathname === "/dashboard"
                  ? "text-primary bg-primary/10 font-medium shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
              }`}
            >
              <LayoutDashboard className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
              <span className="text-sm lg:text-base truncate">主页</span>
            </Link>

            <Link
              to="/orders"
              className={`flex items-center px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 rounded-lg transition-all duration-150 ${
                pathname === "/orders"
                  ? "text-primary bg-primary/10 font-medium shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
              }`}
            >
              <ShoppingBag className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
              <span className="text-sm lg:text-base truncate">我的订单</span>
            </Link>

            <Link
              to="/dashboard/wallet"
              className={`flex items-center px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 rounded-lg transition-all duration-150 ${
                pathname === "/dashboard/wallet"
                  ? "text-primary bg-primary/10 font-medium shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
              }`}
            >
              <Wallet className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
              <span className="text-sm lg:text-base truncate">余额管理</span>
            </Link>

            <Link
              to="/transactions"
              className={`flex items-center px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 rounded-lg transition-all duration-150 ${
                pathname === "/transactions"
                  ? "text-primary bg-primary/10 font-medium shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
              }`}
            >
              <Receipt className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
              <span className="text-sm lg:text-base truncate">消费记录</span>
            </Link>

            <Link
              to="/profile"
              className={`flex items-center px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 rounded-lg transition-all duration-150 ${
                pathname === "/profile"
                  ? "text-primary bg-primary/10 font-medium shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
              }`}
            >
              <User className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
              <span className="text-sm lg:text-base truncate">个人中心</span>
            </Link>

            {/* Static Proxy Section */}
            <div>
              <button
                onClick={() => setIsStaticProxyOpen(!isStaticProxyOpen)}
                className="flex items-center justify-between w-full px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-all duration-150 hover:shadow-sm"
              >
                <div className="flex items-center min-w-0">
                  <Wifi className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0" />
                  <span className="text-sm lg:text-base truncate">
                    代理管理
                  </span>
                </div>
                {isStaticProxyOpen ? (
                  <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 flex-shrink-0" />
                ) : (
                  <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 flex-shrink-0" />
                )}
              </button>
              {isStaticProxyOpen && (
                <div className="ml-3 sm:ml-4 lg:ml-6 mt-1 sm:mt-2 space-y-1">
                  <Link
                    to="/products/static-residential-proxies/pricing"
                    className={`flex items-center px-3 sm:px-4 lg:px-5 py-1.5 sm:py-2 lg:py-2.5 rounded-lg transition-all duration-150 ${
                      pathname ===
                      "/products/static-residential-proxies/pricing"
                        ? "text-primary bg-primary/10 font-medium shadow-sm"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
                    }`}
                  >
                    <span className="text-sm truncate">购买代理</span>
                  </Link>
                  <Link
                    to="/products/static-residential-proxies/purchased-ips"
                    className={`flex items-center px-3 sm:px-4 lg:px-5 py-1.5 sm:py-2 lg:py-2.5 rounded-lg transition-all duration-150 ${
                      pathname ===
                      "/products/static-residential-proxies/purchased-ips"
                        ? "text-primary bg-primary/10 font-medium shadow-sm"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
                    }`}
                  >
                    <span className="text-sm truncate">已购买静态IP</span>
                  </Link>
                  <Link
                    to="/products/dynamic-residential-proxies/purchased-ips"
                    className={`flex items-center px-3 sm:px-4 lg:px-5 py-1.5 sm:py-2 lg:py-2.5 rounded-lg transition-all duration-150 ${
                      pathname ===
                      "/products/dynamic-residential-proxies/purchased-ips"
                        ? "text-primary bg-primary/10 font-medium shadow-sm"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-sm"
                    }`}
                  >
                    <span className="text-sm truncate">已购买动态ip</span>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </nav>
      </div>

      {/* Main Content - 无需额外边距，因为侧边栏使用static定位已占据空间 */}
      <div className="flex-1 flex flex-col">
        {/* Header - 修改为 sticky 定位 */}
        <header className="sticky top-0 bg-card shadow-sm border-b border-border h-[60px] sm:h-[64px] lg:h-[70px] flex items-center justify-between px-3 sm:px-4 md:px-6 lg:px-8 xl:px-8 2xl:px-8 z-40">
          {/* Left side - Mobile menu button */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            <button
              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
              className="lg:hidden p-1.5 sm:p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-all duration-150 active:scale-95"
              aria-label="切换菜单"
            >
              <Menu className="w-5 h-5 sm:w-6 sm:h-6" />
            </button>
            {/* Theme/Language selector placeholder */}
            <div className="hidden lg:flex items-center space-x-2 text-muted-foreground cursor-pointer hover:text-foreground transition-colors duration-150">
              {/* <span>中文</span>
              <ChevronDown className="w-4 h-4" /> */}
            </div>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-6">
            <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-6">
              {/* Wallet Balance Display */}
              <HeaderBalance />

              {/* User Menu */}
              <div className="relative" data-user-menu>
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-1 sm:space-x-2 text-muted-foreground hover:text-foreground focus:outline-none transition-all duration-150 active:scale-95 p-1 rounded-lg hover:bg-muted"
                  aria-expanded={isUserMenuOpen}
                  aria-haspopup="true"
                >
                  <User className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  <span className="hidden sm:inline text-xs sm:text-sm lg:text-base max-w-[100px] sm:max-w-[120px] lg:max-w-[150px] truncate">
                    {user?.email || "加载中..."}
                  </span>
                  <ChevronDown
                    className={`w-3 h-3 sm:w-4 sm:h-4 transition-transform duration-200 ${
                      isUserMenuOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-40 sm:w-48 lg:w-52 bg-popover text-popover-foreground rounded-md shadow-lg py-1 z-50 border border-border ring-1 ring-black ring-opacity-5">
                    <Link
                      to="/profile"
                      onClick={() => setIsUserMenuOpen(false)}
                      className="flex items-center w-full px-3 sm:px-4 py-2 text-xs sm:text-sm hover:bg-muted focus:bg-muted focus:outline-none transition-colors duration-150"
                    >
                      <User className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3" />
                      个人中心
                    </Link>
                    <button
                      onClick={() => {
                        logout();
                        setIsUserMenuOpen(false);
                      }}
                      className="flex items-center w-full px-3 sm:px-4 py-2 text-xs sm:text-sm hover:bg-muted focus:bg-muted focus:outline-none transition-colors duration-150"
                    >
                      <LogOut className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3" />
                      退出登录
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>
        {/* Main Content Area */}
        <main className="flex-1 p-0 sm:p-2 md:p-4 lg:p-6 xl:p-8 2xl:p-8 bg-background min-h-0 overflow-auto">
          <div className="max-w-screen-2xl mx-auto">
            <div className="px-0 sm:px-2 lg:px-4">{children}</div>
          </div>
        </main>
      </div>
    </div>
  );
}
