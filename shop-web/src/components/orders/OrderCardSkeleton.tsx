import { Skeleton } from "@/components/ui/skeleton";

export default function OrderCardSkeleton() {
  return (
    <div className="border-b border-border last:border-b-0">
      <div className="p-4 sm:px-6 sm:py-5">
        <div className="flex flex-col gap-3 sm:gap-4">
          {/* 第一行：状态指示器 + 核心信息 + 箭头 */}
          <div className="flex items-start gap-3">
            <Skeleton className="w-3 h-3 rounded-full mt-1 shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="flex flex-col gap-1">
                <div className="flex items-start justify-between gap-2">
                  <Skeleton className="h-4 sm:h-5 w-3/5" />
                  <Skeleton className="h-4 sm:h-5 w-16 shrink-0" />
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-3 w-1 rounded-full" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            </div>
            <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 shrink-0 mt-1" />
          </div>

          {/* 第二行：重要标签信息 */}
          <div className="flex flex-wrap gap-1.5 sm:gap-2 pl-6">
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-12 rounded-full" />
            <Skeleton className="h-6 w-14 rounded-full" />
          </div>

          {/* 第三行：地理位置和支付信息 */}
          <div className="flex flex-wrap gap-1.5 sm:gap-2 pl-6">
            <Skeleton className="h-6 w-32 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-18 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );
}

export function OrderListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="bg-card rounded-lg shadow-sm overflow-hidden border border-border">
      {Array.from({ length: count }, (_, i) => (
        <OrderCardSkeleton key={i} />
      ))}
    </div>
  );
}