import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  getActivationStatus,
  retryActivation,
  type ActivationStatus,
} from "@/services/activation";
import { useQuery } from "@tanstack/react-query";
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Package,
  RefreshCw,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

interface ActivationProgressProps {
  orderId: string;
  isOpen: boolean;
  onClose: () => void;
  totalQuantity: number;
}

export function ActivationProgress({
  orderId,
  isOpen,
  onClose,
  totalQuantity,
}: ActivationProgressProps) {
  const navigate = useNavigate();
  const [isRetrying, setIsRetrying] = useState(false);

  // 查询开通状态，成功后停止轮询
  const {
    data: activationStatus,
    isLoading,
    error,
    refetch,
  } = useQuery<ActivationStatus>({
    queryKey: ["activation-status", orderId],
    queryFn: () => getActivationStatus(orderId),
    enabled: isOpen && !!orderId,
    refetchInterval: (query) => {
      const data = query.state.data;
      // 如果状态是completed或failed，停止轮询
      if (data?.status === "completed" || data?.status === "failed") {
        return false;
      }
      return 3000; // 3秒轮询一次
    },
    retry: (failureCount, error) => {
      console.error(
        `Activation status query failed (attempt ${failureCount + 1}):`,
        error
      );
      return failureCount < 2; // 最多重试2次
    },
  });

  // 计算进度百分比
  const progressPercent = activationStatus
    ? Math.round(
        (activationStatus.completedInstances /
          activationStatus.totalInstances) *
          100
      )
    : 0;

  // 自动跳转到已购商品页面（全部成功时）
  useEffect(() => {
    if (activationStatus?.status === "completed") {
      const timer = setTimeout(() => {
        onClose();
        navigate("/products/static-residential-proxies/purchased-ips");
      }, 2000); // 2秒后自动跳转
      return () => clearTimeout(timer);
    }
  }, [activationStatus?.status, navigate, onClose]);

  const getStatusIcon = () => {
    if (isLoading)
      return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;

    switch (activationStatus?.status) {
      case "completed":
        return <CheckCircle2 className="w-5 h-5 text-green-500" />;
      case "failed":
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case "processing":
        return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    if (isLoading) return "正在查询开通状态...";

    switch (activationStatus?.status) {
      case "pending":
        return "等待开始开通...";
      case "processing":
        return `正在开通中... (${activationStatus.completedInstances}/${activationStatus.totalInstances})`;
      case "completed":
        return "🎉 所有商品开通成功！";
      case "failed":
        const failedCount = activationStatus.failedInstances;
        const successCount = activationStatus.completedInstances;
        if (successCount > 0) {
          return `部分开通成功 (成功${successCount}个，失败${failedCount}个)`;
        }
        return "开通失败，请重试";
      default:
        return "未知状态";
    }
  };

  const handleViewProducts = () => {
    onClose();
    navigate("/products/static-residential-proxies");
  };

  const handleRetry = async () => {
    if (isRetrying) return; // 防止重复点击

    setIsRetrying(true);
    try {
      await retryActivation(orderId);
      refetch(); // 重新查询状态
    } catch (error) {
      console.error("Retry activation failed:", error);
    } finally {
      // 2秒后重新启用按钮
      setTimeout(() => setIsRetrying(false), 2000);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            商品开通进度
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 状态显示 */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            {getStatusIcon()}
            <span className="font-medium">{getStatusText()}</span>
          </div>

          {/* 进度条 */}
          {activationStatus && activationStatus.status !== "pending" && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>开通进度</span>
                <span>{progressPercent}%</span>
              </div>
              <Progress value={progressPercent} className="h-2" />
              <div className="text-xs text-gray-500 text-center">
                已完成 {activationStatus.completedInstances} /{" "}
                {activationStatus.totalInstances} 个商品
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {(error || activationStatus?.error) && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {error?.message || activationStatus?.error || "查询状态失败"}
              </AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-2 pt-2">
            {activationStatus?.status === "completed" && (
              <Button onClick={handleViewProducts} className="flex-1">
                查看已购商品
              </Button>
            )}

            {activationStatus?.status === "failed" &&
              activationStatus.canRetry && (
                <Button
                  onClick={handleRetry}
                  variant="outline"
                  className="flex-1"
                  disabled={isRetrying}
                >
                  <RefreshCw
                    className={`w-4 h-4 mr-2 ${
                      isRetrying ? "animate-spin" : ""
                    }`}
                  />
                  {isRetrying ? "重试中..." : "重试开通"}
                </Button>
              )}

            {activationStatus?.status === "processing" && (
              <Button onClick={onClose} variant="outline" className="flex-1">
                后台继续开通
              </Button>
            )}

            {(activationStatus?.status === "completed" ||
              activationStatus?.status === "failed") && (
              <Button onClick={onClose} variant="outline">
                关闭
              </Button>
            )}
          </div>

          {/* 说明信息 */}
          {activationStatus?.status === "processing" && (
            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
              💡 开通过程需要1-3分钟，您可以关闭此窗口，开通将在后台继续进行
            </div>
          )}

          {activationStatus?.status === "completed" && (
            <div className="text-xs text-gray-500 bg-green-50 p-2 rounded">
              ✅ 将在2秒后自动跳转到已购商品页面
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ActivationProgress;
