import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useProductPageStore } from "@/store/productStore";
import { ProxyType } from "@/types/productPageTypes";
import { Check } from "lucide-react";

// 注意：不再使用错误的 productType 映射
// 代理类型应该通过 config_proxy_type 字段进行查询，而不是 product_type

export const proxyTypes: ProxyType[] = [
  {
    id: "Shared (ISP) proxies",
    name: "普通",
    description: "经过我们严格的筛选过程，适合小型和初创企业。",
    recommended: true,
  },
  {
    id: "Premium (ISP) proxies",
    name: "原生",
    description: "电子商务、旅游和社交媒体领域最热门表现最佳的 IP。",
  },
  {
    id: "DYNAMIC",
    name: "动态",
    description: "轮换IP地址，适合需要大量IP的业务场景。",
  },
];

interface ProxyTypeSelectorProps {
  onTypeChange?: (proxyType: string) => void; // 修改：传递代理类型字符串而不是 productType
  compact?: boolean; // 新增：紧凑模式
}

const ProxyTypeSelector = ({
  onTypeChange,
  compact = false,
}: ProxyTypeSelectorProps) => {
  const { selectedProxyType, setSelectedProxyType } = useProductPageStore();

  const handleTypeSelect = (type: ProxyType) => {
    // 只有当选择的类型与当前类型不同时才更新
    if (selectedProxyType?.id !== type.id) {
      setSelectedProxyType(type);

      // 触发代理类型变更回调 - 传递实际的代理类型字符串
      if (onTypeChange) {
        onTypeChange(type.id); // 直接传递代理类型 ID（如 "Premium (ISP) proxies"）
      }
    }
  };

  if (compact) {
    // 紧凑模式 - 不显示标题和描述文字
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {proxyTypes.map((type) => (
          <Card
            key={type.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              selectedProxyType?.id === type.id
                ? "border-2 border-purple-500 bg-purple-50"
                : "border-2 border-transparent hover:border-purple-300"
            }`}
            onClick={() => handleTypeSelect(type)}
          >
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all"
                    style={{
                      borderColor:
                        selectedProxyType?.id === type.id
                          ? "#865DDC"
                          : "#E2E8F0",
                      backgroundColor:
                        selectedProxyType?.id === type.id
                          ? "#865DDC"
                          : "transparent",
                    }}
                  >
                    {selectedProxyType?.id === type.id && (
                      <Check className="w-2.5 h-2.5 text-white" />
                    )}
                  </div>
                  <div>
                    <h4
                      className="font-medium text-sm"
                      style={{ color: "#1A202C" }}
                    >
                      {type.name}
                      {selectedProxyType?.id === type.id && (
                        <span className="ml-2 text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">
                          已选择
                        </span>
                      )}
                    </h4>
                    <p className="text-xs mt-1" style={{ color: "#718096" }}>
                      {type.description}
                    </p>
                  </div>
                </div>
                {type.recommended && (
                  <Badge
                    className="text-xs"
                    style={{ backgroundColor: "#F56565", color: "white" }}
                  >
                    Best Results
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // 原始完整模式
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold" style={{ color: "#1A202C" }}>
        选择静态住宅IP类型
      </h2>
      <p className="text-sm" style={{ color: "#718096" }}>
        不同地区静态住宅有独特的时长算法需求，建议使用:{" "}
        <span style={{ color: "#865DDC" }}>7天周期</span>
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {proxyTypes.map((type) => (
          <Card
            key={type.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              selectedProxyType?.id === type.id
                ? "border-2 border-purple-500 bg-purple-50"
                : "border-2 border-transparent hover:border-purple-300"
            }`}
            onClick={() => handleTypeSelect(type)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all"
                    style={{
                      borderColor:
                        selectedProxyType?.id === type.id
                          ? "#865DDC"
                          : "#E2E8F0",
                      backgroundColor:
                        selectedProxyType?.id === type.id
                          ? "#865DDC"
                          : "transparent",
                    }}
                  >
                    {selectedProxyType?.id === type.id && (
                      <Check className="w-3 h-3 text-white" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium" style={{ color: "#1A202C" }}>
                      {type.name}
                      {selectedProxyType?.id === type.id && (
                        <span className="ml-2 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                          已选择
                        </span>
                      )}
                    </h3>
                    <p className="text-sm mt-1" style={{ color: "#718096" }}>
                      {type.description}
                    </p>
                  </div>
                </div>
                {type.recommended && (
                  <Badge
                    className="text-xs"
                    style={{ backgroundColor: "#F56565", color: "white" }}
                  >
                    Best Results
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ProxyTypeSelector;
