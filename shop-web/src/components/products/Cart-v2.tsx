import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useOrderCreationStore } from "@/store/order-creation";
import { useProductPageStore } from "@/store/productStore";
import type { shopProduct } from "@/types/shop-product";
import { AlertCircle, CreditCard } from "lucide-react";
import { useEffect, useState } from "react";
import ReactCountryFlag from "react-country-flag";
import { useNavigate } from "react-router-dom";

// 导入我们新的工具函数
import {
  createResolverParams,
  resolveProductWithCache,
  type ProductResolveResult,
} from "@/services/product-resolver";
import { getProductById } from "@/services/products"; // 导入 getProductById
import { getCountryCode, getFullLocationDisplay } from "@/utils/location-utils";
import { calculateTotalPrice, getPriceLabel } from "@/utils/price-utils";

/**
 * 新版购物车组件 - 使用标准化产品和预计算价格
 */
const CartV2 = () => {
  const navigate = useNavigate();
  const {
    selectedProxyType,
    selectedLocationId,
    selectedId, // 改为id，统一使用id字段
    count,
    setCount,
    duration,
  } = useProductPageStore();

  const [resolvedProduct, setResolvedProduct] = useState<shopProduct | null>(
    null
  );
  const [resolveError, setResolveError] = useState<string | null>(null);
  const [isResolving, setIsResolving] = useState(false);

  // 当选择的位置或产品ID发生变化时，解析对应的标准化产品
  useEffect(() => {
    const resolveProductData = async () => {
      setIsResolving(true);
      setResolveError(null);

      try {
        if (selectedId) {
          // 优先使用产品ID获取产品详情
          console.log("尝试通过产品ID获取产品:", selectedId);
          const productData: shopProduct | null | undefined =
            await getProductById(selectedId);
          if (productData) {
            console.log("成功通过产品ID获取产品:", productData);
            setResolvedProduct(productData);
          } else {
            const errorMsg = "无法通过ID获取产品信息或产品不存在";
            console.error(
              "Error fetching product by ID:",
              errorMsg,
              productData
            );
            setResolveError(errorMsg);
            setResolvedProduct(null);
          }
        } else if (selectedLocationId && selectedProxyType) {
          // 如果没有产品ID，则回退到现有逻辑，通过位置和代理类型解析产品
          console.log("尝试通过位置和代理类型解析产品:", {
            selectedLocationId,
            selectedProxyType: selectedProxyType.id,
          });
          const params = createResolverParams(
            {
              city_id: String(selectedLocationId), // 确保是字符串类型
              city_name: "",
              country_code: "",
              number: 0,
              continents_name: "",
            },
            selectedProxyType.id,
            "IPNUX"
          );

          console.log("解析产品参数:", params);

          const productResolveResult: ProductResolveResult =
            await resolveProductWithCache(params);

          if (productResolveResult.success && productResolveResult.product) {
            console.log("成功解析产品:", productResolveResult.product);
            setResolvedProduct(productResolveResult.product);
          } else {
            const errorMsg = productResolveResult.error || "无法找到对应的产品";
            console.error(errorMsg);
            setResolveError(errorMsg);
            setResolvedProduct(null);
          }
        } else {
          // 没有足够的信息来获取或解析产品
          setResolvedProduct(null);
          setResolveError("请先选择IP位置或产品");
        }
      } catch (error) {
        const errorMsg =
          error instanceof Error ? error.message : "产品加载失败";
        console.error("产品加载失败:", errorMsg);
        setResolveError(errorMsg);
        setResolvedProduct(null);
      } finally {
        setIsResolving(false);
      }
    };

    resolveProductData();
  }, [selectedId, selectedLocationId, selectedProxyType]); // 改为selectedId

  // 获取价格信息
  const priceInfo = resolvedProduct ? getPriceLabel(resolvedProduct) : null;
  const totalPriceInfo = resolvedProduct
    ? calculateTotalPrice(resolvedProduct, count)
    : null;

  // 处理结账按钮点击
  const setOrderDetails = useOrderCreationStore(
    (state) => state.setOrderDetails
  );

  const handleCheckout = () => {
    if (!resolvedProduct || !selectedProxyType) {
      return;
    }

    // 设置订单详情到 store
    console.log("Cart-v2: resolvedProduct for checkout:", resolvedProduct);
    console.log("Cart-v2: limitTrafficGb for checkout:", resolvedProduct?.limitTrafficGb);
    setOrderDetails({
      id: resolvedProduct.id.toString(), // 改为id，表示产品的数据库主键
      quantity: count,
      duration: duration,
      limitTrafficGb: resolvedProduct.limitTrafficGb ?? 1, // 添加 limitTrafficGb，如果产品没有则默认为1
    });

    // 直接跳转到结账页面，不传递 URL 参数
    navigate("/orders/static-ip-checkout");
  };

  return (
    <Card style={{ backgroundColor: "#FFFFFF" }}>
      <CardHeader>
        <CardTitle className="flex items-center" style={{ color: "#1A202C" }}>
          🛒 订单汇总
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 选择IP位置显示 */}
        {resolvedProduct ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg h-[70px]">
              {isResolving ? (
                <>
                  <div className="w-8 h-6 bg-gray-300 rounded animate-pulse"></div>
                  <div className="flex-1">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2 text-sm text-gray-600">
                      正在获取产品信息...
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <ReactCountryFlag
                    countryCode={getCountryCode(resolvedProduct) || "US"}
                    svg
                    style={{
                      // 定比例
                      width: "64px",
                      height: "48px",
                      borderRadius: "4px",
                    }}
                    title={getCountryCode(resolvedProduct)}
                  />
                  <div className="flex-1">
                    <div className="text-sm text-gray-600">
                      {getFullLocationDisplay(resolvedProduct)}
                    </div>
                    <div className="flex items-center mt-1">
                      <Badge variant="outline" className="text-xs">
                        可用: {resolvedProduct.availableCount ?? "N/A"}
                      </Badge>
                      {resolvedProduct.availableCount !== undefined &&
                        resolvedProduct.availableCount <= 10 &&
                        resolvedProduct.availableCount > 0 && (
                          <Badge variant="destructive" className="text-xs ml-2">
                            库存紧张
                          </Badge>
                        )}
                      {resolvedProduct.availableCount === 0 && (
                        <Badge variant="destructive" className="text-xs ml-2">
                          缺货
                        </Badge>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>

            {resolveError && (
              <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                <span className="text-sm text-red-700">{resolveError}</span>
              </div>
            )}

            {/* IP数量输入 */}
            <div>
              <Label htmlFor="count">数量</Label>
              <Input
                id="count"
                type="number"
                min={resolvedProduct.minQuantity || 1}
                max={resolvedProduct.availableCount || 1000}
                value={count}
                onChange={(e) => setCount(parseInt(e.target.value) || 1)}
                className="mt-1"
                placeholder="输入IP数量"
              />
              {/* 库存检查提示 */}
              {resolvedProduct &&
                resolvedProduct.availableCount !== undefined && (
                  <>
                    {count > resolvedProduct.availableCount && (
                      <p className="text-sm text-red-600 mt-1">
                        数量不能超过可用库存 ({resolvedProduct.availableCount})
                      </p>
                    )}
                    {resolvedProduct.minQuantity &&
                      count < resolvedProduct.minQuantity && (
                        <p className="text-sm text-orange-600 mt-1">
                          最小购买数量为 {resolvedProduct.minQuantity}
                        </p>
                      )}
                    {resolvedProduct.maxQuantity &&
                      count > resolvedProduct.maxQuantity && (
                        <p className="text-sm text-orange-600 mt-1">
                          最大购买数量为 {resolvedProduct.maxQuantity}
                        </p>
                      )}
                  </>
                )}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {isResolving ? (
              <div className="flex items-center justify-center">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-sm">正在加载产品信息...</span>
              </div>
            ) : resolveError ? (
              <div className="flex items-center justify-center text-red-500">
                <AlertCircle className="w-4 h-4 mr-1" />
                <span>{resolveError}</span>
              </div>
            ) : (
              <p>请先选择IP位置或产品</p>
            )}
          </div>
        )}

        {resolvedProduct && priceInfo && totalPriceInfo && (
          <>
            <Separator />

            {/* 价格详情 */}
            <div className="space-y-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>每个IP的价格:</span>
                  <div className="text-right">
                    <div className="font-medium">{priceInfo.mainPrice}</div>
                    {priceInfo.hasDiscount && priceInfo.originalPrice && (
                      <div className="text-xs text-gray-500 line-through">
                        {priceInfo.originalPrice}
                      </div>
                    )}
                    {resolvedProduct.isPriceManual && (
                      <Badge variant="secondary" className="text-xs ml-1">
                        手动定价
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>有效期:</span>
                  <span>{resolvedProduct.validityPeriod} 天</span>
                </div>
                <div className="flex justify-between">
                  <span>数量:</span>
                  <span>{count}</span>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>总计</span>
                  <span
                    className="font-semibold text-lg"
                    style={{ color: "#1A202C" }}
                  >
                    {totalPriceInfo.totalPrice}
                  </span>
                </div>

                {priceInfo.hasDiscount && priceInfo.discountPercentage && (
                  <div className="flex justify-between text-sm">
                    <span className="text-green-600">
                      节省 ({priceInfo.discountPercentage}% 折扣)
                    </span>
                    <span className="text-green-600">
                      {totalPriceInfo.savings}
                    </span>
                  </div>
                )}

                <p className="text-xs text-right text-gray-500">
                  价格已包含税费 • 货币: {priceInfo.currency}
                </p>
              </div>

              {/* 支付方式图标 */}
              <div className="flex space-x-2 flex-wrap gap-1">
                <div className="w-8 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center">
                  VISA
                </div>
                <div className="w-8 h-6 bg-red-600 rounded text-white text-xs flex items-center justify-center">
                  MC
                </div>
                <div className="w-8 h-6 bg-gray-800 rounded text-white text-xs flex items-center justify-center">
                  AE
                </div>
                <div className="w-8 h-6 bg-blue-500 rounded text-white text-xs flex items-center justify-center">
                  PP
                </div>
                <div className="w-8 h-6 bg-cyan-500 rounded text-white text-xs flex items-center justify-center">
                  AP
                </div>
              </div>

              <Button
                className="w-full"
                style={{ backgroundColor: "#865DDC", borderColor: "#865DDC" }}
                disabled={
                  !resolvedProduct ||
                  count < 1 ||
                  isResolving ||
                  // 库存检查
                  (resolvedProduct.availableCount !== undefined &&
                    count > resolvedProduct.availableCount) ||
                  (resolvedProduct.minQuantity &&
                    count < resolvedProduct.minQuantity) ||
                  (resolvedProduct.maxQuantity &&
                    count > resolvedProduct.maxQuantity) ||
                  resolvedProduct.availableCount === 0
                }
                onClick={handleCheckout}
              >
                <CreditCard className="w-4 h-4 mr-2" />
                {isResolving
                  ? "准备中..."
                  : resolvedProduct?.availableCount === 0
                  ? "缺货"
                  : "继续结账"}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default CartV2;
