import ProtectedRoute from "@/components/ProtectedRoute";
import { Loading } from "@/components/ui";
import RootLayout from "@/layouts/RootLayout";
import { lazy, Suspense } from "react";
import { createBrowserRouter, Outlet, RouteObject } from "react-router-dom";

// Lazy-loaded page components
const Deposit = lazy(() => import("@/pages/deposit"));
const Dashboard = lazy(() => import("@/pages/dashboard"));
const Login = lazy(() => import("@/pages/login"));
const ResetPassword = lazy(() => import("@/pages/reset-password"));
const HomePage = lazy(() => import("@/pages/home"));
const Orders = lazy(() => import("@/pages/orders"));
const OrderDetail = lazy(() => import("@/pages/orders/OrderDetail"));
const DetailProxy = lazy(
  () => import("@/pages/products/detail/ProductDetailV2")
);

const CompleteOrder = lazy(() => import("@/pages/orders/CompleteOrder"));
const PaymentResult = lazy(() => import("@/pages/orders/PaymentResult"));
const StaticResidentialProxiesPricing = lazy(
  () => import("@/pages/products/static-residential-proxies/PricingV2")
);
const PurchasedIPList = lazy(
  () => import("@/pages/products/static-residential-proxies/PurchasedIPList")
);
const PurchasedDynamicIPList = lazy(
  () =>
    import(
      "@/pages/products/dynamic-residential-proxies/PurchasedDynamicIPList"
    )
);
const StaticIPCheckout = lazy(() => import("@/pages/orders/StaticIPCheckout"));
const DynamicIPCheckout = lazy(
  () => import("@/pages/orders/DynamicIPCheckout")
);
const RenewalCheckout = lazy(() => import("@/pages/orders/RenewalCheckout"));
const WalletPage = lazy(() => import("@/pages/dashboard/wallet"));
const TransactionsPage = lazy(() => import("@/pages/transactions"));
const ProfilePage = lazy(() => import("@/pages/profile"));
// Protected route wrapper
const ProtectedRouteWrapper = () => (
  <ProtectedRoute>
    <Outlet />
  </ProtectedRoute>
);

// Wrap lazy-loaded components with Suspense
const lazyLoad = (Component: React.LazyExoticComponent<any>) => {
  return (
    <Suspense fallback={<Loading fullScreen={true} text="加载中..." />}>
      <Component />
    </Suspense>
  );
};

const routes: RouteObject[] = [
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: lazyLoad(HomePage),
      },
      {
        path: "/login",
        element: lazyLoad(Login),
      },
      {
        path: "/reset-password",
        element: lazyLoad(ResetPassword),
      },
      {
        path: "/",
        element: <ProtectedRouteWrapper />,
        children: [
          {
            path: "dashboard",
            element: lazyLoad(Dashboard),
          },
          {
            path: "dashboard/wallet",
            element: lazyLoad(WalletPage),
          },
          {
            path: "transactions",
            element: lazyLoad(TransactionsPage),
          },
          {
            path: "profile",
            element: lazyLoad(ProfilePage),
          },
          {
            path: "orders",
            element: lazyLoad(Orders),
          },
          {
            path: "orders/complete-order",
            element: lazyLoad(CompleteOrder),
          },
          {
            path: "orders/static-ip-checkout",
            element: lazyLoad(StaticIPCheckout),
          },
          {
            path: "orders/dynamic-ip-checkout",
            element: lazyLoad(DynamicIPCheckout),
          },
          {
            path: "checkout/renewal",
            element: lazyLoad(RenewalCheckout),
          },
          {
            path: "orders/:id",
            element: lazyLoad(OrderDetail),
          },
          {
            path: "orders/:id/payment-result",
            element: lazyLoad(PaymentResult),
          },
          {
            path: "products/static-residential-proxies",
            element: lazyLoad(StaticResidentialProxiesPricing),
          },
          {
            path: "products/static-residential-proxies/pricing",
            element: lazyLoad(StaticResidentialProxiesPricing),
          },
          {
            path: "products/static-residential-proxies/purchased-ips",
            element: lazyLoad(PurchasedIPList),
          },
          {
            path: "products/dynamic-residential-proxies/purchased-ips",
            element: lazyLoad(PurchasedDynamicIPList),
          },
          {
            path: "products/detail",
            element: lazyLoad(DetailProxy),
          },

          {
            path: "deposit",
            element: lazyLoad(Deposit),
          },
        ],
      },
    ],
  },
];

export const router: any = createBrowserRouter(routes);
