/**
 * 邮件相关API - 使用专用的邮件客户端，具有更长的超时时间
 */

import { mailApiClient } from '@/services/api-client';
import type { ResultData } from '@/types/global';
import type { ApiTypes } from './generated/types';

/**
 * 发送登录验证码（使用专用邮件客户端，30秒超时）
 * @param data Request body payload
 */
export const postShopAuthSendLoginCode = async (
  data: ApiTypes.SendCodeDto
): Promise<ResultData<any>> => {
  const url = '/api/shop/auth/send-login-code';
  const response = await mailApiClient.post<ResultData<any>>(url, data);

  // Return the actual data from the response, which matches the ResultData
  return response.data;
};

/**
 * 发送注册验证码（使用专用邮件客户端，30秒超时）
 * @param data Request body payload
 */
export const postShopAuthSendRegistrationCode = async (
  data: ApiTypes.SendCodeDto
): Promise<ResultData<any>> => {
  const url = '/api/shop/auth/send-registration-code';
  const response = await mailApiClient.post<ResultData<any>>(url, data);

  return response.data;
};

/**
 * 请求重置密码（使用专用邮件客户端，30秒超时）
 * @param data Request body payload
 */
export const postShopAuthResetPasswordRequest = async (
  data: ApiTypes.ResetPasswordRequestDto
): Promise<ResultData<any>> => {
  const url = '/api/shop/auth/reset-password-request';
  const response = await mailApiClient.post<ResultData<any>>(url, data);

  return response.data;
};