import axiosInstance from "./api-client";
import type { ApiTypes } from "./generated/types";

export interface CreateDynamicIpOrderDto {
  id: string;
  limitTrafficGb: number;
  duration: number;
  paymentMethod: string;
  remark?: string;
}

/**
 * 创建动态IP订单
 */
export const createDynamicIpOrder = async (
  orderData: CreateDynamicIpOrderDto
): Promise<ApiTypes.Order> => {
  const response = await axiosInstance.post<ApiTypes.Order>(
    "/api/shop/orders/dynamic-ip",
    orderData
  );
  return response.data;
};
