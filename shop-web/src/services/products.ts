import apiClient from "./api-client";
import { ResultData, PageResponse } from "@/types/global";
import { shopProduct } from "@/types/shop-product";

export interface ProductPrice {
  id: string;
  dataAmount: number;
  pricePerGB: number;
  discount: number;
  isPopular: boolean;
}

// API endpoints
const API_ENDPOINTS = {
  PRODUCTS: "/api/shop/products",
  DETAIL: "/api/shop/products/detail",
  DATACENTER: "/api/shop/products/datacenter",
  MOBILE: "/api/shop/products/mobile",
};

// Get all products
export const getProducts = async (
  params: any
): Promise<PageResponse<shopProduct>> => {
  const response = await apiClient.get(API_ENDPOINTS.PRODUCTS, {
    params,
  });
  return response.data;
};

// Get a specific product by ID
export const getProductById = async (
  id: string | number // 允许接受 string 或 number 类型的 ID
): Promise<ResultData<shopProduct>> => {
  const response = await apiClient.get(`${API_ENDPOINTS.PRODUCTS}/${id}`);
  return response.data;
};

// Get detail proxy products
export const getDetailProxies = async (): Promise<
  ResultData<shopProduct[]>
> => {
  const response = await apiClient.get(API_ENDPOINTS.DETAIL);
  return response.data;
};

// Get datacenter proxy products
export const getDatacenterProxies = async (): Promise<
  ResultData<shopProduct[]>
> => {
  const response = await apiClient.get(API_ENDPOINTS.DATACENTER);
  return response.data;
};

// Get mobile proxy products
export const getMobileProxies = async (): Promise<
  ResultData<shopProduct[]>
> => {
  const response = await apiClient.get(API_ENDPOINTS.MOBILE);
  return response.data;
};

// Get product pricing options
export const getProductPricing = async (
  id: string
): Promise<ResultData<ProductPrice[]>> => {
  const response = await apiClient.get(
    `${API_ENDPOINTS.PRODUCTS}/${id}/pricing`
  );
  return response.data;
};

// Purchase a product
export interface PurchaseRequest {
  id: string;
  dataAmount: number;
  paymentMethod: string;
}

export interface PurchaseResponse {
  orderId: string;
  status: string;
  redirectUrl?: string;
}

export const purchaseProduct = async (
  data: PurchaseRequest
): Promise<ResultData<PurchaseResponse>> => {
  const response = await apiClient.post(
    `${API_ENDPOINTS.PRODUCTS}/purchase`,
    data
  );
  return response.data;
};
