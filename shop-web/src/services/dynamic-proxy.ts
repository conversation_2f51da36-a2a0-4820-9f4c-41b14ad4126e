import apiClient from '@/services/api-client';
import type { ResultData } from '@/types/global';
import type { ApiTypes } from './generated/types';

export const listUserChannels = async (): Promise<ResultData<any>> => {
  const url = '/api/shop/proxy/dynamic/channels';
  const response = await apiClient.get<ResultData<any>>(url);
  return response.data;
};

export const getChannelDetails = async (instanceId: number): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/channels/${instanceId}`;
  const response = await apiClient.get<ResultData<any>>(url);
  return response.data;
};

export const updateChannel = async (instanceId: number, data: any): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/channels/${instanceId}`;
  const response = await apiClient.put<ResultData<any>>(url, data);
  return response.data;
};

export const getChannelTraffic = async (instanceId: number, params: any): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/channels/${instanceId}/traffic`;
  const response = await apiClient.get<ResultData<any>>(url, { params });
  return response.data;
};

export const generateEndpoints = async (instanceId: number, data: any): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/channels/${instanceId}/endpoints`;
  const response = await apiClient.post<ResultData<any>>(url, data);
  return response.data;
};

export const listChannelEndpoints = async (instanceId: number): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/channels/${instanceId}/endpoints`;
  const response = await apiClient.get<ResultData<any>>(url);
  return response.data;
};

export const deleteEndpoint = async (endpointId: number): Promise<ResultData<any>> => {
  const url = `/api/shop/proxy/dynamic/endpoints/${endpointId}`;
  const response = await apiClient.delete<ResultData<any>>(url);
  return response.data;
};
