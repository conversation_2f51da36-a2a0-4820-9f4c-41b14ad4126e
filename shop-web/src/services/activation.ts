// 开通状态相关的API服务
import { API_BASE_URL } from "@/constants";
import { getAccessToken } from "@/utils/token-utils";

export interface ActivationStatus {
  status: "pending" | "processing" | "completed" | "failed";
  totalInstances: number;
  completedInstances: number;
  failedInstances: number;
  retryCount: number;
  error?: string;
  canRetry?: boolean;
}

/**
 * 查询订单开通状态
 */
export async function getActivationStatus(
  orderId: string
): Promise<ActivationStatus> {
  const token = getAccessToken();
  const response = await fetch(
    `${API_BASE_URL}/api/shop/orders/${orderId}/activation-status`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to fetch activation status: ${response.statusText}`
    );
  }

  const result = await response.json();
  // 后端返回格式为 { code, data, message }，需要提取 data 部分
  return result.data || result;
}

/**
 * 重试订单开通
 */
export async function retryActivation(
  orderId: string
): Promise<{ message: string }> {
  const token = getAccessToken();
  const response = await fetch(
    `${API_BASE_URL}/api/shop/orders/${orderId}/retry-activation`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to retry activation: ${response.statusText}`);
  }

  const result = await response.json();
  // 后端返回格式为 { code, data, message }，需要提取 data 部分
  return result.data || result;
}
