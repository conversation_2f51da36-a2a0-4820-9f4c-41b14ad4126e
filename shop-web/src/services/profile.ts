import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import apiClient from "./api-client";
import type { UpdateProfileRequest, ChangePasswordRequest } from "@/types/profile";
import type { ResultData, ResponseInterface } from "@/types/global";
import type { User } from "@/store/user";
import { toast } from "sonner";

// 获取个人信息
export const useGetProfile = () => {
  return useQuery({
    queryKey: ["profile"],
    queryFn: async (): Promise<User> => {
      const response = await apiClient.get<ResponseInterface<User>>("/api/shop/auth/profile");
      return response.data.data;
    },
  });
};

// 更新个人信息
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateProfileRequest): Promise<User> => {
      const response = await apiClient.put<ResponseInterface<User>>("/api/shop/auth/profile", data);
      return response.data.data;
    },
    onSuccess: (data) => {
      // 更新缓存
      queryClient.setQueryData(["profile"], data);
      // 同时更新用户信息缓存
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("个人信息更新成功");
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "更新失败，请重试";
      toast.error(message);
    },
  });
};

// 修改密码
export const useChangePassword = () => {
  return useMutation({
    mutationFn: async (data: ChangePasswordRequest): Promise<any> => {
      const response = await apiClient.put<ResponseInterface<any>>("/api/shop/auth/password", data);
      return response.data.data;
    },
    onSuccess: () => {
      toast.success("密码修改成功");
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "密码修改失败，请重试";
      toast.error(message);
    },
  });
};