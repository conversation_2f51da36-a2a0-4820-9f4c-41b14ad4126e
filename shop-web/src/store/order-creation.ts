import { create } from "zustand";

interface OrderCreationState {
  id: string | null; // 改为id，表示产品的数据库主键
  quantity: number;
  duration: number;
  limitTrafficGb: number; // 新增流量限制属性
  
  // 设置订单详情
  setOrderDetails: (details: {
    id: string; // 改为id
    quantity: number;
    duration: number;
    limitTrafficGb: number; // 新增流量限制属性
  }) => void;
  
  // 重置状态
  reset: () => void;
  
  // 检查是否有有效的订单数据
  hasValidOrderData: () => boolean;
}

export const useOrderCreationStore = create<OrderCreationState>((set, get) => ({
  id: null, // 改为id
  quantity: 1,
  duration: 30, // 默认30天
  limitTrafficGb: 1, // 默认流量限制为1GB
  
  setOrderDetails: (details) => set(details),
  
  reset: () =>
    set({
      id: null, // 改为id
      quantity: 1,
      duration: 30,
      limitTrafficGb: 1, // 重置时也重置流量限制
      }),

  hasValidOrderData: () => {
    const state = get();
    return state.id !== null && state.quantity > 0 && state.duration > 0; // 改为id
  },}));
