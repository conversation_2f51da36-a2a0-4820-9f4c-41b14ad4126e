import ActivationProgress from "@/components/orders/ActivationProgress";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BalancePayment from "@/components/wallet/BalancePayment";
import { getUsdToCnyRate } from "@/services/exchange-rate";
import { createPayment } from "@/services/payment";
import {
  getPaymentMethods,
  type PaymentMethod,
} from "@/services/payment-methods";
import { getProductById } from "@/services/products";
import { createDynamicIpOrder } from "@/services/dynamic-ip-orders";
import { useOrderCreationStore } from "@/store/order-creation";
import { useUserStore } from "@/store/user";
import type { shopProduct } from "@/types/shop-product";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

function DynamicIPCheckout() {
  const navigate = useNavigate();
  const [selectedPaymentType, setSelectedPaymentType] = useState<string>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [showActivationProgress, setShowActivationProgress] = useState(false);
  const userStore = useUserStore();

  // 从 store 获取订单信息
  const {
    id: productId,
    quantity, // This will be used for traffic amount (e.g., in GB)
    duration,
    limitTrafficGb,
    hasValidOrderData,
    reset,
  } = useOrderCreationStore();

  // 检查是否有有效的订单数据，如果没有则重定向
  useEffect(() => {
    if (!hasValidOrderData()) {
      toast.error("订单信息缺失，请重新选择产品");
      navigate("/products/dynamic-residential-proxies"); // Redirect to dynamic IP products page
      return;
    }
  }, [hasValidOrderData, navigate]);

  // 通过productId获取产品详情
  const {
    data: product,
    isLoading: productLoading,
    error: productError,
  } = useQuery<shopProduct>({
    queryKey: ["product", productId],
    queryFn: async () => {
      if (!productId) throw new Error("缺少产品ID");
      const result = await getProductById(productId);
      if (!result) throw new Error("API响应为空");
      if (typeof result === "object" && "data" in result && "code" in result) {
        if (!result.data) throw new Error("产品数据为空");
        return result.data as shopProduct;
      }
      if (typeof result === "object" && "id" in result) {
        return result as shopProduct;
      }
      throw new Error("产品数据格式不正确");
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error) => failureCount < 2,
  });

  // 获取实时汇率
  const { data: exchangeRate = 7.25 } = useQuery<number>({
    queryKey: ["exchangeRate", "USD", "CNY"],
    queryFn: getUsdToCnyRate,
    staleTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // 计算价格信息
  const unitPrice = product ? parseFloat(product.price) : 0; // Assuming price is per GB
  const totalPrice = unitPrice * (limitTrafficGb || 1);

  const productCurrency = product?.currency || "CNY";

  let pricePerUnitCNY: number;
  let pricePerUnitUSD: number;
  let totalPriceCNY: number;
  let totalPriceUSD: number;

  if (productCurrency === "CNY") {
    pricePerUnitCNY = unitPrice;
    totalPriceCNY = totalPrice;
    pricePerUnitUSD = Number((unitPrice / exchangeRate).toFixed(2));
    totalPriceUSD = Number((totalPrice / exchangeRate).toFixed(2));
  } else if (productCurrency === "USD") {
    pricePerUnitUSD = unitPrice;
    totalPriceUSD = totalPrice;
    pricePerUnitCNY = Number((unitPrice * exchangeRate).toFixed(2));
    totalPriceCNY = Number((totalPrice * exchangeRate).toFixed(2));
  } else {
    pricePerUnitCNY = unitPrice;
    totalPriceCNY = totalPrice;
    pricePerUnitUSD = Number((unitPrice / exchangeRate).toFixed(2));
    totalPriceUSD = Number((totalPrice / exchangeRate).toFixed(2));
  }

  const orderDetails = {
    productId: productId || "",
    limitTrafficGb: limitTrafficGb || 1,
    duration,
  };

  // 获取支付方式列表
  const { data: paymentMethods = [], isLoading: paymentMethodsLoading } =
    useQuery<PaymentMethod[]>({
      queryKey: ["paymentMethods"],
      queryFn: getPaymentMethods,
      staleTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
    });

  const handleBalancePaymentSuccess = (paidOrderId?: string) => {
    toast.success("余额支付成功！");
    if (paidOrderId) {
      setOrderId(paidOrderId);
      setShowActivationProgress(true);
    }
  };

  const handleBalancePaymentError = (error: string) => {
    toast.error(`余额支付失败：${error}`);
    setIsSubmitting(false);
  };

  const createOrderForBalancePayment = async () => {
    const orderPayload = {
      id: orderDetails.productId,
      limitTrafficGb: orderDetails.limitTrafficGb,
      duration: orderDetails.duration,
      paymentMethod: "balance",
    };

    const result = await createDynamicIpOrder(orderPayload);
    const createdOrderId = result.orderId;
    setOrderId(createdOrderId);
    return createdOrderId;
  };

  const handleOrderSubmit = async () => {
    setIsSubmitting(true);
    try {
      if (!selectedPaymentType) {
        toast.error("请选择支付方式");
        setIsSubmitting(false);
        return;
      }

      if (selectedPaymentType === "balance") {
        toast.error("请使用余额支付按钮完成支付");
        setIsSubmitting(false);
        return;
      }

      const orderPayload = {
        id: orderDetails.productId,
        limitTrafficGb: orderDetails.limitTrafficGb,
        duration: orderDetails.duration,
        paymentMethod: selectedPaymentType,
      };

      const result = await createDynamicIpOrder(orderPayload);
      const orderId = result.orderId;
      if (!orderId) {
        toast.error("创建订单失败，请联系客服");
        return;
      }

      const paymentData = await createPayment({
        orderId,
        paymentMethod: selectedPaymentType,
      });

      if (paymentData?.paymentUrl) {
        window.location.href = paymentData.paymentUrl;
      } else {
        toast.error("创建支付失败");
      }
    } catch (error) {
      console.error("订单提交失败:", error);
      toast.error("订单提交失败���请重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowLeft className="w-5 h-5" />
            返回
          </button>
          <h1 className="text-3xl font-bold" style={{ color: "#1A202C" }}>
            完成动态IP订单
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">选择支付方式</h2>
              {paymentMethodsLoading && (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">
                    正在加载支付方式...
                  </span>
                </div>
              )}
              {!paymentMethodsLoading && (
                <div className="space-y-3">
                  {paymentMethods
                    .filter((method) => method.isEnabled)
                    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                    .map((method) => (
                      <label
                        key={method.id}
                        className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedPaymentType === method.id
                            ? "border-purple-500 bg-purple-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="radio"
                            name="paymentMethod"
                            value={method.id}
                            checked={selectedPaymentType === method.id}
                            onChange={(e) =>
                              setSelectedPaymentType(e.target.value)
                            }
                            className="text-purple-600"
                          />
                          <span className="text-2xl">{method.icon}</span>
                          <div>
                            <div className="font-medium">{method.name}</div>
                            {method.description && (
                              <div className="text-sm text-gray-500">
                                {method.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </label>
                    ))}
                  <label
                    className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPaymentType === "balance"
                        ? "border-purple-500 bg-purple-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="balance"
                        checked={selectedPaymentType === "balance"}
                        onChange={(e) =>
                          setSelectedPaymentType(e.target.value as "balance")
                        }
                        className="text-purple-600"
                      />
                      <span className="text-2xl">💰</span>
                      <div>
                        <div className="font-medium">余额支付</div>
                        <div className="text-sm text-gray-500">
                          使用余额余额进行支付
                        </div>
                      </div>
                    </div>
                  </label>
                  {paymentMethods.filter((method) => method.isEnabled)
                    .length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      暂无可用的支付方式，请联系客服
                    </div>
                  )}
                </div>
              )}
            </div>
            {selectedPaymentType === "balance" && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">余额支付</h2>
                <BalancePayment
                  amount={totalPriceCNY}
                  onPaymentSuccess={handleBalancePaymentSuccess}
                  onPaymentError={handleBalancePaymentError}
                  onCreateOrder={createOrderForBalancePayment}
                />
              </div>
            )}
          </div>
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-4">
              <h2 className="text-xl font-semibold mb-4">订单摘要</h2>
              {productLoading && (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">
                    正在加载产品信息...
                  </span>
                </div>
              )}
              {productError && (
                <div className="text-center py-8">
                  <div className="text-red-600 mb-2">加载失败</div>
                  <div className="text-sm text-gray-500">
                    {productError instanceof Error
                      ? productError.message
                      : "未知错误"}
                  </div>
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                  >
                    重新加载
                  </button>
                </div>
              )}
              {!productLoading && !productError && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">
                      {product?.productName || "动态住宅代理"}
                    </h3>
                  </div>
                  <div className="border-t pt-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">单价 (每 GB)</span>
                        <div className="text-right">
                          {productCurrency === "CNY" ? (
                            <>
                              <div className="font-medium">
                                ¥{pricePerUnitCNY.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-500">
                                ≈ ${pricePerUnitUSD.toFixed(2)}
                              </div>
                            </>
                          ) : (
                            <>
                              <div className="font-medium">
                                ${pricePerUnitUSD.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-500">
                                ≈ ¥{pricePerUnitCNY.toFixed(2)}
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">流量</span>
                        <span className="font-medium">
                          {orderDetails.limitTrafficGb} GB
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">有效期</span>
                        <span className="font-medium">
                          {orderDetails.duration} 天
                        </span>
                      </div>
                      <div className="border-t pt-3">
                        {productCurrency === "CNY" ? (
                          <>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-base font-medium">
                                总计 ({productCurrency})
                              </span>
                              <span
                                className="text-xl font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ¥{totalPriceCNY.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-sm text-gray-500">
                                参考价格 (USD)
                              </span>
                              <span className="text-sm text-gray-500">
                                ≈ ${totalPriceUSD.toFixed(2)}
                              </span>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-base font-medium">
                                总计 ({productCurrency})
                              </span>
                              <span
                                className="text-lg font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ${totalPriceUSD.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-base font-medium">
                                应付金额 (CNY)
                              </span>
                              <span
                                className="text-xl font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ¥{totalPriceCNY.toFixed(2)}
                              </span>
                            </div>
                          </>
                        )}
                        {productCurrency !== "CNY" && (
                          <div className="text-xs text-gray-500 text-center bg-gray-50 p-2 rounded">
                            汇率: 1 USD = {exchangeRate.toFixed(4)} CNY
                            (实时更新)
                          </div>
                        )}
                      </div>
                    </div>
                    {selectedPaymentType !== "balance" && (
                      <button
                        className="w-full py-3 mt-6 rounded-lg text-white font-medium transition-opacity disabled:opacity-50"
                        style={{ backgroundColor: "#865DDC" }}
                        onClick={handleOrderSubmit}
                        disabled={
                          !selectedPaymentType ||
                          isSubmitting ||
                          productLoading ||
                          paymentMethodsLoading ||
                          paymentMethods.filter((method) => method.isEnabled)
                            .length === 0
                        }
                      >
                        {isSubmitting
                          ? "处理中..."
                          : productLoading
                          ? "加载中..."
                          : "立即支付"}
                      </button>
                    )}
                    {selectedPaymentType === "balance" && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                        <div className="text-sm text-blue-800">
                          <div className="font-medium mb-2">余额支付说明：</div>
                          <ul className="space-y-1 text-xs">
                            <li>• 支付金额：¥{totalPriceCNY.toFixed(2)}</li>
                            <li>• 请在左侧余额支付区域完成付款</li>
                            <li>• 支付成功后将自动跳转到订单页面</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {showActivationProgress && orderId && (
        <ActivationProgress
          orderId={orderId}
          isOpen={showActivationProgress}
          onClose={() => setShowActivationProgress(false)}
          totalQuantity={1} // For dynamic IP, it's always 1 channel
        />
      )}
    </div>
  );
}

export default DynamicIPCheckout;
