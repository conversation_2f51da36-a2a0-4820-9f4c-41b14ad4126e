import ActivationProgress from "@/components/orders/ActivationProgress";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BalancePayment from "@/components/wallet/BalancePayment";
import { getUsdToCnyRate } from "@/services/exchange-rate";
import { createPayment } from "@/services/payment";
import {
  getPaymentMethods,
  type PaymentMethod,
} from "@/services/payment-methods";
import { getProductById } from "@/services/products";
import { createStaticIpOrder } from "@/services/static-ip-orders";
import { useOrderCreationStore } from "@/store/order-creation";
import { useUserStore } from "@/store/user";
import type { shopProduct } from "@/types/shop-product";
import { getFullLocationDisplay } from "@/utils/location-utils";
import { SupplierLocationUtils } from "@/utils/supplier-location-utils";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import ReactCountryFlag from "react-country-flag";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

function StaticIPCheckout() {
  const navigate = useNavigate();
  const [selectedPaymentType, setSelectedPaymentType] = useState<string>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [showActivationProgress, setShowActivationProgress] = useState(false);
  const userStore = useUserStore();

  // 从 store 获取订单信息
  const {
    id: productId,
    quantity,
    duration,
    hasValidOrderData,
    reset,
  } = useOrderCreationStore();

  // 检查是否有有效的订单数据，如果没有则重定向
  useEffect(() => {
    if (!hasValidOrderData()) {
      toast.error("订单信息缺失，请重新选择产品");
      navigate("/products/static-residential-proxies");
      return;
    }
  }, [hasValidOrderData, navigate]);

  // 通过productId获取产品详情
  const {
    data: product,
    isLoading: productLoading,
    error: productError,
  } = useQuery<shopProduct>({
    queryKey: ["product", productId],
    queryFn: async () => {
      if (!productId) throw new Error("缺少产品ID");

      console.log("正在获取产品ID:", productId);
      const result = await getProductById(productId);
      console.log("API响应结果:", result);

      // 检查API响应结构
      if (!result) {
        throw new Error("API响应为空");
      }

      // 根据实际API响应结构处理数据
      // 检查是否是完整的API响应格式 {code, data, message}
      if (typeof result === "object" && "data" in result && "code" in result) {
        console.log("API响应格式，从data字段返回产品数据:", result.data);
        if (!result.data) {
          throw new Error("产品数据为空");
        }
        return result.data as shopProduct;
      }

      // 如果result直接是产品数据
      if (typeof result === "object" && "id" in result) {
        console.log("直接返回产品数据:", result);
        return result as shopProduct;
      }

      throw new Error("产品数据格式不正确");
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: (failureCount, error) => {
      console.error(`产品获取失败 (尝试 ${failureCount + 1}):`, error);
      return failureCount < 2; // 最多重试2次
    },
  });

  // 获取实时汇率
  const { data: exchangeRate = 7.25 } = useQuery<number>({
    queryKey: ["exchangeRate", "USD", "CNY"],
    queryFn: getUsdToCnyRate,
    staleTime: 30 * 60 * 1000, // 30分钟
    refetchOnWindowFocus: false,
  });

  // 计算价格信息（修复货币处理逻辑）
  const unitPrice = product
    ? parseFloat(product.discountPrice || product.price)
    : 0;
  const totalPrice = unitPrice * quantity;

  // 价格转换逻辑（修复）：
  // 1. 从产品的currency字段获取真实的货币类型
  // 2. 根据产品实际货币进行正确的转换和显示
  const productCurrency = product?.currency || "CNY"; // 默认为CNY，与数据库保持一致

  let pricePerUnitCNY: number;
  let pricePerUnitUSD: number;
  let totalPriceCNY: number;
  let totalPriceUSD: number;

  if (productCurrency === "CNY") {
    // 如果产品已经是CNY定价，直接使用
    pricePerUnitCNY = unitPrice;
    totalPriceCNY = totalPrice;
    // 计算等价的USD价格用于显示
    pricePerUnitUSD = Number((unitPrice / exchangeRate).toFixed(2));
    totalPriceUSD = Number((totalPrice / exchangeRate).toFixed(2));
  } else if (productCurrency === "USD") {
    // 如果是USD定价，进行转换
    pricePerUnitUSD = unitPrice;
    totalPriceUSD = totalPrice;
    // 计算CNY价格用于支付
    pricePerUnitCNY = Number((unitPrice * exchangeRate).toFixed(2));
    totalPriceCNY = Number((totalPrice * exchangeRate).toFixed(2));
  } else {
    // 不支持的货币类型，按CNY处理
    pricePerUnitCNY = unitPrice;
    totalPriceCNY = totalPrice;
    pricePerUnitUSD = Number((unitPrice / exchangeRate).toFixed(2));
    totalPriceUSD = Number((totalPrice / exchangeRate).toFixed(2));
  }

  // 订单详情（用于显示和API调用）
  const orderDetails = {
    productId: productId || "", // API仍然使用productId字段名
    // 使用工具类安全获取城市名称（用于显示）
    cityName: SupplierLocationUtils.getCityNameFromProduct(product),
    // 使用工具类安全获取国家代码（用于显示）
    countryCode: SupplierLocationUtils.getCountryCodeFromProduct(product),
    quantity,
    duration,
  };

  // 获取支付方式列表
  const { data: paymentMethods = [], isLoading: paymentMethodsLoading } =
    useQuery<PaymentMethod[]>({
      queryKey: ["paymentMethods"],
      queryFn: getPaymentMethods,
      staleTime: 10 * 60 * 1000, // 10分钟缓存
      refetchOnWindowFocus: false,
    });

  // 余额支付回调函数
  const handleBalancePaymentSuccess = (paidOrderId?: string) => {
    toast.success("余额支付成功！");
    if (paidOrderId) {
      setOrderId(paidOrderId);
      setShowActivationProgress(true);
    }
  };

  const handleBalancePaymentError = (error: string) => {
    toast.error(`余额支付失败：${error}`);
    setIsSubmitting(false);
  };

  const createOrderForBalancePayment = async () => {
    const orderPayload = {
      id: orderDetails.productId,
      quantity: orderDetails.quantity,
      duration: orderDetails.duration,
      paymentMethod: "balance",
    };

    const result = await createStaticIpOrder(orderPayload);
    const createdOrderId = result.orderId;
    setOrderId(createdOrderId); // 保存订单ID用于进度追踪
    return createdOrderId;
  };

  const handleOrderSubmit = async () => {
    setIsSubmitting(true);
    try {
      if (!selectedPaymentType) {
        toast.error("请选择支付方式");
        setIsSubmitting(false);
        return;
      }

      // 如果选择了余额支付，则不需要创建订单，直接显示余额支付组件
      if (selectedPaymentType === "balance") {
        toast.error("请使用余额支付按钮完成支付");
        setIsSubmitting(false);
        return;
      }

      // 创建静态IP订单的请求数据
      const orderPayload = {
        id: orderDetails.productId,
        quantity: orderDetails.quantity,
        duration: orderDetails.duration,
        paymentMethod: selectedPaymentType,
      };

      // 创建订单
      const result = await createStaticIpOrder(orderPayload);
      console.log("订单创建响应:", result);

      const orderId = result.orderId;
      if (!orderId) {
        console.error("无法获取订单ID:", result);
        toast.error("创建订单失败，请联系客服");
        return;
      }

      // 在线支付流程
      const paymentData = await createPayment({
        orderId,
        paymentMethod: selectedPaymentType,
      });

      if (paymentData?.paymentUrl) {
        // 跳转到支付页面
        window.location.href = paymentData.paymentUrl;
      } else {
        toast.error("创建支付失败");
      }
    } catch (error) {
      console.error("订单提交失败:", error);
      toast.error("订单提交失败，请重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowLeft className="w-5 h-5" />
            返回
          </button>
          <h1 className="text-3xl font-bold" style={{ color: "#1A202C" }}>
            完成订单
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：支付方式选择 */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">选择支付方式</h2>

              {/* 支付方式加载状态 */}
              {paymentMethodsLoading && (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">
                    正在加载支付方式...
                  </span>
                </div>
              )}

              {/* 支付方式列表 */}
              {!paymentMethodsLoading && (
                <div className="space-y-3">
                  {paymentMethods
                    .filter((method) => method.isEnabled)
                    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                    .map((method) => (
                      <label
                        key={method.id}
                        className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedPaymentType === method.id
                            ? "border-purple-500 bg-purple-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="radio"
                            name="paymentMethod"
                            value={method.id}
                            checked={selectedPaymentType === method.id}
                            onChange={(e) =>
                              setSelectedPaymentType(e.target.value)
                            }
                            className="text-purple-600"
                          />
                          <span className="text-2xl">{method.icon}</span>
                          <div>
                            <div className="font-medium">{method.name}</div>
                            {method.description && (
                              <div className="text-sm text-gray-500">
                                {method.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </label>
                    ))}

                  {/* 余额支付选项 */}
                  <label
                    className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPaymentType === "balance"
                        ? "border-purple-500 bg-purple-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="balance"
                        checked={selectedPaymentType === "balance"}
                        onChange={(e) =>
                          setSelectedPaymentType(e.target.value as "balance")
                        }
                        className="text-purple-600"
                      />
                      <span className="text-2xl">💰</span>
                      <div>
                        <div className="font-medium">余额支付</div>
                        <div className="text-sm text-gray-500">
                          使用余额余额进行支付
                        </div>
                      </div>
                    </div>
                  </label>

                  {/* 无可用支付方式提示 */}
                  {paymentMethods.filter((method) => method.isEnabled)
                    .length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      暂无可用的支付方式，请联系客服
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 余额支付组件 */}
            {selectedPaymentType === "balance" && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">余额支付</h2>
                <BalancePayment
                  amount={totalPriceCNY}
                  onPaymentSuccess={handleBalancePaymentSuccess}
                  onPaymentError={handleBalancePaymentError}
                  onCreateOrder={createOrderForBalancePayment}
                />
              </div>
            )}
          </div>

          {/* 右侧：订单摘要 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-4">
              <h2 className="text-xl font-semibold mb-4">订单摘要</h2>

              {/* 加载状态 */}
              {productLoading && (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">
                    正在加载产品信息...
                  </span>
                </div>
              )}

              {/* 错误状态 */}
              {productError && (
                <div className="text-center py-8">
                  <div className="text-red-600 mb-2">加载失败</div>
                  <div className="text-sm text-gray-500">
                    {productError instanceof Error
                      ? productError.message
                      : "未知错误"}
                  </div>
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                  >
                    重新加载
                  </button>
                </div>
              )}

              {/* 产品信息 */}
              {!productLoading && !productError && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">
                      {product?.productName || "静态住宅代理"}
                    </h3>
                    <div className="text-sm text-gray-600 space-y-2">
                      <div className="flex items-center gap-2">
                        {orderDetails.countryCode && (
                          <ReactCountryFlag
                            countryCode={orderDetails.countryCode}
                            svg
                            style={{
                              width: "20px",
                              height: "15px",
                              borderRadius: "2px",
                            }}
                          />
                        )}
                        <span className="font-medium">
                          {orderDetails.cityName}
                          {orderDetails.countryCode &&
                            ` (${orderDetails.countryCode})`}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 mt-2">
                        {product
                          ? getFullLocationDisplay(product)
                          : "加载中..."}
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="space-y-3">
                      {/* 单价显示（修复货币显示） */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">单价</span>
                        <div className="text-right">
                          {productCurrency === "CNY" ? (
                            // CNY产品：主要显示CNY，参考显示USD
                            <>
                              <div className="font-medium">
                                ¥{pricePerUnitCNY.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-500">
                                ≈ ${pricePerUnitUSD.toFixed(2)}
                              </div>
                            </>
                          ) : (
                            // USD产品：主要显示USD，参考显示CNY
                            <>
                              <div className="font-medium">
                                ${pricePerUnitUSD.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-500">
                                ≈ ¥{pricePerUnitCNY.toFixed(2)}
                              </div>
                            </>
                          )}
                        </div>
                      </div>

                      {/* 数量显示 */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">数量</span>
                        <span className="font-medium">
                          {orderDetails.quantity} 个
                        </span>
                      </div>

                      {/* 有效期显示 */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">有效期</span>
                        <span className="font-medium">
                          {orderDetails.duration} 天
                        </span>
                      </div>

                      {/* 分隔线 */}
                      <div className="border-t pt-3">
                        {productCurrency === "CNY" ? (
                          // CNY产品：主要显示CNY，参考显示USD
                          <>
                            {/* CNY总计（主要显示） */}
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-base font-medium">
                                总计 ({productCurrency})
                              </span>
                              <span
                                className="text-xl font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ¥{totalPriceCNY.toFixed(2)}
                              </span>
                            </div>

                            {/* USD参考价格 */}
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-sm text-gray-500">
                                参考价格 (USD)
                              </span>
                              <span className="text-sm text-gray-500">
                                ≈ ${totalPriceUSD.toFixed(2)}
                              </span>
                            </div>
                          </>
                        ) : (
                          // USD产品：主要显示USD，应付显示CNY
                          <>
                            {/* USD总计（显示给用户看） */}
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-base font-medium">
                                总计 ({productCurrency})
                              </span>
                              <span
                                className="text-lg font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ${totalPriceUSD.toFixed(2)}
                              </span>
                            </div>

                            {/* 应付金额（实际支付的CNY金额） */}
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-base font-medium">
                                应付金额 (CNY)
                              </span>
                              <span
                                className="text-xl font-bold"
                                style={{ color: "#865DDC" }}
                              >
                                ¥{totalPriceCNY.toFixed(2)}
                              </span>
                            </div>
                          </>
                        )}

                        {/* 汇率信息（仅在需要转换时显示） */}
                        {productCurrency !== "CNY" && (
                          <div className="text-xs text-gray-500 text-center bg-gray-50 p-2 rounded">
                            汇率: 1 USD = {exchangeRate.toFixed(4)} CNY
                            (实时更新)
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 支付按钮 */}
                    {selectedPaymentType !== "balance" && (
                      <button
                        className="w-full py-3 mt-6 rounded-lg text-white font-medium transition-opacity disabled:opacity-50"
                        style={{ backgroundColor: "#865DDC" }}
                        onClick={handleOrderSubmit}
                        disabled={
                          !selectedPaymentType ||
                          isSubmitting ||
                          productLoading ||
                          paymentMethodsLoading ||
                          paymentMethods.filter((method) => method.isEnabled)
                            .length === 0
                        }
                      >
                        {isSubmitting
                          ? "处理中..."
                          : productLoading
                          ? "加载中..."
                          : "立即支付"}
                      </button>
                    )}

                    {/* 余额支付说明 */}
                    {selectedPaymentType === "balance" && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                        <div className="text-sm text-blue-800">
                          <div className="font-medium mb-2">余额支付说明：</div>
                          <ul className="space-y-1 text-xs">
                            <li>• 支付金额：¥{totalPriceCNY.toFixed(2)}</li>
                            <li>• 请在左侧余额支付区域完成付款</li>
                            <li>• 支付成功后将自动跳转到订单页面</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 开通进度模态框 */}
      {showActivationProgress && orderId && (
        <ActivationProgress
          orderId={orderId}
          isOpen={showActivationProgress}
          onClose={() => setShowActivationProgress(false)}
          totalQuantity={quantity}
        />
      )}
    </div>
  );
}

export default StaticIPCheckout;
