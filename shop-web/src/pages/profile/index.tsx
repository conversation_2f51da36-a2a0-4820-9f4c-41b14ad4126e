import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useGetProfile } from "@/services/profile";
import { useUserStore } from "@/store/user";
import { Lock, RefreshCw, User } from "lucide-react";
import { useState } from "react";
import PasswordForm from "./components/PasswordForm";
import ProfileForm from "./components/ProfileForm";

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("profile");
  const { user } = useUserStore();
  const { data: profileData, isLoading, refetch } = useGetProfile();

  // 使用从API获取的数据，如果没有则使用store中的数据
  const currentUser = profileData || user;

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className="container max-w-4xl mx-auto p-4 md:p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2 text-muted-foreground">
            <RefreshCw className="h-4 w-4 animate-spin" />
            加载中...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto p-4 md:p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-3">
          <User className="h-8 w-8 text-[#865DDC]" />
          <div>
            <h1 className="text-2xl font-bold text-foreground">个人中心</h1>
            <p className="text-muted-foreground">管理您的个人信息和账户设置</p>
          </div>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* 用户信息卡片 */}
      <Card className="mb-6">
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            {/* <AvatarUpload
              currentAvatar={currentUser?.avatar}
              onAvatarChange={(avatar) => console.log("Avatar changed:", avatar)}
            /> */}
            <div className="space-y-1">
              <CardTitle className="text-xl">
                {currentUser?.nickname || "未设置昵称"}
              </CardTitle>
              <p className="text-muted-foreground">{currentUser?.email}</p>
              {currentUser?.mobile && (
                <p className="text-sm text-muted-foreground">
                  手机: {currentUser.mobile}
                </p>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 标签页内容 */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">个人信息</span>
            <span className="sm:hidden">信息</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span className="hidden sm:inline">安全设置</span>
            <span className="sm:hidden">安全</span>
          </TabsTrigger>
        </TabsList>

        {/* 个人信息标签页 */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>个人信息</CardTitle>
              <p className="text-sm text-muted-foreground">
                更新您的个人资料信息
              </p>
            </CardHeader>
            <CardContent>
              <ProfileForm currentUser={currentUser} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 安全设置标签页 */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>修改密码</CardTitle>
              <p className="text-sm text-muted-foreground">
                确保您的账户安全，定期更换密码
              </p>
            </CardHeader>
            <CardContent>
              <PasswordForm />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
