import { Button } from "@/components/ui/button";
import { Camera, User } from "lucide-react";
import { useState, useRef } from "react";
import { toast } from "sonner";

interface AvatarUploadProps {
  currentAvatar?: string;
  onAvatarChange: (avatar: string) => void;
}

export default function AvatarUpload({ currentAvatar, onAvatarChange }: AvatarUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      toast.error("请选择图片文件");
      return;
    }

    // 验证文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error("图片大小不能超过2MB");
      return;
    }

    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // 模拟上传 - 实际项目中应该上传到服务器
    setIsUploading(true);
    setTimeout(() => {
      setIsUploading(false);
      onAvatarChange(url);
      toast.success("头像更新成功");
    }, 1500);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const displayAvatar = previewUrl || currentAvatar;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* 头像显示区域 */}
      <div className="relative">
        <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full overflow-hidden bg-muted border-2 border-border">
          {displayAvatar ? (
            <img
              src={displayAvatar}
              alt="用户头像"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <User className="w-8 h-8 sm:w-10 sm:h-10 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* 上传按钮覆盖层 */}
        <Button
          type="button"
          size="sm"
          variant="secondary"
          disabled={isUploading}
          onClick={handleUploadClick}
          className="absolute bottom-0 right-0 rounded-full p-2 shadow-lg"
        >
          <Camera className="w-3 h-3" />
        </Button>
      </div>

      {/* 上传状态文本 */}
      <div className="text-center">
        {isUploading ? (
          <p className="text-sm text-muted-foreground">上传中...</p>
        ) : (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleUploadClick}
            className="text-xs"
          >
            更换头像
          </Button>
        )}
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 提示文本 */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground">
          支持 JPG、PNG 格式
        </p>
        <p className="text-xs text-muted-foreground">
          文件大小不超过 2MB
        </p>
      </div>
    </div>
  );
}