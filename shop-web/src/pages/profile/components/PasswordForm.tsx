import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useChangePassword } from "@/services/profile";
import { useState } from "react";
import { Eye, EyeOff, Shield } from "lucide-react";
import { toast } from "sonner";

const passwordSchema = z.object({
  oldPassword: z.string().min(1, "请输入当前密码"),
  newPassword: z.string()
    .min(6, "新密码至少需要6个字符")
    .max(20, "新密码不能超过20个字符")
    .regex(/^(?=.*[a-zA-Z])(?=.*\d)/, "新密码必须包含字母和数字"),
  confirmPassword: z.string().min(1, "请确认新密码"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "两次输入的密码不一致",
  path: ["confirmPassword"],
});

type PasswordFormData = z.infer<typeof passwordSchema>;

export default function PasswordForm() {
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const changePasswordMutation = useChangePassword();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const newPassword = watch("newPassword");

  const onSubmit = async (data: PasswordFormData) => {
    try {
      await changePasswordMutation.mutateAsync({
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
      });
      
      reset();
      toast.success("密码修改成功");
    } catch (error: any) {
      console.error("密码修改失败:", error);
      const message = error.response?.data?.message || "密码修改失败，请重试";
      toast.error(message);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return { score: 0, text: "", color: "" };
    
    let score = 0;
    if (password.length >= 6) score += 1;
    if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

    if (score <= 2) return { score, text: "弱", color: "text-red-500" };
    if (score <= 4) return { score, text: "中等", color: "text-yellow-500" };
    return { score, text: "强", color: "text-green-500" };
  };

  const passwordStrength = getPasswordStrength(newPassword);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        {/* 当前密码 */}
        <div className="space-y-2">
          <Label htmlFor="oldPassword">当前密码 *</Label>
          <div className="relative">
            <Input
              id="oldPassword"
              type={showOldPassword ? "text" : "password"}
              placeholder="请输入当前密码"
              {...register("oldPassword")}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowOldPassword(!showOldPassword)}
            >
              {showOldPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.oldPassword && (
            <p className="text-sm text-destructive">{errors.oldPassword.message}</p>
          )}
        </div>

        {/* 新密码 */}
        <div className="space-y-2">
          <Label htmlFor="newPassword">新密码 *</Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              placeholder="请输入新密码"
              {...register("newPassword")}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {newPassword && (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">密码强度:</span>
              <span className={passwordStrength.color}>{passwordStrength.text}</span>
              <div className="flex gap-1">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 w-4 rounded-full ${
                      i < passwordStrength.score 
                        ? passwordStrength.score <= 2 
                          ? "bg-red-500" 
                          : passwordStrength.score <= 4 
                          ? "bg-yellow-500" 
                          : "bg-green-500"
                        : "bg-gray-200"
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
          {errors.newPassword && (
            <p className="text-sm text-destructive">{errors.newPassword.message}</p>
          )}
        </div>

        {/* 确认新密码 */}
        <div className="space-y-2">
          <Label htmlFor="confirmPassword">确认新密码 *</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="请再次输入新密码"
              {...register("confirmPassword")}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-destructive">{errors.confirmPassword.message}</p>
          )}
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <Button
          type="submit"
          disabled={changePasswordMutation.isPending}
          className="bg-[#865DDC] hover:bg-[#7a52c7]"
        >
          <Shield className="h-4 w-4 mr-2" />
          {changePasswordMutation.isPending ? "修改中..." : "修改密码"}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => reset()}
          disabled={changePasswordMutation.isPending}
        >
          重置表单
        </Button>
      </div>

      {/* 密码要求提示 */}
      <div className="bg-muted/50 rounded-lg p-4 space-y-2">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Shield className="h-4 w-4" />
          密码安全要求
        </h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• 长度为6-20个字符</li>
          <li>• 必须包含字母和数字</li>
          <li>• 建议使用大小写字母、数字和特殊字符的组合</li>
          <li>• 避免使用个人信息作为密码</li>
        </ul>
      </div>
    </form>
  );
}