import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useUpdateProfile } from "@/services/profile";
import { useUserStore } from "@/store/user";
import type { User } from "@/store/user";
import { useState } from "react";
import { toast } from "sonner";

const profileSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  nickname: z.string().min(1, "昵称不能为空").max(50, "昵称不能超过50个字符"),
  phone: z.string().optional().refine((val) => {
    if (!val) return true;
    return /^1[3-9]\d{9}$/.test(val);
  }, "请输入有效的手机号码"),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface ProfileFormProps {
  currentUser: User | null;
}

export default function ProfileForm({ currentUser }: ProfileFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const updateProfileMutation = useUpdateProfile();
  const { setUser } = useUserStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      email: currentUser?.email || "",
      nickname: currentUser?.nickname || "",
      phone: currentUser?.mobile || "",
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    try {
      const updateData = {
        email: data.email,
        nickname: data.nickname,
        phone: data.phone || undefined,
      };

      const result = await updateProfileMutation.mutateAsync(updateData);
      
      if (result) {
        // 更新本地用户状态
        setUser({ ...currentUser, ...result });
        setIsEditing(false);
      }
    } catch (error: any) {
      console.error("更新个人信息失败:", error);
      const message = error.response?.data?.message || "更新失败，请重试";
      toast.error(message);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 邮箱 */}
        <div className="space-y-2">
          <Label htmlFor="email">邮箱地址 *</Label>
          <Input
            id="email"
            type="email"
            disabled={!isEditing}
            className={!isEditing ? "bg-muted" : ""}
            {...register("email")}
          />
          {errors.email && (
            <p className="text-sm text-destructive">{errors.email.message}</p>
          )}
        </div>

        {/* 昵称 */}
        <div className="space-y-2">
          <Label htmlFor="nickname">昵称 *</Label>
          <Input
            id="nickname"
            disabled={!isEditing}
            className={!isEditing ? "bg-muted" : ""}
            placeholder="请输入昵称"
            {...register("nickname")}
          />
          {errors.nickname && (
            <p className="text-sm text-destructive">{errors.nickname.message}</p>
          )}
        </div>

        {/* 手机号 */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="phone">手机号</Label>
          <Input
            id="phone"
            disabled={!isEditing}
            className={!isEditing ? "bg-muted" : ""}
            placeholder="请输入手机号码（可选）"
            {...register("phone")}
          />
          {errors.phone && (
            <p className="text-sm text-destructive">{errors.phone.message}</p>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        {!isEditing ? (
          <Button 
            type="button" 
            onClick={handleEdit}
            className="bg-[#865DDC] hover:bg-[#7a52c7]"
          >
            编辑信息
          </Button>
        ) : (
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              type="submit"
              disabled={!isDirty || updateProfileMutation.isPending}
              className="bg-[#865DDC] hover:bg-[#7a52c7]"
            >
              {updateProfileMutation.isPending ? "保存中..." : "保存更改"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={updateProfileMutation.isPending}
            >
              取消
            </Button>
          </div>
        )}
      </div>

      <div className="text-sm text-muted-foreground space-y-1">
        <p>• 邮箱地址是您的登录凭证</p>
        <p>• 昵称将在系统中显示</p>
        <p>• 手机号用于安全验证（可选）</p>
      </div>
    </form>
  );
}