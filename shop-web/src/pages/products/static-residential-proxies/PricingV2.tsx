import Cart from "@/components/products/Cart-v2";
import Layout from "@/components/products/Layout";
import ProxyTypeSelector, {
  proxyTypes,
} from "@/components/products/ProxyTypeSelector";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  useProductFilters,
  useStandardizedProducts,
} from "@/hooks/useStandardizedProducts";
import { useOrderCreationStore } from "@/store/order-creation";
import { useProductPageStore } from "@/store/productStore";
import {
  ChevronDown,
  ChevronUp,
  DollarSign,
  Filter,
  MapPin,
  ShoppingCart,
  SortAsc,
  SortDesc,
} from "lucide-react";
import { useEffect, useState } from "react";
import ReactCountryFlag from "react-country-flag";
import { useNavigate } from "react-router-dom";

// 导入工具函数
import {
  getCountryCode,
  getCountryName,
  getRegionName,
} from "@/utils/location-utils";
import { getPriceLabel, hasValidPrice } from "@/utils/price-utils";
import { DynamicIPPurchase } from "../dynamic-residential-proxies/DynamicIPPurchase";

// 滚动位置检测Hook
const useScrollPosition = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return scrollY;
};

// 移动端购物车组件 - 完整功能的紧凑版本
const MobileCart = ({
  selectedProduct,
  quantity = 1,
  isExpanded,
  onToggleExpand,
  isSticky = false,
}) => {
  const { count, setCount, duration, selectedProxyType } =
    useProductPageStore();
  const navigate = useNavigate();
  const setOrderDetails = useOrderCreationStore(
    (state) => state.setOrderDetails
  );

  if (!selectedProduct) {
    return (
      <div
        className={`${
          isSticky ? "fixed top-[60px]" : "relative"
        } left-0 right-0 bg-white border-b shadow-sm z-10 lg:hidden`}
      >
        <div className="flex items-center justify-center p-3">
          <div className="flex items-center space-x-2 text-gray-500">
            <ShoppingCart className="w-4 h-4" />
            <span className="text-sm">请选择代理产品</span>
          </div>
        </div>
      </div>
    );
  }

  const priceInfo = getPriceLabel(selectedProduct);
  const countryName = getCountryName(selectedProduct);
  const countryCode = getCountryCode(selectedProduct);
  const unitPrice = parseFloat(priceInfo.mainPrice.replace(/[^0-9.]/g, ""));
  const total = unitPrice * count;

  // 处理数量变更
  const handleQuantityChange = (newQuantity: number) => {
    const minQty = selectedProduct.minQuantity || 1;
    const maxQty = Math.min(
      selectedProduct.maxQuantity || 1000,
      selectedProduct.availableCount || 1000
    );

    if (newQuantity >= minQty && newQuantity <= maxQty) {
      setCount(newQuantity);
    }
  };

  // 处理结账按钮点击
  const handleCheckout = () => {
    if (!selectedProduct || !selectedProxyType) {
      return;
    }

    // 设置订单详情到 store
    console.log("PricingV2: selectedProduct for checkout:", selectedProduct);
    console.log(
      "PricingV2: limitTrafficGb for checkout:",
      selectedProduct.limitTrafficGb
    );
    setOrderDetails({
      id: selectedProduct.id.toString(),
      quantity: count,
      duration: duration,
      limitTrafficGb: selectedProduct.limitTrafficGb ?? 1, // 添加 limitTrafficGb，如果产品没有则默认为1
    });

    // 直接跳转到结账页面
    navigate("/orders/static-ip-checkout");
  };

  if (!isExpanded) {
    // 收起状态 - 显示关键信息
    return (
      <div
        className={`${
          isSticky ? "fixed top-[60px]" : "relative"
        } left-0 right-0 bg-white border-b shadow-md z-50 lg:hidden`}
      >
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <ShoppingCart className="w-4 h-4 text-purple-600 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm lg:text-base font-medium text-gray-900 truncate">
                {countryName} - {selectedProduct.cityName}
              </div>
              <div className="text-xs text-gray-500">
                数量: {count} • 总计: ¥{total.toFixed(2)}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 flex-shrink-0">
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 text-xs sm:text-sm lg:text-base"
              onClick={handleCheckout}
              disabled={selectedProduct.availableCount === 0}
            >
              结账
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleExpand}
              className="px-2"
            >
              <ChevronDown className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // 展开状态 - 显示完整信息
  return (
    <div
      className={`${
        isSticky ? "fixed top-0" : "relative"
      } left-0 right-0 bg-white border-b shadow-lg z-50 lg:hidden`}
    >
      <div className="p-4 space-y-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ShoppingCart className="w-5 h-5 text-purple-600" />
            <span className="font-semibold text-gray-900">订单汇总</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpand}
            className="px-2"
          >
            <ChevronUp className="w-4 h-4" />
          </Button>
        </div>

        {/* 产品信息卡片 */}
        <div className="bg-gray-50 rounded-lg p-3 space-y-3">
          {/* 产品标题 */}
          <div className="flex items-center space-x-3">
            {countryCode && countryCode.length === 2 && (
              <ReactCountryFlag
                countryCode={countryCode}
                svg
                style={{
                  width: "20px",
                  height: "15px",
                  borderRadius: "3px",
                }}
                title={countryCode}
              />
            )}
            <div className="flex-1">
              <div className="font-medium text-gray-900 text-sm">
                {countryName} - {selectedProduct.cityName}
              </div>
              <div className="text-xs text-gray-600">静态住宅代理</div>
            </div>
          </div>

          {/* 产品详细信息 */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="space-y-1">
              <div className="text-gray-600">单价</div>
              <div className="font-semibold text-purple-600">
                {priceInfo.mainPrice}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-600">有效期</div>
              <div className="font-medium">
                {selectedProduct.validityPeriod} 天
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-600">可用数量</div>
              <div
                className={`font-medium ${
                  selectedProduct.availableCount === 0
                    ? "text-red-600"
                    : selectedProduct.availableCount < 100
                    ? "text-orange-600"
                    : "text-green-600"
                }`}
              >
                {selectedProduct.availableCount === 0
                  ? "缺货"
                  : `${selectedProduct.availableCount} 个`}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-600">已选数量</div>
              <div className="font-medium">{count}</div>
            </div>
          </div>

          {/* 数量调节器 */}
          <div className="bg-white rounded-md p-3 border">
            <div className="text-sm font-medium text-gray-700 mb-2">
              调整数量
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuantityChange(count - 1)}
                disabled={count <= (selectedProduct.minQuantity || 1)}
                className="w-8 h-8 p-0"
              >
                -
              </Button>
              <div className="flex-1">
                <input
                  type="number"
                  value={count}
                  onChange={(e) =>
                    handleQuantityChange(parseInt(e.target.value) || 1)
                  }
                  min={selectedProduct.minQuantity || 1}
                  max={Math.min(
                    selectedProduct.maxQuantity || 1000,
                    selectedProduct.availableCount || 1000
                  )}
                  className="w-full text-center text-sm border rounded-md px-2 py-1"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuantityChange(count + 1)}
                disabled={
                  count >=
                  Math.min(
                    selectedProduct.maxQuantity || 1000,
                    selectedProduct.availableCount || 1000
                  )
                }
                className="w-8 h-8 p-0"
              >
                +
              </Button>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              最小: {selectedProduct.minQuantity || 1}， 最大:{" "}
              {Math.min(
                selectedProduct.maxQuantity || 1000,
                selectedProduct.availableCount || 1000
              )}
            </div>
          </div>

          {/* 折扣信息 */}
          {priceInfo.hasDiscount && priceInfo.discountPercentage && (
            <div className="flex items-center justify-between bg-red-50 border border-red-200 rounded-md p-2">
              <span className="text-sm text-red-700">限时优惠</span>
              <Badge variant="destructive" className="text-xs">
                -{priceInfo.discountPercentage}% 折扣
              </Badge>
            </div>
          )}
        </div>

        {/* 价格汇总 */}
        <div className="space-y-2 border-t pt-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">每个IP价格:</span>
            <span className="font-medium">{priceInfo.mainPrice}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">数量:</span>
            <span className="font-medium">{count}</span>
          </div>
          <div className="flex justify-between text-lg font-bold border-t pt-2">
            <span className="text-gray-900">总计:</span>
            <span className="text-purple-600">¥{total.toFixed(2)}</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="flex-1 px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 text-xs sm:text-sm lg:text-base"
            onClick={onToggleExpand}
          >
            收起购物车
          </Button>
          <Button
            className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 text-xs sm:text-sm lg:text-base"
            disabled={selectedProduct.availableCount === 0}
            onClick={handleCheckout}
          >
            立即结账
          </Button>
        </div>

        {/* 提示信息 */}
        <div className="text-xs text-gray-500 text-center bg-blue-50 rounded-md p-2">
          💡 建议使用7天周期，适合静态住宅IP的算法需求
        </div>
      </div>
    </div>
  );
};

// 优化的Tab组件，支持横向滚动
const ResponsiveTabs = ({
  continents,
  selectedContinentId,
  onContinentSelect,
}) => {
  return (
    <div className="w-full">
      {/* 移动端：横向滚动的Tab */}
      <div className="block sm:hidden">
        <div className="flex overflow-x-auto pb-2 -mx-4 px-4">
          <div className="flex space-x-2 flex-nowrap">
            {continents.map((continent) => (
              <button
                key={continent.id}
                onClick={() => onContinentSelect(continent.id)}
                className={`px-3 py-2 rounded-md text-xs sm:text-sm lg:text-base font-medium whitespace-nowrap flex-shrink-0 transition-colors ${
                  selectedContinentId === continent.id
                    ? "bg-purple-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {continent.displayName === "所有位置"
                  ? "全部"
                  : continent.displayName === "北美洲"
                  ? "北美"
                  : continent.displayName === "南美洲"
                  ? "南美"
                  : continent.displayName}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 桌面端：原有的Tabs组件 */}
      <div className="hidden sm:block">
        <Tabs value={selectedContinentId} onValueChange={onContinentSelect}>
          <TabsList className="grid w-full grid-cols-3 sm:grid-cols-5 gap-1">
            {continents.map((continent) => (
              <TabsTrigger
                key={continent.id}
                value={continent.id}
                className="text-xs sm:text-sm lg:text-base px-2 py-1.5"
              >
                {continent.displayName}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
    </div>
  );
};

/**
 * 新版静态住宅代理定价页面 - 移动端优化版本
 */
const StaticResidentialProxiesPricingV2 = () => {
  const [selectedContinentMatcher, setSelectedContinentMatcher] = useState<
    string | null
  >(null);
  const [selectedContinentId, setSelectedContinentId] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [showFullCart, setShowFullCart] = useState(true);
  const [mobileCartExpanded, setMobileCartExpanded] = useState(false);

  const scrollY = useScrollPosition();
  const shouldStickyCart = scrollY > 100; // 滚动超过100px时固定购物车

  const {
    selectedProxyType,
    setSelectedProxyType,
    selectedLocationId,
    setSelectedLocationId,
    selectedId,
    setSelectedId,
    count,
    setCount,
    udpStatus,
    setUdpStatus,
    randomPassword,
    setRandomPassword,
  } = useProductPageStore();

  // 使用产品筛选器
  const { filters, updateFilter, clearFilters } = useProductFilters();

  // 获取标准化产品
  const { products, total, loading, error, updateQuery, query } =
    useStandardizedProducts({
      initialQuery: {
        proxyType: selectedProxyType?.id || "Shared (ISP) proxies",
        sortBy: "price",
        sortOrder: "asc",
        ...filters,
      },
      enabled: true,
    });

  // 获取当前选中的产品
  const selectedProduct = products.find((p) => p.id === selectedId);

  // 处理代理类型变更
  const handleProxyTypeChange = (proxyType: string) => {
    // 根据字符串ID找到对应的代理类型对象
    const proxyTypeObj = proxyTypes.find((type) => type.id === proxyType);
    if (proxyTypeObj) {
      setSelectedProxyType(proxyTypeObj);
    }

    setSelectedLocationId(null);
    setSelectedId(null);
  };

  // 自动选择默认代理类型
  useEffect(() => {
    if (!selectedProxyType) {
      const defaultType =
        proxyTypes.find((type) => type.recommended) || proxyTypes[0];
      if (defaultType) {
        setSelectedProxyType(defaultType);
      }
    }
  }, []);

  // 当selectedProxyType变化时，更新查询参数
  useEffect(() => {
    if (selectedProxyType) {
      updateQuery({
        proxyType: selectedProxyType.id,
      });
    }
  }, [selectedProxyType, updateQuery]);

  // 当代理类型改变时，重置选中的大洲和位置
  useEffect(() => {
    setSelectedContinentId("all");
    setSelectedContinentMatcher(null);
    setSelectedLocationId(null);
  }, [selectedProxyType, setSelectedLocationId]);

  // 大洲配置
  const continents = [
    { id: "all", displayName: "所有位置", matcherName: null },
    { id: "na", displayName: "北美洲", matcherName: "北美洲" },
    { id: "as", displayName: "亚洲", matcherName: "亚洲" },
    { id: "eu", displayName: "欧洲", matcherName: "欧洲" },
    { id: "sa", displayName: "南美洲", matcherName: "南美洲" },
  ];

  // 根据选中的大洲筛选产品
  const filteredProducts = products.filter((product) => {
    if (selectedContinentMatcher === null) return true;

    const regionFromProduct = getRegionName(product);
    return regionFromProduct === selectedContinentMatcher;
  });

  // 扁平化产品列表
  const flatList = filteredProducts.map((product) => ({
    ...product,
    type: "product" as const,
    listId: `product-${product.id}`,
  }));

  // 处理产品选择
  const handleProductSelect = (product: any) => {
    setSelectedId(product.id);
    setSelectedLocationId(
      product.standardLocationCityId
        ? String(product.standardLocationCityId)
        : null
    );
  };

  // 处理大洲选择
  const handleContinentSelect = (continentId: string) => {
    setSelectedContinentId(continentId);
    const continent = continents.find((c) => c.id === continentId);
    setSelectedContinentMatcher(continent?.matcherName ?? null);

    // 重置位置和产品选择
    setSelectedLocationId(null);
    setSelectedId(null);
  };

  // 处理排序
  const handleSort = (sortBy: "price" | "name" | "location") => {
    const currentOrder = query.sortOrder || "asc";
    const newOrder =
      query.sortBy === sortBy && currentOrder === "asc" ? "desc" : "asc";

    updateQuery({
      sortBy,
      sortOrder: newOrder,
    });
  };

  // 处理移动端购物车展开/收起
  const handleToggleMobileCart = () => {
    setMobileCartExpanded(!mobileCartExpanded);
  };

  return (
    <Layout>
      {/* 移动端购物车 - 始终显示 */}
      <MobileCart
        selectedProduct={selectedProduct}
        quantity={count}
        isExpanded={mobileCartExpanded}
        onToggleExpand={handleToggleMobileCart}
        isSticky={shouldStickyCart}
      />

      <div
        className={`flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8 px-4 sm:px-6 lg:px-8 ${
          shouldStickyCart
            ? mobileCartExpanded
              ? "pt-64"
              : "pt-2"
            : mobileCartExpanded
            ? "pt-48"
            : "pt-2"
        } lg:pt-0`}
      >
        {/* 左侧 - 产品选择区域 */}
        <div className="flex-1 space-y-3 md:space-y-4">
          {/* 代理类型选择器 - 移动端两列布局 */}
          <div className="bg-gray-50 p-3 sm:p-4 lg:p-6 rounded-lg">
            <h2 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800 mb-3">
              选择代理类型
            </h2>

            {/* 移动端：两列网格布局 */}
            <div className="block sm:hidden">
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => handleProxyTypeChange("Shared (ISP) proxies")}
                  className={`p-3 rounded-lg border-2 transition-all text-left ${
                    selectedProxyType?.id === "Shared (ISP) proxies"
                      ? "border-purple-500 bg-purple-50"
                      : "border-gray-200 bg-white hover:border-purple-200"
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        selectedProxyType?.id === "Shared (ISP) proxies"
                          ? "border-purple-500 bg-purple-500"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedProxyType?.id === "Shared (ISP) proxies" && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                    <span className="font-medium text-xs sm:text-sm lg:text-base">
                      普通
                    </span>
                  </div>
                </button>

                <button
                  onClick={() => handleProxyTypeChange("Premium (ISP) proxies")}
                  className={`p-3 rounded-lg border-2 transition-all text-left ${
                    selectedProxyType?.id === "Premium (ISP) proxies"
                      ? "border-purple-500 bg-purple-50"
                      : "border-gray-200 bg-white hover:border-purple-200"
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        selectedProxyType?.id === "Premium (ISP) proxies"
                          ? "border-purple-500 bg-purple-500"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedProxyType?.id === "Premium (ISP) proxies" && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                    <span className="font-medium text-xs sm:text-sm lg:text-base">
                      原生
                    </span>
                  </div>
                </button>
              </div>
            </div>

            {/* 桌面端：使用原有组件 */}
            <div className="hidden sm:block">
              <ProxyTypeSelector onTypeChange={handleProxyTypeChange} compact />
            </div>
          </div>

          {/* 大洲选择器 - 使用优化的响应式组件 */}
          <div>
            <h3 className="text-sm sm:text-base lg:text-lg font-medium mb-2 md:mb-3 text-gray-800">
              选择地理位置
            </h3>
            <ResponsiveTabs
              continents={continents}
              selectedContinentId={selectedContinentId}
              onContinentSelect={handleContinentSelect}
            />
          </div>

          {/* 筛选和排序控制 */}
          <div className="bg-white border rounded-lg p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="text-xs sm:text-sm lg:text-base"
                >
                  <Filter className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                  筛选{" "}
                  {Object.keys(filters).length > 0 &&
                    `(${Object.keys(filters).length})`}
                </Button>

                {Object.keys(filters).length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-xs sm:text-sm lg:text-base"
                  >
                    清除筛选
                  </Button>
                )}
              </div>

              <div className="flex items-center space-x-1">
                <span className="text-xs text-gray-600 hidden sm:inline">
                  排序:
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort("price")}
                  className="flex items-center space-x-1 px-2"
                >
                  <DollarSign className="w-3 h-3" />
                  <span className="text-xs">价格</span>
                  {query.sortBy === "price" &&
                    (query.sortOrder === "asc" ? (
                      <SortAsc className="w-2 h-2" />
                    ) : (
                      <SortDesc className="w-2 h-2" />
                    ))}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort("location")}
                  className="flex items-center space-x-1 px-2"
                >
                  <MapPin className="w-3 h-3" />
                  <span className="text-xs">位置</span>
                  {query.sortBy === "location" &&
                    (query.sortOrder === "asc" ? (
                      <SortAsc className="w-2 h-2" />
                    ) : (
                      <SortDesc className="w-2 h-2" />
                    ))}
                </Button>
              </div>
            </div>
          </div>

          {/* 产品列表 */}
          <div>
            {selectedProxyType?.id === "DYNAMIC" ? (
              <DynamicIPPurchase />
            ) : (
              <>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-3">
                  <h3 className="text-sm sm:text-base lg:text-lg font-medium text-gray-800">
                    可用位置和价格
                  </h3>

                  {!loading && (
                    <div className="text-xs sm:text-sm text-gray-600">
                      找到{" "}
                      {
                        flatList.filter((item) => item.type === "product")
                          .length
                      }{" "}
                      个位置
                    </div>
                  )}
                </div>

                {loading ? (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 lg:gap-4">
                    {Array.from({ length: 10 }).map((_, index) => (
                      <Card key={index} className="animate-pulse">
                        <CardContent className="p-2 sm:p-3">
                          <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-5 bg-gray-200 rounded w-1/3"></div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : error ? (
                  <div className="text-center py-6 sm:py-8">
                    <div className="text-red-600 mb-2 text-sm sm:text-base">
                      加载失败
                    </div>
                    <div className="text-xs sm:text-sm text-gray-500">
                      {error.message}
                    </div>
                  </div>
                ) : flatList.filter((item) => item.type === "product")
                    .length === 0 ? (
                  <div className="text-center py-6 sm:py-8 text-gray-500">
                    <MapPin className="w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-gray-300" />
                    <p className="text-sm sm:text-base">该区域暂无可用位置</p>
                    <p className="text-xs sm:text-sm">请尝试选择其他地区</p>
                  </div>
                ) : (
                  // 优化的网格布局 - 移动端两列，更大屏幕更多列
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 lg:gap-4">
                    {flatList.map((item) => {
                      const priceInfo = getPriceLabel(item);
                      const countryCode = getCountryCode(item);
                      const countryName = getCountryName(item);
                      const isSelected = selectedId === item.id;

                      return (
                        <Card
                          key={item.listId}
                          className={`cursor-pointer transition-all hover:shadow-md border-2 ${
                            isSelected
                              ? "border-purple-500 bg-purple-50"
                              : "border-gray-200 hover:border-purple-200"
                          }`}
                          onClick={() => handleProductSelect(item)}
                        >
                          <CardContent className="p-2 sm:p-3">
                            <div className="space-y-2">
                              {/* 国旗和位置信息 - 一行显示 */}
                              <div className="flex items-center space-x-2">
                                {countryCode && countryCode.length === 2 && (
                                  <ReactCountryFlag
                                    countryCode={countryCode}
                                    svg
                                    style={{
                                      width: "16px",
                                      height: "12px",
                                      borderRadius: "2px",
                                      flexShrink: 0,
                                    }}
                                    title={countryCode}
                                  />
                                )}
                                <div className="min-w-0 flex-1">
                                  <div className="font-semibold text-gray-900 text-xs sm:text-sm lg:text-base leading-tight truncate">
                                    {countryName} - {item.cityName}
                                  </div>
                                </div>
                              </div>

                              {/* 价格信息 */}
                              {hasValidPrice(item) && (
                                <div className="space-y-1">
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm font-bold text-purple-600 truncate">
                                      {priceInfo.mainPrice}
                                    </span>
                                    {priceInfo.hasDiscount &&
                                      priceInfo.discountPercentage && (
                                        <Badge
                                          variant="destructive"
                                          className="text-xs px-1 py-0"
                                        >
                                          -{priceInfo.discountPercentage}%
                                        </Badge>
                                      )}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    有效期: {item.validityPeriod}天
                                  </div>
                                </div>
                              )}

                              {/* 库存和选中状态 */}
                              <div className="flex items-center justify-between text-xs">
                                <div>
                                  {item.availableCount === 0 ? (
                                    <span className="text-red-600 font-medium">
                                      缺货
                                    </span>
                                  ) : item.availableCount &&
                                    item.availableCount < 100 ? (
                                    <span className="text-orange-600 font-medium">
                                      可用: {item.availableCount}
                                    </span>
                                  ) : (
                                    <span className="text-green-600 font-medium">
                                      可用: {item.availableCount ?? 0}
                                    </span>
                                  )}
                                </div>
                                <div className="flex-shrink-0">
                                  {isSelected && (
                                    <Badge className="text-white bg-purple-600 text-xs px-1 py-0">
                                      已选择
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* 右侧 - 购物车 (仅桌面端显示) */}
        <div className="hidden lg:block w-full lg:w-80 order-first lg:order-last">
          <div className="lg:sticky lg:top-4">
            <Cart />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default StaticResidentialProxiesPricingV2;
