import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useOrderCreationStore } from "@/store/order-creation";
import { useProductPageStore } from "@/store/productStore";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function DynamicIPPurchase() {
  const navigate = useNavigate();
  const { setOrderDetails } = useOrderCreationStore();
  const { count, setCount, duration, setDuration } = useProductPageStore();

  const handlePurchase = () => {
    // Here you would typically get the product ID for the dynamic IP product
    // For now, we'll use a placeholder
    const dynamicIpProductId = "dynamic-ip-product-id";

    setOrderDetails({
      id: dynamicIpProductId,
      quantity: 1, // For dynamic IP, quantity is always 1 (1 channel)
      duration: duration,
      // Add any other relevant details for dynamic IP purchase
      limitTrafficGb: count, // Using 'count' as the traffic limit in GB
    });

    toast.info("正在跳转到结账页面...");
    navigate("/orders/dynamic-ip-checkout"); // Assuming a new checkout page for dynamic IPs
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>购买动态IP流量包</CardTitle>
        <CardDescription>
          选择您需要的流量和使用期限，即可开始使用。
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="traffic">流量 (GB)</Label>
          <Input
            id="traffic"
            type="number"
            value={count}
            onChange={(e) => setCount(parseInt(e.target.value, 10))}
            min={1}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="duration">使用期限 (天)</Label>
          <Input
            id="duration"
            type="number"
            value={duration}
            onChange={(e) => setDuration(parseInt(e.target.value, 10))}
            min={1}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handlePurchase}>立即购买</Button>
      </CardFooter>
    </Card>
  );
}
