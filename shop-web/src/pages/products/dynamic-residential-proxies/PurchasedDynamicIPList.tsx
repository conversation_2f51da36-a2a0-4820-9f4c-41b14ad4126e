import {
  DataExportDialog,
  type DownloadFormat,
} from "@/components/products/DataExportDialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useProxyInstanceStatusDict } from "@/hooks/useDict";
import type { ApiTypes } from "@/services/generated/types";
import {
  listUserChannels,
  updateChannel,
  getChannelTraffic,
  generateEndpoints,
  listChannelEndpoints,
  deleteEndpoint,
} from "@/services/dynamic-proxy";
import {
  batchUpdateStaticCredentials,
  updateStaticCredentials,
} from "@/services/static-proxy";
import { copyProxyData, downloadProxies } from "@/utils/data-export-utils";
import {
  Calendar,
  Clock,
  Copy,
  CreditCard,
  Download,
  Eye,
  EyeOff,
  Filter,
  Globe,
  KeyRound,
  MapPin,
  RefreshCw,
  Search,
  ShoppingCart,
  Wifi,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { ManageEndpointsDialog } from "./ManageEndpointsDialog";
import { TrafficChartDialog } from "./TrafficChartDialog";
import { EditChannelDialog } from "./EditChannelDialog";
import { BatchPasswordChangeDialog } from "@/pages/products/static-residential-proxies/BatchPasswordChangeDialog";
import { SinglePasswordChangeDialog } from "@/pages/products/static-residential-proxies/SinglePasswordChangeDialog";

interface IPListProps {
  pageSize?: number;
}

// 根据字典样式类映射到Badge variant
const getStatusVariant = (
  listClass: string = ""
): "default" | "secondary" | "destructive" => {
  switch (listClass) {
    case "success":
      return "default";
    case "danger":
      return "destructive";
    case "warning":
    case "info":
    default:
      return "secondary";
  }
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return "N/A";
  try {
    return new Date(dateString).toLocaleString("zh-CN", {
      // Using toLocaleString for date and time
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit", // Added seconds for consistency with image
      hour12: false,
    });
  } catch (e) {
    return "无效日期";
  }
};
const calculateRemainingDays = (
  expiresAt: string | null | undefined
): number => {
  if (!expiresAt) return 0;
  const expireDate = new Date(expiresAt);
  const now = new Date();
  const diffTime = expireDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

const copyToClipboard = async (text: string | undefined, label: string) => {
  if (text === undefined || text === null) {
    toast.error(`${label} 内容为空`);
    return;
  }
  try {
    await navigator.clipboard.writeText(text);
    toast.success(`${label}已复制到剪贴板`);
  } catch (err) {
    console.error("复制失败:", err);
    toast.error("复制失败");
  }
};

// 增强的响应式钩子
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);
    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
};

// 多断点响应式钩子
const useBreakpoints = () => {
  const isSm = useMediaQuery("(min-width: 640px)");
  const isMd = useMediaQuery("(min-width: 768px)");
  const isLg = useMediaQuery("(min-width: 1024px)");
  const isXl = useMediaQuery("(min-width: 1280px)");
  const is2Xl = useMediaQuery("(min-width: 1536px)");

  // 计算当前屏幕类型
  const getCurrentBreakpoint = () => {
    if (is2Xl) return "2xl";
    if (isXl) return "xl";
    if (isLg) return "lg";
    if (isMd) return "md";
    if (isSm) return "sm";
    return "xs";
  };

  return {
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isMobile: !isMd, // 768px以下为移动端
    isTablet: isMd && !isLg, // 768px-1024px为平板
    isDesktop: isLg, // 1024px以上为桌面端
    currentBreakpoint: getCurrentBreakpoint(),
  };
};

// 移动端卡片组件
const ProxyCard = ({
  proxy,
  isSelected,
  onSelect,
  onManageEndpoints, // 确保这里有定义
  onViewTraffic, // 确保这里有定义
  onEditChannel, // 确保这里有定义
  onSingleCopy, // 新增
  onSinglePasswordChange, // 新增
  onSingleRenewal, // 新增
  showPasswords,
  togglePasswordVisibility,
  getStatusLabel,
  getStatusClass,
  getStatusVariant,
  loading,
  singlePasswordChangeLoading,
  renewalLoading,
}: {
  proxy: ApiTypes.ProxyInstance;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
  onManageEndpoints: (proxy: ApiTypes.ProxyInstance) => void;
  onViewTraffic: (proxy: ApiTypes.ProxyInstance) => void;
  onEditChannel: (proxy: ApiTypes.ProxyInstance) => void;
  onSingleCopy: (proxy: ApiTypes.ProxyInstance) => void; // 新增类型定义
  onSinglePasswordChange: (proxy: ApiTypes.ProxyInstance) => void; // 新增类型定义
  onSingleRenewal: (proxy: ApiTypes.ProxyInstance) => void; // 新增类型定义
  showPasswords: Record<number | string, boolean>;
  togglePasswordVisibility: (instanceId: number | string) => void;
  getStatusLabel: (status: string) => string;
  getStatusClass: (status: string) => any;
  getStatusVariant: (
    listClass: string
  ) => "default" | "secondary" | "destructive";
  loading: boolean;
  singlePasswordChangeLoading: boolean;
  renewalLoading: boolean;
}) => {
  // 移除重复的props解构，因为这些属性已经在函数参数中解构过了
  const remainingDays = calculateRemainingDays(proxy.expiresAt);
  const statusLabel = getStatusLabel(proxy.status || "");
  const statusClass = getStatusClass(proxy.status || "");
  const statusVariant = getStatusVariant(statusClass.listClass);
  const isPasswordVisible = showPasswords[proxy.instanceId];

  console.log("ProxyCard: Rendering proxy", proxy.instanceId);
  console.log(
    "ProxyCard: onManageEndpoints prop received:",
    typeof onManageEndpoints
  );
  console.log("ProxyCard: onSingleCopy prop received:", typeof onSingleCopy);

  return (
    <Card
      className={`transition-all duration-200 ${
        isSelected ? "ring-2 ring-primary shadow-md" : "hover:shadow-sm"
      }`}
    >
      <CardContent className="p-3 sm:p-4 space-y-3 sm:space-y-4">
        {/* 头部：选择框、IP地址、状态 */}
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start gap-2 sm:gap-3 min-w-0 flex-1">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
              aria-label={`Select proxy ${proxy.instanceId}`}
              className="mt-1"
            />
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-1 sm:gap-2 mb-1">
                <span className="font-semibold text-base sm:text-lg truncate">
                  {proxy.channelName}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    copyToClipboard(proxy.channelName, "Channel名称")
                  }
                  className="h-5 w-5 sm:h-6 sm:w-6 p-0 flex-shrink-0"
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
              <div className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm text-muted-foreground">
                <Globe className="w-3 h-3 flex-shrink-0" />
                <span className="truncate">
                  {(proxy as any).country || proxy.countryCode}
                </span>
                {proxy.cityName && (
                  <>
                    <MapPin className="w-3 h-3 flex-shrink-0" />
                    <span className="truncate">{proxy.cityName}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <Badge variant={statusVariant} className="shrink-0 text-xs">
            {statusLabel}
          </Badge>
        </div>

        {/* 认证信息 */}
        <div className="bg-muted/50 rounded-lg p-2 sm:p-3 space-y-2">
          <div className="flex items-center justify-between gap-2">
            <span className="text-xs sm:text-sm text-muted-foreground flex-shrink-0">
              密码
            </span>
            <div className="flex items-center gap-1 min-w-0">
              <span className="font-mono text-xs sm:text-sm truncate max-w-[80px] sm:max-w-[120px]">
                {isPasswordVisible ? proxy.password : "••••••••"}
              </span>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => togglePasswordVisibility(proxy.instanceId)}
                className="h-5 w-5 sm:h-6 sm:w-6 p-0 flex-shrink-0"
              >
                {isPasswordVisible ? (
                  <EyeOff className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                ) : (
                  <Eye className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => copyToClipboard(proxy.password, "密码")}
                className="h-5 w-5 sm:h-6 sm:w-6 p-0 flex-shrink-0"
              >
                <Copy className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* 时间和剩余天数信息 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-muted-foreground">
              <Calendar className="w-3 h-3 flex-shrink-0" />
              <span className="text-xs sm:text-sm">创建时间</span>
            </div>
            <div className="text-green-600 text-xs break-all">
              {formatDate(proxy.activatedAt)}
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-muted-foreground">
              <Clock className="w-3 h-3 flex-shrink-0" />
              <span className="text-xs sm:text-sm">过期时间</span>
            </div>
            <div className="text-red-600 text-xs break-all">
              {formatDate(proxy.expiresAt)}
            </div>
          </div>
        </div>

        {/* 剩余天数和节点ID */}
        <div className="space-y-2 sm:space-y-3">
          {/* 剩余天数 */}
          <div className="flex items-center justify-between py-2 border-t">
            <span className="text-xs sm:text-sm text-muted-foreground">
              剩余天数
            </span>
            <span
              className={`text-xs sm:text-sm font-medium ${
                remainingDays <= 7 && remainingDays > 0
                  ? "text-red-500"
                  : remainingDays <= 30 && remainingDays > 0
                  ? "text-orange-500"
                  : remainingDays > 0
                  ? "text-green-500"
                  : "text-muted-foreground"
              }`}
            >
              {remainingDays > 0
                ? `${remainingDays} 天`
                : proxy.expiresAt
                ? "已过期"
                : "-"}
            </span>
          </div>

          {/* 节点ID */}
          <div className="flex items-center justify-between gap-2">
            <span className="text-xs sm:text-sm text-muted-foreground flex-shrink-0">
              节点ID
            </span>
            <div className="flex items-center gap-1 min-w-0">
              <span
                className="font-mono text-xs text-muted-foreground truncate max-w-[100px] sm:max-w-[140px]"
                title={proxy.providerInstanceId}
              >
                {proxy.providerInstanceId}
              </span>
              <Button
                variant="ghost"
                size="icon"
                onClick={() =>
                  copyToClipboard(proxy.providerInstanceId, "节点ID")
                }
                className="h-5 w-5 sm:h-6 sm:w-6 p-0 flex-shrink-0"
              >
                <Copy className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onManageEndpoints(proxy)}
            disabled={loading}
            className="flex-1 text-xs sm:text-sm h-9 sm:h-9 justify-center gap-1 sm:gap-2"
          >
            <KeyRound className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>管理端点</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewTraffic(proxy)}
            disabled={loading}
            className="flex-1 text-xs sm:text-sm h-9 sm:h-9 justify-center gap-1 sm:gap-2"
          >
            <CreditCard className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>流量使用</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEditChannel(proxy)}
            disabled={loading}
            className="flex-1 text-xs sm:text-sm h-9 sm:h-9 justify-center gap-1 sm:gap-2"
          >
            <CreditCard className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>编辑</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default function PurchasedIPList({ pageSize = 10 }: IPListProps) {
  const navigate = useNavigate();
  const breakpoints = useBreakpoints();
  const {
    options: statusOptions,
    getLabel: getStatusLabel,
    getClass: getStatusClass,
  } = useProxyInstanceStatusDict();
  const [proxies, setProxies] = useState<ApiTypes.ProxyInstance[]>([]);
  const [filteredProxies, setFilteredProxies] = useState<
    ApiTypes.ProxyInstance[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPasswords, setShowPasswords] = useState<
    Record<number | string, boolean>
  >({});
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedProxies, setSelectedProxies] = useState<Set<number | string>>(
    new Set()
  );
  const [activeTab, setActiveTab] = useState("all");
  const [showManageEndpointsDialog, setShowManageEndpointsDialog] =
    useState(false);
  const [showTrafficDialog, setShowTrafficDialog] = useState(false);
  const [showEditChannelDialog, setShowEditChannelDialog] = useState(false);
  const [selectedProxy, setSelectedProxy] =
    useState<ApiTypes.ProxyInstance | null>(null);
  const [singlePasswordChangeLoading, setSinglePasswordChangeLoading] =
    useState(false);
  const [renewalLoading, setRenewalLoading] = useState(false);
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);
  const [showCopyDialog, setShowCopyDialog] = useState(false);
  const [selectedCopyProxy, setSelectedCopyProxy] =
    useState<ApiTypes.ProxyInstance | null>(null);
  const [showPasswordChangeDialog, setShowPasswordChangeDialog] =
    useState(false);
  const [passwordChangeLoading, setPasswordChangeLoading] = useState(false);
  const [showSinglePasswordChangeDialog, setShowSinglePasswordChangeDialog] =
    useState(false);
  const [selectedSingleProxy, setSelectedSingleProxy] =
    useState<ApiTypes.ProxyInstance | null>(null);

  // Calculate IP counts by type
  const getIPCounts = () => {
    const shared = proxies.filter((p) => {
      // 根据产品代理分类或产品名称判断是否为共享代理
      const category = p.product?.productProxyCategory?.toLowerCase();
      const productName = p.product?.productName?.toLowerCase();
      return (
        category?.includes("shared") ||
        category?.includes("isp") ||
        productName?.includes("shared") ||
        productName?.includes("共享")
      );
    }).length;
    const premium = proxies.filter((p) => {
      // 根据产品代理分类或产品名称判断是否为原生代理
      const category = p.product?.productProxyCategory?.toLowerCase();
      const productName = p.product?.productName?.toLowerCase();
      return (
        category?.includes("premium") ||
        category?.includes("native") ||
        category?.includes("datacenter") ||
        productName?.includes("premium") ||
        productName?.includes("原生") ||
        productName?.includes("独享")
      );
    }).length;
    return { shared, premium, total: proxies.length };
  };

  const fetchProxies = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await listUserChannels();

      console.log("获取到的代理数据:", response);
      console.log(
        "数据类型:",
        typeof response,
        "是否为数组:",
        Array.isArray(response)
      );

      // API 现在直接返回数组
      setProxies(response.data || []);
    } catch (err) {
      console.error("获取IP列表失败:", err);
      setError("无法加载IP列表,请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProxies();
  }, []);

  useEffect(() => {
    let currentFiltered = proxies;

    // Filter by IP type (tab)
    if (activeTab !== "all") {
      currentFiltered = currentFiltered.filter((proxy) => {
        const category =
          proxy.product?.productProxyCategory?.toLowerCase() || "";
        const productName = proxy.product?.productName?.toLowerCase() || "";

        if (activeTab === "shared") {
          return (
            category.includes("shared") ||
            category.includes("isp") ||
            productName.includes("shared") ||
            productName.includes("共享")
          );
        } else if (activeTab === "premium") {
          return (
            category.includes("premium") ||
            category.includes("native") ||
            category.includes("datacenter") ||
            productName.includes("premium") ||
            productName.includes("原生") ||
            productName.includes("独享")
          );
        }
        return true;
      });
    }

    if (statusFilter !== "all") {
      currentFiltered = currentFiltered.filter(
        (proxy) => proxy.status === statusFilter
      );
    }
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      currentFiltered = currentFiltered.filter(
        (proxy) =>
          proxy.ipAddress?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.countryCode?.toLowerCase().includes(lowerSearchTerm) ||
          (proxy as any).country?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.cityName?.toLowerCase().includes(lowerSearchTerm) ||
          (proxy as any).region?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.username?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.providerInstanceId?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.product?.productName?.toLowerCase().includes(lowerSearchTerm) ||
          proxy.protocols?.toLowerCase().includes(lowerSearchTerm)
      );
    }
    setFilteredProxies(currentFiltered);
    setCurrentPage(1);

    // Validate selectedProxies against the new full 'proxies' list
    const validSelectedProxies = new Set<number | string>();
    const allFetchedInstanceIds = new Set(proxies.map((p) => p.instanceId));
    selectedProxies.forEach((id) => {
      if (allFetchedInstanceIds.has(Number(id))) {
        validSelectedProxies.add(id);
      }
    });
    setSelectedProxies(validSelectedProxies);
  }, [proxies, searchTerm, statusFilter, activeTab]);

  const totalPages = Math.ceil(filteredProxies.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentProxiesOnPage = filteredProxies.slice(startIndex, endIndex);

  const togglePasswordVisibility = (instanceId: number | string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [instanceId]: !prev[instanceId],
    }));
  };

  // Selection logic
  const handleSelectRow = (instanceId: number | string, checked: boolean) => {
    const newSelectedProxies = new Set(selectedProxies);
    if (checked) {
      newSelectedProxies.add(instanceId);
    } else {
      newSelectedProxies.delete(instanceId);
    }
    setSelectedProxies(newSelectedProxies);
  };

  const selectedOnCurrentPageCount = currentProxiesOnPage.filter((p) =>
    selectedProxies.has(p.instanceId)
  ).length;
  const isAllOnCurrentPageSelected =
    currentProxiesOnPage.length > 0 &&
    selectedOnCurrentPageCount === currentProxiesOnPage.length;
  const isSomeOnCurrentPageSelected =
    currentProxiesOnPage.length > 0 &&
    selectedOnCurrentPageCount > 0 &&
    !isAllOnCurrentPageSelected;

  const handleSelectAllCurrentPage = (checked: boolean | "indeterminate") => {
    const newSelectedProxies = new Set(selectedProxies);
    // Determine if we should select or deselect all on current page
    // If checked is true (was false/indeterminate), or if indeterminate and not all selected, then select all.
    // Otherwise (checked is false, or indeterminate and all were selected), deselect all.
    const selectAll =
      typeof checked === "boolean" ? checked : !isAllOnCurrentPageSelected;

    if (selectAll) {
      currentProxiesOnPage.forEach((proxy) =>
        newSelectedProxies.add(proxy.instanceId)
      );
    } else {
      currentProxiesOnPage.forEach((proxy) =>
        newSelectedProxies.delete(proxy.instanceId)
      );
    }
    setSelectedProxies(newSelectedProxies);
  };

  // Batch action handlers
  const handleDownloadSelected = () => {
    if (selectedProxies.size === 0) {
      toast.info("请至少选择一个IP进行下载");
      return;
    }
    setShowDownloadDialog(true);
  };

  const handleCopySelected = () => {
    if (selectedProxies.size === 0) {
      toast.info("请至少选择一个IP进行复制");
      return;
    }
    // 使用批量复制，设置选中的代理列表
    const selectedProxyInstances = proxies.filter((proxy) =>
      selectedProxies.has(proxy.instanceId)
    );
    setSelectedCopyProxy(selectedProxyInstances[0]); // 设置第一个作为代表，实际会传递整个数��
    setShowCopyDialog(true);
  };

  const handleDownloadConfirm = async (format: DownloadFormat) => {
    try {
      // Get selected proxy instances
      const selectedProxyInstances = proxies.filter((proxy) =>
        selectedProxies.has(proxy.instanceId)
      );

      await downloadProxies(selectedProxyInstances, format);
      toast.success(`已下载 ${selectedProxyInstances.length} 个IP`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "下载失败，请重试");
    }
  };

  const handleRenewSelected = () => {
    if (selectedProxies.size === 0) {
      toast.info("请至少选择一个IP进行续费");
      return;
    }
    // 显示确认对话框
    setShowRenewalDialog(true);
  };

  const handleConfirmRenewal = async () => {
    try {
      setRenewalLoading(true);
      // Filter out any invalid IDs and ensure they are valid strings
      const instanceIds = Array.from(selectedProxies)
        .filter((id) => id !== undefined && id !== null && id !== "")
        .map((id) => {
          // Convert to string and validate
          const stringId = typeof id === "number" ? id.toString() : String(id);
          // Check if it's a valid numeric string
          if (!stringId || isNaN(parseInt(stringId, 10))) {
            console.warn(`Invalid instance ID: ${id}`);
            return null;
          }
          return stringId;
        })
        .filter((id): id is string => id !== null);

      if (instanceIds.length === 0) {
        toast.error("没有有效的IP实例ID");
        return;
      }

      setShowRenewalDialog(false);

      // 跳转到结账页面进行续费
      const selectedProxyInstances = proxies.filter((proxy) =>
        selectedProxies.has(proxy.instanceId)
      );

      // 构建续费结账页面的URL参数
      const checkoutParams = new URLSearchParams({
        type: "renewal",
        instanceIds: instanceIds.join(","),
        // 传递第一个产品的信息用于显示
        productId: selectedProxyInstances[0]?.productId?.toString() || "",
        quantity: selectedProxyInstances.length.toString(),
      });

      navigate(`/checkout/renewal?${checkoutParams.toString()}`);
    } catch (error: any) {
      toast.error(error.response?.data?.message || "准备续费失败");
      console.error("Renewal preparation failed:", error);
    } finally {
      setRenewalLoading(false);
    }
  };

  const handleChangePasswordSelected = () => {
    if (selectedProxies.size === 0) {
      toast.info("请至少选择一个IP更改密码");
      return;
    }
    setShowPasswordChangeDialog(true);
  };

  const handleConfirmPasswordChange = async (customCredentials?: {
    username?: string;
    password?: string;
  }) => {
    try {
      setPasswordChangeLoading(true);
      // Filter out any invalid IDs and ensure they are valid strings
      const instanceIds = Array.from(selectedProxies)
        .filter((id) => id !== undefined && id !== null && id !== "")
        .map((id) => {
          // Convert to string and validate
          const stringId = typeof id === "number" ? id.toString() : String(id);
          // Check if it's a valid numeric string
          if (!stringId || isNaN(parseInt(stringId, 10))) {
            console.warn(`Invalid instance ID: ${id}`);
            return null;
          }
          return stringId;
        })
        .filter((id): id is string => id !== null);

      if (instanceIds.length === 0) {
        toast.error("没有有效的IP实例ID");
        return;
      }

      const result = await batchUpdateStaticCredentials(
        instanceIds,
        customCredentials
      );

      setShowPasswordChangeDialog(false);

      if (result.success > 0) {
        toast.success(`成功更新了 ${result.success} 个IP的密码`);
        if (result.failed > 0) {
          toast.warning(`${result.failed} 个IP更新失���`);
        }
        // 刷新列表以显示最新数据
        await fetchProxies();
      } else {
        toast.error("所有IP密码更新失败");
      }

      // 清空选择
      setSelectedProxies(new Set());
    } catch (error: any) {
      toast.error(error.message || "批量更改密码失败");
      console.error("Batch password change failed:", error);
    } finally {
      setPasswordChangeLoading(false);
    }
  };

  const handleManageEndpoints = (proxy: ApiTypes.ProxyInstance) => {
    setSelectedProxy(proxy);
    setShowManageEndpointsDialog(true);
  };

  const handleViewTraffic = (proxy: ApiTypes.ProxyInstance) => {
    setSelectedProxy(proxy);
    setShowTrafficDialog(true);
  };

  const handleEditChannel = (proxy: ApiTypes.ProxyInstance) => {
    setSelectedProxy(proxy);
    setShowEditChannelDialog(true);
  };

  const handleSinglePasswordChange = (proxy: ApiTypes.ProxyInstance) => {
    setSelectedSingleProxy(proxy);
    setShowSinglePasswordChangeDialog(true);
  };

  const handleSingleRenewal = (proxy: ApiTypes.ProxyInstance) => {
    // 为单个IP创建续费流程
    const instanceId = proxy.instanceId.toString();

    // 构建续费结账页面的URL参数
    const checkoutParams = new URLSearchParams({
      type: "renewal",
      instanceIds: instanceId,
      productId: proxy.productId?.toString() || "",
      quantity: "1",
    });

    navigate(`/checkout/renewal?${checkoutParams.toString()}`);
  };

  const handleSingleCopy = (proxy: ApiTypes.ProxyInstance) => {
    setSelectedCopyProxy(proxy);
    setShowCopyDialog(true);
  };

  const handleCopyConfirm = async (format: DownloadFormat) => {
    try {
      // 检查是否是批量复��（通过选中的代理数量判断）
      if (selectedProxies.size > 1) {
        // 批量复制
        const selectedProxyInstances = proxies.filter((proxy) =>
          selectedProxies.has(proxy.instanceId)
        );
        await copyProxyData(selectedProxyInstances, format);
        toast.success(
          `已复制 ${selectedProxyInstances.length} 个代理信息到剪贴板`
        );
      } else if (selectedCopyProxy) {
        // 单个复制
        await copyProxyData(selectedCopyProxy, format);
        toast.success(`代理信息已复制到剪贴板`);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "复制失败，请重试");
    }
  };

  const handleConfirmSinglePasswordChange = async (credentials: {
    username?: string;
    password?: string;
    generateRandom?: boolean;
  }) => {
    if (!selectedSingleProxy) return;

    try {
      setSinglePasswordChangeLoading(true);

      const result = await updateStaticCredentials(
        selectedSingleProxy.instanceId,
        credentials
      );

      setShowSinglePasswordChangeDialog(false);
      setSelectedSingleProxy(null);

      if (result.success) {
        toast.success(`成功更新IP ${selectedSingleProxy.ipAddress} 的密码`);
        // 刷新列表以显示最新数据
        await fetchProxies();
      } else {
        toast.error(result.error || "更新密码失败");
      }
    } catch (error: any) {
      toast.error(error.message || "更改密码失败");
      console.error("Single password change failed:", error);
    } finally {
      setSinglePasswordChangeLoading(false);
    }
  };

  if (loading && proxies.length === 0) {
    // Show loading only on initial load
    return (
      <div className="max-w-8xl mx-auto">
        <div className="text-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">正在加载IP列表...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="text-center py-12">
          <Card className="max-w-md mx-auto p-8">
            <div className="flex flex-col items-center gap-4">
              <div className="rounded-full bg-destructive/10 p-4">
                <Wifi className="w-8 h-8 text-destructive" />
              </div>
              <div className="space-y-2 text-center">
                <h3 className="text-lg font-semibold">加载失败</h3>
                <p className="text-sm text-muted-foreground">{error}</p>
              </div>
              <Button onClick={() => fetchProxies()} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                重试
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`max-w-8xl mx-auto space-y-4 sm:space-y-6 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 ${
        breakpoints.currentBreakpoint === "xs" ? "px-2" : ""
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-baseline gap-2">
          <h2 className="text-2xl font-bold text-foreground">
            已购买动态IP列表
          </h2>
          <p className="text-muted-foreground">
            {activeTab === "all" && `共 ${filteredProxies.length} 个IP`}
            {activeTab === "shared" && `共 ${filteredProxies.length} 个共享IP`}
            {activeTab === "premium" && `共 ${filteredProxies.length} 个原生IP`}
            {(searchTerm || statusFilter !== "all") &&
              filteredProxies.length !==
                proxies.filter((p) => {
                  const category =
                    p.product?.productProxyCategory?.toLowerCase() || "";
                  const productName =
                    p.product?.productName?.toLowerCase() || "";

                  if (activeTab === "shared") {
                    return (
                      category.includes("shared") ||
                      category.includes("isp") ||
                      productName.includes("shared") ||
                      productName.includes("共享")
                    );
                  }
                  if (activeTab === "premium") {
                    return (
                      category.includes("premium") ||
                      category.includes("native") ||
                      category.includes("datacenter") ||
                      productName.includes("premium") ||
                      productName.includes("原生") ||
                      productName.includes("独享")
                    );
                  }
                  return true;
                }).length &&
              ` (已筛选)`}
          </p>
        </div>
        <Button
          onClick={() => fetchProxies()}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw
            className={`w-4 h-4 mr-2 ${
              loading && proxies.length > 0 ? "animate-spin" : ""
            }`}
          />
          刷新
        </Button>
      </div>

      {/* IP Type Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-1 max-w-md">
          <TabsTrigger value="all">全部 ({getIPCounts().total})</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Combined Batch Action Buttons Bar and Search/Filter Section */}
      <div className="bg-card rounded-lg border border-border p-4 space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between lg:gap-6">
        {/* Batch Action Buttons Bar */}
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          {/* 在小屏幕上允许按钮换行 */}
          <Button
            variant="default"
            size="sm"
            disabled={selectedProxies.size === 0 || loading}
            onClick={() => {
              const selected = proxies.filter((p) =>
                selectedProxies.has(p.instanceId)
              );
              // Implement bulk actions for dynamic channels if needed
              toast.info("动态IP的批量操作尚未实现");
            }}
            className="flex-shrink-0"
          >
            <span className="hidden sm:inline">批量操作</span>
          </Button>
        </div>

        {/* Search and Filter Section */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          {/* 搜索框 */}
          <div className="flex-1 min-w-0 sm:min-w-[300px] lg:min-w-[400px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索IP地址、用户名、国家、城市、产品名称或节点ID"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 text-sm"
                disabled={loading}
              />
            </div>
          </div>

          {/* 状态筛选器 */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <Filter className="w-4 h-4 text-muted-foreground flex-shrink-0" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="flex h-10 w-full sm:w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={loading}
            >
              <option value="all">全部状态</option>
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 移动端卡片布局 */}
      {breakpoints.isMobile ? (
        <div className="space-y-4">
          {currentProxiesOnPage.length === 0 ? (
            <div className="text-center py-12">
              {proxies.length > 0 && (searchTerm || statusFilter !== "all") ? (
                <div className="flex flex-col items-center gap-4">
                  <div className="rounded-full bg-muted p-4">
                    <Search className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-lg font-medium">没有找到匹配的IP</p>
                    <p className="text-sm text-muted-foreground">
                      尝试调整搜索条件或筛选器
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm("");
                      setStatusFilter("all");
                    }}
                  >
                    清除筛选
                  </Button>
                </div>
              ) : proxies.length === 0 && !loading ? (
                <div className="flex flex-col items-center gap-6 py-8">
                  <div className="rounded-full bg-primary/10 p-6">
                    <Wifi className="w-12 h-12 text-primary" />
                  </div>
                  <div className="space-y-2 text-center">
                    <h3 className="text-xl font-semibold">
                      您还没有购买任何静态IP
                    </h3>
                    <p className="text-muted-foreground max-w-md">
                      静态住宅代理提供稳定的IP地址，适合需要长期固定IP的业务场景
                    </p>
                  </div>
                  <div className="flex gap-4">
                    <Button
                      onClick={() => navigate("/products")}
                      className="bg-primary hover:bg-primary/90"
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      浏览产品
                    </Button>
                    <Button variant="outline" onClick={() => fetchProxies()}>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      刷新列表
                    </Button>
                  </div>
                </div>
              ) : null}
            </div>
          ) : (
            currentProxiesOnPage.map((proxy) => (
              <ProxyCard
                key={proxy.instanceId}
                proxy={proxy}
                isSelected={selectedProxies.has(proxy.instanceId)}
                onSelect={(checked) =>
                  handleSelectRow(proxy.instanceId, checked)
                }
                onManageEndpoints={handleManageEndpoints}
                onViewTraffic={handleViewTraffic}
                onEditChannel={handleEditChannel}
                showPasswords={showPasswords}
                togglePasswordVisibility={togglePasswordVisibility}
                getStatusLabel={getStatusLabel}
                getStatusClass={getStatusClass}
                getStatusVariant={getStatusVariant}
                loading={loading}
                singlePasswordChangeLoading={singlePasswordChangeLoading} // 传递属性
                renewalLoading={renewalLoading} // 传递属性
              />
            ))
          )}
        </div>
      ) : (
        /* 桌面端优化表格 */
        <div className="bg-card rounded-lg border border-border overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="w-[50px] xl:w-[60px]">
                    <Checkbox
                      checked={
                        isAllOnCurrentPageSelected
                          ? true
                          : isSomeOnCurrentPageSelected
                          ? "indeterminate"
                          : false
                      }
                      onCheckedChange={handleSelectAllCurrentPage}
                      aria-label="Select all rows on current page"
                      disabled={currentProxiesOnPage.length === 0 || loading}
                    />
                  </TableHead>
                  <TableHead className="min-w-[160px] lg:min-w-[180px] xl:min-w-[200px]">
                    Channel 名称
                  </TableHead>
                  <TableHead className="min-w-[200px] lg:min-w-[240px] xl:min-w-[280px]">
                    认证信息
                  </TableHead>
                  <TableHead className="min-w-[120px] lg:min-w-[140px] xl:min-w-[160px]">
                    流量
                  </TableHead>
                  <TableHead className="min-w-[80px] lg:min-w-[100px] xl:min-w-[120px]">
                    状态
                  </TableHead>
                  <TableHead className="min-w-[80px] lg:min-w-[100px] xl:min-w-[120px]">
                    剩余天数
                  </TableHead>
                  <TableHead className="min-w-[180px] lg:min-w-[200px] xl:min-w-[220px]">
                    创建/过期时间
                  </TableHead>
                  <TableHead className="min-w-[120px] lg:min-w-[140px] xl:min-w-[160px]">
                    Channel ID
                  </TableHead>
                  <TableHead className="min-w-[120px] lg:min-w-[140px] xl:min-w-[160px]">
                    操作
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentProxiesOnPage.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-12">
                      {proxies.length > 0 &&
                      (searchTerm || statusFilter !== "all") ? (
                        <div className="flex flex-col items-center gap-4">
                          <div className="rounded-full bg-muted p-4">
                            <Search className="w-8 h-8 text-muted-foreground" />
                          </div>
                          <div className="space-y-2">
                            <p className="text-lg font-medium">
                              没有找到匹配的Channel
                            </p>
                            <p className="text-sm text-muted-foreground">
                              尝试调整搜索条件或筛选器
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSearchTerm("");
                              setStatusFilter("all");
                            }}
                          >
                            清除筛选
                          </Button>
                        </div>
                      ) : proxies.length === 0 && !loading ? (
                        <div className="flex flex-col items-center gap-6 py-8">
                          <div className="rounded-full bg-primary/10 p-6">
                            <Wifi className="w-12 h-12 text-primary" />
                          </div>
                          <div className="space-y-2 text-center">
                            <h3 className="text-xl font-semibold">
                              您还没有购买任何动态IP
                            </h3>
                            <p className="text-muted-foreground max-w-md">
                              动态住宅代理提供轮换IP地址，适合需要大量IP的业务场景
                            </p>
                          </div>
                          <div className="flex gap-4">
                            <Button
                              onClick={() => navigate("/products")}
                              className="bg-primary hover:bg-primary/90"
                            >
                              <ShoppingCart className="w-4 h-4 mr-2" />
                              浏览产品
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => fetchProxies()}
                            >
                              <RefreshCw className="w-4 h-4 mr-2" />
                              刷新列表
                            </Button>
                          </div>
                        </div>
                      ) : null}
                    </TableCell>
                  </TableRow>
                ) : (
                  currentProxiesOnPage.map((proxy) => {
                    const remainingDays = calculateRemainingDays(
                      proxy.expiresAt
                    );
                    const statusLabel = getStatusLabel(proxy.status || "");
                    const statusClass = getStatusClass(proxy.status || "");
                    const statusVariant = getStatusVariant(
                      statusClass.listClass
                    );
                    const isPasswordVisible = showPasswords[proxy.instanceId];

                    return (
                      <TableRow
                        key={proxy.instanceId}
                        data-state={
                          selectedProxies.has(proxy.instanceId)
                            ? "selected"
                            : ""
                        }
                      >
                        <TableCell className="py-3 lg:py-4">
                          <Checkbox
                            checked={selectedProxies.has(proxy.instanceId)}
                            onCheckedChange={(checked) =>
                              handleSelectRow(
                                proxy.instanceId,
                                Boolean(checked)
                              )
                            }
                            aria-label={`Select row ${proxy.instanceId}`}
                          />
                        </TableCell>

                        {/* Channel 名称 */}
                        <TableCell className="font-medium py-3 lg:py-4">
                          <div className="flex items-center gap-1 lg:gap-2">
                            <span
                              className="font-mono text-sm lg:text-base truncate max-w-[120px] lg:max-w-[160px] xl:max-w-[180px]"
                              title={proxy.channelName}
                            >
                              {proxy.channelName}
                            </span>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                copyToClipboard(
                                  proxy.channelName,
                                  "Channel 名称"
                                )
                              }
                              className="h-5 w-5 lg:h-6 lg:w-6 p-0 flex-shrink-0"
                            >
                              <Copy className="w-2.5 h-2.5 lg:w-3 lg:h-3" />
                            </Button>
                          </div>
                        </TableCell>

                        {/* 认证信息 */}
                        <TableCell className="py-3 lg:py-4">
                          <div className="space-y-1 lg:space-y-2 max-w-[200px] lg:max-w-[240px] xl:max-w-[280px]">
                            <div className="flex items-center gap-1">
                              <span className="text-xs lg:text-sm text-muted-foreground shrink-0">
                                密码:
                              </span>
                              <span className="font-mono text-xs lg:text-sm truncate max-w-[80px] lg:max-w-[120px] xl:max-w-[160px] inline-block">
                                {isPasswordVisible
                                  ? proxy.password
                                  : "••••••••"}
                              </span>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  togglePasswordVisibility(proxy.instanceId)
                                }
                                className="h-4 w-4 lg:h-5 lg:w-5 p-0 flex-shrink-0"
                              >
                                {isPasswordVisible ? (
                                  <EyeOff className="w-2 h-2 lg:w-2.5 lg:h-2.5" />
                                ) : (
                                  <Eye className="w-2 h-2 lg:w-2.5 lg:h-2.5" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  copyToClipboard(proxy.password, "密码")
                                }
                                className="h-4 w-4 lg:h-5 lg:w-5 p-0 flex-shrink-0"
                              >
                                <Copy className="w-2 h-2 lg:w-2.5 lg:h-2.5" />
                              </Button>
                            </div>
                          </div>
                        </TableCell>

                        {/* 流量 */}
                        <TableCell className="py-3 lg:py-4">
                          <div className="flex items-center gap-1 lg:gap-2 max-w-[120px] lg:max-w-[140px] xl:max-w-[160px]">
                            <div className="min-w-0">
                              <div className="text-xs lg:text-sm font-medium truncate">
                                {proxy.isUnlimited
                                  ? "无限"
                                  : `${proxy.usedFlow} / ${proxy.totalFlow} GB`}
                              </div>
                            </div>
                          </div>
                        </TableCell>

                        {/* 状态 */}
                        <TableCell className="py-3 lg:py-4">
                          <Badge
                            variant={statusVariant}
                            className="inline-block text-xs lg:text-sm min-w-[60px] lg:min-w-[80px] xl:min-w-[100px] text-center"
                          >
                            {statusLabel}
                          </Badge>
                        </TableCell>

                        {/* 剩余天数 */}
                        <TableCell className="py-3 lg:py-4">
                          <span
                            className={`text-xs lg:text-sm font-medium ${
                              remainingDays <= 7 && remainingDays > 0
                                ? "text-red-500"
                                : remainingDays <= 30 && remainingDays > 0
                                ? "text-orange-500"
                                : remainingDays > 0
                                ? "text-green-500"
                                : "text-muted-foreground"
                            }`}
                          >
                            {remainingDays > 0
                              ? `${remainingDays} 天`
                              : proxy.expiresAt
                              ? "已过期"
                              : "-"}
                          </span>
                        </TableCell>

                        {/* 创建/过期时间 */}
                        <TableCell className="py-3 lg:py-4">
                          <div className="text-xs space-y-1 max-w-[180px] lg:max-w-[200px] xl:max-w-[220px]">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-green-600 flex-shrink-0" />
                              <span
                                className="text-green-600 truncate"
                                title={formatDate(proxy.activatedAt)}
                              >
                                {formatDate(proxy.activatedAt)}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-red-600 flex-shrink-0" />
                              <span
                                className="text-red-600 truncate"
                                title={formatDate(proxy.expiresAt)}
                              >
                                {formatDate(proxy.expiresAt)}
                              </span>
                            </div>
                          </div>
                        </TableCell>

                        {/* Channel ID */}
                        <TableCell className="py-3 lg:py-4">
                          <div className="flex items-center gap-1 max-w-[120px] lg:max-w-[140px] xl:max-w-[160px]">
                            <span
                              className="text-xs text-muted-foreground truncate font-mono flex-1"
                              title={proxy.providerInstanceId}
                            >
                              {proxy.providerInstanceId}
                            </span>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                copyToClipboard(
                                  proxy.providerInstanceId,
                                  "Channel ID"
                                )
                              }
                              className="h-4 w-4 lg:h-5 lg:w-5 p-0 flex-shrink-0"
                            >
                              <Copy className="w-2 h-2 lg:w-2.5 lg:h-2.5" />
                            </Button>
                          </div>
                        </TableCell>

                        {/* 操作 */}
                        <TableCell className="py-3 lg:py-4">
                          <div className="flex items-center gap-2 lg:gap-3 xl:gap-4">
                            <Button
                              variant="link"
                              size="sm"
                              onClick={() => handleManageEndpoints(proxy)}
                              disabled={loading}
                              className="p-0 h-auto text-xs lg:text-sm"
                              title="管理端点"
                            >
                              管理端点
                            </Button>
                            <Button
                              variant="link"
                              size="sm"
                              onClick={() => handleViewTraffic(proxy)}
                              disabled={loading}
                              className="p-0 h-auto text-xs lg:text-sm"
                            >
                              流量
                            </Button>
                            <Button
                              variant="link"
                              size="sm"
                              onClick={() => handleEditChannel(proxy)}
                              disabled={loading}
                              className="p-0 h-auto text-xs lg:text-sm"
                            >
                              编辑
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-4 pt-4">
          <div className="text-xs sm:text-sm text-muted-foreground order-2 sm:order-1">
            <span className="hidden sm:inline">
              第 {currentPage} / {totalPages} 页 (共 {filteredProxies.length}{" "}
              条)
            </span>
            <span className="sm:hidden">共 {filteredProxies.length} 条</span>
          </div>
          <div className="flex items-center gap-2 sm:gap-3 order-1 sm:order-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1 || loading}
              className="text-xs sm:text-sm h-8 sm:h-9 px-2 sm:px-3"
            >
              <span className="hidden sm:inline">上一页</span>
              <span className="sm:hidden">上一页</span>
            </Button>
            <span className="text-xs sm:text-sm text-muted-foreground px-2 sm:px-3 py-1 bg-muted rounded">
              {currentPage} / {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) => Math.min(totalPages, prev + 1))
              }
              disabled={currentPage === totalPages || loading}
              className="text-xs sm:text-sm h-8 sm:h-9 px-2 sm:px-3"
            >
              <span className="hidden sm:inline">下一页</span>
              <span className="sm:hidden">下一页</span>
            </Button>
          </div>
        </div>
      )}

      <ManageEndpointsDialog
        open={showManageEndpointsDialog}
        onOpenChange={setShowManageEndpointsDialog}
        proxy={selectedProxy}
      />
      <TrafficChartDialog
        open={showTrafficDialog}
        onOpenChange={setShowTrafficDialog}
        proxy={selectedProxy}
      />
      <EditChannelDialog
        open={showEditChannelDialog}
        onOpenChange={setShowEditChannelDialog}
        proxy={selectedProxy}
        onSuccess={fetchProxies}
      />
      {/* 续费确认对话框 */}
      <DataExportDialog
        open={showRenewalDialog}
        onOpenChange={setShowRenewalDialog}
        title="确认续费"
        description={`您确定要续费选中的 ${selectedProxies.size} 个IP吗？`}
        confirmText="确认续费"
        onConfirm={handleConfirmRenewal}
        showFormatSelection={false} // 续费不需要格式选择
        loading={renewalLoading}
      />

      {/* 批量更改密码对话框 */}
      <BatchPasswordChangeDialog
        open={showPasswordChangeDialog}
        onOpenChange={setShowPasswordChangeDialog}
        selectedProxies={proxies.filter((p) =>
          selectedProxies.has(p.instanceId)
        )}
        onConfirm={handleConfirmPasswordChange}
        loading={passwordChangeLoading}
      />

      {/* 单个更改密码对话框 */}
      <SinglePasswordChangeDialog
        open={showSinglePasswordChangeDialog}
        onOpenChange={setShowSinglePasswordChangeDialog}
        proxy={selectedSingleProxy}
        onConfirm={handleConfirmSinglePasswordChange}
        loading={singlePasswordChangeLoading}
      />
    </div>
  );
}
