/**
 * 产品开通状态显示工具函数
 */

export interface FulfillmentStatusDisplay {
  label: string;
  className: string;
  bgClassName: string;
  textClassName: string;
  isProcessing: boolean;
  isCompleted: boolean;
  isFailed: boolean;
}

/**
 * 获取开通状态显示信息
 */
export function getFulfillmentStatusDisplay(
  fulfillmentStatus?: string
): FulfillmentStatusDisplay {
  switch (fulfillmentStatus) {
    case 'pending':
      return {
        label: '等待开通',
        className: 'bg-gray-500',
        bgClassName: 'bg-gray-100',
        textClassName: 'text-gray-800',
        isProcessing: false,
        isCompleted: false,
        isFailed: false,
      };
    case 'processing':
      return {
        label: '开通中',
        className: 'bg-blue-500 animate-pulse',
        bgClassName: 'bg-blue-100',
        textClassName: 'text-blue-800',
        isProcessing: true,
        isCompleted: false,
        isFailed: false,
      };
    case 'completed':
      return {
        label: '已开通',
        className: 'bg-green-500',
        bgClassName: 'bg-green-100',
        textClassName: 'text-green-800',
        isProcessing: false,
        isCompleted: true,
        isFailed: false,
      };
    case 'failed':
      return {
        label: '开通失败',
        className: 'bg-red-500',
        bgClassName: 'bg-red-100',
        textClassName: 'text-red-800',
        isProcessing: false,
        isCompleted: false,
        isFailed: true,
      };
    default:
      return {
        label: '未知状态',
        className: 'bg-gray-400',
        bgClassName: 'bg-gray-100',
        textClassName: 'text-gray-600',
        isProcessing: false,
        isCompleted: false,
        isFailed: false,
      };
  }
}

/**
 * 判断是否应该显示开通状态
 * 只有已支付的订单才显示开通状态
 */
export function shouldShowFulfillmentStatus(
  orderStatus: string,
  fulfillmentStatus?: string
): boolean {
  // 只有已支付的订单才显示开通状态
  if (orderStatus !== '1') {
    return false;
  }
  
  // 有开通状态字段才显示
  return !!fulfillmentStatus;
}

/**
 * 获取开通进度百分比
 */
export function getFulfillmentProgress(fulfillmentStatus?: string): number {
  switch (fulfillmentStatus) {
    case 'pending':
      return 0;
    case 'processing':
      return 50;
    case 'completed':
      return 100;
    case 'failed':
      return 0;
    default:
      return 0;
  }
}