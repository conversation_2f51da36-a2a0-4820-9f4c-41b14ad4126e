# 时间格式化工具使用指南

## 📝 概述

新的统一时间格式化工具 (`@/utils/timeFormatter`) 提供了一个可扩展的、支持多时区和国际化的时间处理方案。

## 🚀 核心特性

- **多时区支持**: 轻松切换不同时区 (北京、东京、纽约等)
- **国际化支持**: 支持多语言环境 (中文、英文、日文)
- **格式灵活**: 自定义时间显示格式
- **向后兼容**: 保持与现有代码的兼容性
- **错误处理**: 优雅的错误处理和降级机制

## 🛠 基本用法

### 1. 导入函数

```javascript
// 基础函数
import { formatTime } from '@/utils/timeFormatter'

// 预设函数
import { 
  formatDateTime,    // YYYY-MM-DD HH:mm:ss
  formatDate,        // YYYY-MM-DD  
  formatTimeOnly,    // HH:mm:ss
  formatShortDateTime, // MM-DD HH:mm
  getCurrentTime     // 当前时间
} from '@/utils/timeFormatter'
```

### 2. 基本使用

```javascript
// 基本格式化 (默认: YYYY-MM-DD HH:mm:ss, 北京时间)
formatTime('2025-06-23T10:30:00Z')
// 输出: "2025-06-23 18:30:00"

// 自定义格式
formatTime('2025-06-23T10:30:00Z', { format: '{y}/{m}/{d} {h}:{i}' })
// 输出: "2025/06/23 18:30"

// 预设格式
formatDateTime('2025-06-23T10:30:00Z')   // "2025-06-23 18:30:00"
formatDate('2025-06-23T10:30:00Z')       // "2025-06-23" 
formatTimeOnly('2025-06-23T10:30:00Z')   // "18:30:00"
formatShortDateTime('2025-06-23T10:30:00Z') // "06-23 18:30"
```

## 🌍 多时区支持

```javascript
// 北京时间 (默认)
formatTime(utcTime, { timezone: 'Asia/Shanghai' })

// 东京时间
formatTime(utcTime, { timezone: 'Asia/Tokyo' })

// 纽约时间
formatTime(utcTime, { timezone: 'America/New_York' })

// UTC时间
formatTime(utcTime, { timezone: 'UTC' })
```

## 🌐 国际化支持

```javascript
// 中文环境 (默认)
formatTime(utcTime, { locale: 'zh-CN' })

// 英文环境
formatTime(utcTime, { locale: 'en-US' })

// 日文环境
formatTime(utcTime, { locale: 'ja-JP' })
```

## ⚙️ 全局配置

```javascript
import { setGlobalTimeConfig } from '@/utils/timeFormatter'

// 设置全局默认配置
setGlobalTimeConfig({
  timezone: 'Asia/Tokyo',      // 切换为东京时间
  locale: 'ja-JP',             // 切换为日文
  format: '{y}/{m}/{d} {h}:{i}' // 自定义默认格式
})
```

## 📋 在 Vue 模板中使用

### el-table 中的时间显示

```vue
<template>
  <el-table :data="tableData">
    <!-- 方式1: 使用 formatter -->
    <el-table-column 
      prop="createTime" 
      label="创建时间" 
      :formatter="timeFormatter"
    />
    
    <!-- 方式2: 使用模板插槽 -->
    <el-table-column label="更新时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.updateTime) }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { formatTime } from '@/utils/timeFormatter'

// 表格时间格式化器
const timeFormatter = (row, column, cellValue) => {
  return formatTime(cellValue)
}
</script>
```

### 表单和详情页

```vue
<template>
  <!-- 详情展示 -->
  <el-descriptions>
    <el-descriptions-item label="创建时间">
      {{ formatTime(detailData.createTime) }}
    </el-descriptions-item>
    <el-descriptions-item label="最后登录">
      {{ formatDateTime(detailData.lastLoginTime) }}
    </el-descriptions-item>
  </el-descriptions>

  <!-- 表单字段 -->
  <el-form>
    <el-form-item label="处理时间">
      {{ formatShortDateTime(formData.processTime) }}
    </el-form-item>
  </el-form>
</template>
```

## 🔄 迁移指南

### 从 parseTime 迁移

```javascript
// 旧代码
import { parseTime } from '@/utils/ruoyi'
parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')

// 新代码
import { formatTime } from '@/utils/timeFormatter'
formatTime(time, { format: '{y}-{m}-{d} {h}:{i}:{s}' })
// 或直接使用预设
formatDateTime(time)
```

### 从 formatInBeijingTime 迁移

```javascript
// 旧代码
import { formatInBeijingTime } from '@/utils/date'
formatInBeijingTime(time, '{y}-{m}-{d} {h}:{i}:{s}')

// 新代码
import { formatTime } from '@/utils/timeFormatter'
formatTime(time) // 默认就是北京时间
```

### 从 dayjs 迁移

```javascript
// 旧代码
import dayjs from 'dayjs'
dayjs(time).format('YYYY-MM-DD HH:mm:ss')

// 新代码
import { formatTime } from '@/utils/timeFormatter'
formatTime(time) // 自动处理时区转换
```

## 📐 格式模式参考

| 占位符 | 说明 | 示例 |
|--------|------|------|
| `{y}` | 年份 (4位) | 2025 |
| `{m}` | 月份 (01-12) | 06 |
| `{d}` | 日期 (01-31) | 23 |
| `{h}` | 小时 (00-23) | 18 |
| `{i}` | 分钟 (00-59) | 30 |
| `{s}` | 秒数 (00-59) | 45 |
| `{a}` | 星期 | 一 (中文) / Mon (英文) |

### 常用格式示例

```javascript
// 完整日期时间
'{y}-{m}-{d} {h}:{i}:{s}'  // 2025-06-23 18:30:45

// 日期格式
'{y}/{m}/{d}'              // 2025/06/23
'{y}年{m}月{d}日'          // 2025年06月23日

// 时间格式
'{h}:{i}'                  // 18:30
'{h}时{i}分'               // 18时30分

// 简短格式
'{m}-{d} {h}:{i}'          // 06-23 18:30
```

## 🛡 错误处理

```javascript
// 空值处理
formatTime(null)          // "--"
formatTime(undefined)     // "--"
formatTime('')            // "--"

// 无效日期处理
formatTime('invalid-date') // "无效时间" (中文) / "Invalid Time" (英文)

// 自定义错误处理
formatTime(invalidTime, { 
  onError: (error, input) => {
    console.log('Time format error:', error)
    return '格式错误'
  }
})
```

## 🔧 高级用法

### 动态时区切换

```javascript
// 根据用户设置动态切换时区
const userTimezone = getUserPreference('timezone') // 'Asia/Tokyo'
formatTime(time, { timezone: userTimezone })

// 批量设置
setGlobalTimeConfig({ 
  timezone: userTimezone,
  locale: getUserPreference('locale')
})
```

### 自定义格式函数

```javascript
// 创建自定义格式化函数
const formatUserTime = (time) => formatTime(time, {
  format: '{y}年{m}月{d}日 {h}:{i}',
  timezone: 'Asia/Shanghai'
})

// 在组件中使用
{{ formatUserTime(user.createTime) }}
```

## ⚡ 性能优化

```javascript
// 对于大量数据，考虑使用缓存
const timeCache = new Map()

const cachedFormatTime = (time) => {
  if (timeCache.has(time)) {
    return timeCache.get(time)
  }
  const result = formatTime(time)
  timeCache.set(time, result)
  return result
}
```

## 🧪 测试示例

```javascript
import { formatTime, setGlobalTimeConfig } from '@/utils/timeFormatter'

// 测试基本功能
console.log(formatTime('2025-06-23T10:30:00Z'))
// 输出: "2025-06-23 18:30:00"

// 测试时区切换
console.log(formatTime('2025-06-23T10:30:00Z', { timezone: 'Asia/Tokyo' }))
// 输出: "2025-06-23 19:30:00"

// 测试全局配置
setGlobalTimeConfig({ timezone: 'UTC' })
console.log(formatTime('2025-06-23T10:30:00Z'))
// 输出: "2025-06-23 10:30:00"
```

## 📝 最佳实践

1. **统一使用新工具**: 逐步替换项目中的 `parseTime`、`formatInBeijingTime` 和直接使用 `dayjs`
2. **全局配置**: 在应用启动时设置全局时区和语言配置
3. **类型安全**: 确保传入的时间数据是有效的UTC时间
4. **错误处理**: 在关键位置添加错误处理逻辑
5. **性能考虑**: 对于大量时间数据，考虑使用缓存或虚拟滚动

## 🐛 常见问题

### Q: 为什么时间显示不正确？
A: 确保后端传递的是UTC时间，新工具假设输入是UTC时间并转换到目标时区。

### Q: 如何支持新的时区？
A: 在 `timeFormatter.js` 的 `TIMEZONE_OFFSETS` 对象中添加新的时区映射。

### Q: 如何添加新的语言支持？
A: 在相关的语言映射对象中添加新语言的翻译。

### Q: 性能如何？
A: 新工具经过优化，性能与原有工具相当，并增加了错误处理和扩展性。

## 🔗 相关文件

- 主要工具: `/src/utils/timeFormatter.js`
- 兼容函数: `/src/utils/ruoyi.js`, `/src/utils/date.js`  
- 迁移脚本: `/scripts/unify-time-formatter.js`
- 使用指南: `/docs/时间格式化工具使用指南.md`