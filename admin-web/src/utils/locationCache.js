/**
 * 地理位置数据缓存工具
 * 提供内存缓存和本地存储缓存，支持静默刷新机制
 */

class LocationCache {
  constructor() {
    this.memoryCache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存过期
    this.storagePrefix = 'location_cache_'
    this.maxStorageTime = 24 * 60 * 60 * 1000 // 本地存储24小时过期
  }

  /**
   * 生成缓存key
   */
  generateKey(type, params = {}) {
    const paramStr = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|')
    return `${type}_${paramStr}`
  }

  /**
   * 获取内存缓存
   */
  getFromMemory(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null

    const now = Date.now()
    if (now - cached.timestamp > this.cacheTimeout) {
      this.memoryCache.delete(key)
      return null
    }

    return cached.data
  }

  /**
   * 设置内存缓存
   */
  setToMemory(key, data) {
    this.memoryCache.set(key, {
      data: data,
      timestamp: Date.now()
    })
  }

  /**
   * 获取本地存储缓存
   */
  getFromStorage(key) {
    try {
      const storageKey = this.storagePrefix + key
      const cached = localStorage.getItem(storageKey)
      if (!cached) return null

      const parsedCache = JSON.parse(cached)
      const now = Date.now()
      
      if (now - parsedCache.timestamp > this.maxStorageTime) {
        localStorage.removeItem(storageKey)
        return null
      }

      return parsedCache.data
    } catch (error) {
      console.warn('读取本地缓存失败:', error)
      return null
    }
  }

  /**
   * 设置本地存储缓存
   */
  setToStorage(key, data) {
    try {
      const storageKey = this.storagePrefix + key
      const cacheData = {
        data: data,
        timestamp: Date.now()
      }
      localStorage.setItem(storageKey, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('设置本地缓存失败:', error)
    }
  }

  /**
   * 获取缓存数据（先查内存，再查本地存储）
   */
  get(type, params = {}) {
    const key = this.generateKey(type, params)
    
    // 先查内存缓存
    let cached = this.getFromMemory(key)
    if (cached) {
      return { data: cached, source: 'memory' }
    }

    // 再查本地存储缓存
    cached = this.getFromStorage(key)
    if (cached) {
      // 将本地存储数据加载到内存
      this.setToMemory(key, cached)
      return { data: cached, source: 'storage' }
    }

    return null
  }

  /**
   * 设置缓存数据（同时设置内存和本地存储）
   */
  set(type, params = {}, data) {
    const key = this.generateKey(type, params)
    this.setToMemory(key, data)
    this.setToStorage(key, data)
  }

  /**
   * 检查缓存是否需要刷新（内存缓存超过2分钟或本地存储超过1小时）
   */
  shouldRefresh(type, params = {}) {
    const key = this.generateKey(type, params)
    const memoryCache = this.memoryCache.get(key)
    
    if (!memoryCache) return true
    
    const now = Date.now()
    const timeSinceCache = now - memoryCache.timestamp
    
    // 如果内存缓存超过2分钟，建议刷新
    return timeSinceCache > (2 * 60 * 1000)
  }

  /**
   * 清除特定类型的缓存
   */
  clearType(type) {
    // 清除内存缓存
    for (const [key] of this.memoryCache) {
      if (key.startsWith(type + '_')) {
        this.memoryCache.delete(key)
      }
    }

    // 清除本地存储缓存
    try {
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.storagePrefix + type + '_')) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('清除本地缓存失败:', error)
    }
  }

  /**
   * 清除所有缓存
   */
  clearAll() {
    this.memoryCache.clear()
    
    try {
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.storagePrefix)) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('清除所有本地缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const memorySize = this.memoryCache.size
    let storageSize = 0
    
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.storagePrefix)) {
          storageSize++
        }
      }
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
    }

    return {
      memorySize,
      storageSize,
      cacheTimeout: this.cacheTimeout,
      maxStorageTime: this.maxStorageTime
    }
  }
}

// 创建单例实例
const locationCache = new LocationCache()

export default locationCache