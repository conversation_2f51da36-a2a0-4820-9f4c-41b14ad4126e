<template>
  <div class="proxy-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="105px">
      <el-form-item label="客户ID" prop="customerId">
        <el-input v-model="queryParams.customerId" placeholder="请输入客户ID" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="Channel名称" prop="channelName">
        <el-input v-model="queryParams.channelName" placeholder="请输入Channel名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Refresh" :disabled="single" @click="handleRefreshStatus" v-hasPermi="['shop:proxy:edit']">刷新状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['shop:proxy:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="proxyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="实例ID" align="center" prop="instanceId" width="80" />
      <el-table-column label="客户ID" align="center" prop="customerId" width="80" />
      <el-table-column label="Channel名称" align="center" prop="channelName" :show-overflow-tooltip="true" />
      <el-table-column label="流量限制" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.isUnlimited">无限</span>
          <span v-else>{{ formatFlow(scope.row.totalFlow) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已用流量" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.usedFlow">{{ formatFlow(scope.row.usedFlow) }}</span>
          <span v-else>0 MB</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余流量" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.isUnlimited">无限</span>
          <span v-else-if="scope.row.remainingFlow !== null">{{ formatFlow(scope.row.remainingFlow) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="到期时间" align="center" prop="expiresAt" width="160">
        <template #default="scope">
          <span>{{ formatTime(scope.row.expiresAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ getStatusLabel(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" link icon="View" @click="handleDetail(scope.row)" v-hasPermi="['shop:proxy:query']">详情</el-button>
          <el-button type="success" link icon="Monitor" @click="handleViewTraffic(scope.row)" v-hasPermi="['shop:proxy:query']">流量</el-button>
          <el-button type="info" link icon="SetUp" @click="handleManageEndpoints(scope.row)" v-hasPermi="['shop:proxy:edit']">管理端点</el-button>
          <el-button type="warning" link icon="Edit" @click="handleUpdateChannel(scope.row)" v-hasPermi="['shop:proxy:edit']">编辑</el-button>
          <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['shop:proxy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit" @pagination="getList" />

    <!-- 详情对话框 -->
    <el-dialog :title="'动态代理详情 - Channel: ' + detailForm.channelName" v-model="detailOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="实例ID">{{ detailForm.instanceId }}</el-descriptions-item>
        <el-descriptions-item label="客户ID">{{ detailForm.customerId }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detailForm.orderId }}</el-descriptions-item>
        <el-descriptions-item label="产品ID">{{ detailForm.productId }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ detailForm.providerName }}</el-descriptions-item>
        <el-descriptions-item label="供应商实例ID">{{ detailForm.providerInstanceId }}</el-descriptions-item>
        <el-descriptions-item label="Channel名称">{{ detailForm.channelName }}</el-descriptions-item>
        <el-descriptions-item label="密码">{{ detailForm.password }}</el-descriptions-item>
        <el-descriptions-item label="购买时间">{{ formatTime(detailForm.purchasedAt) }}</el-descriptions-item>
        <el-descriptions-item label="激活时间">{{ formatTime(detailForm.activatedAt) }}</el-descriptions-item>
        <el-descriptions-item label="到期时间">{{ formatTime(detailForm.expiresAt) }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ getStatusLabel(detailForm.status) }}</el-descriptions-item>
        <el-descriptions-item label="流量限制" :span="2">
          {{ detailForm.isUnlimited ? '无限流量' : formatFlow(detailForm.totalFlow) }}
        </el-descriptions-item>
        <el-descriptions-item label="已用流量">{{ formatFlow(detailForm.usedFlow) }}</el-descriptions-item>
        <el-descriptions-item label="剩余流量">
          {{ detailForm.isUnlimited ? '无限' : formatFlow(detailForm.remainingFlow) }}
        </el-descriptions-item>
        <el-descriptions-item label="自动续费">{{ detailForm.autoRenew ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(detailForm.createTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 流量图表对话框 -->
    <el-dialog :title="'流量使用统计 - Channel: ' + trafficForm.channelName" v-model="trafficOpen" width="800px" append-to-body>
      <el-form :inline="true" :model="trafficQueryParams" class="traffic-query-form">
        <el-form-item label="日期类型">
          <el-select v-model="trafficQueryParams.dateType" placeholder="请选择日期类型" @change="handleTrafficQuery">
            <el-option v-for="item in dateTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <template v-if="trafficQueryParams.dateType === 4">
          <el-form-item label="开始日期">
            <el-date-picker v-model="trafficQueryParams.startTime" type="date" placeholder="选择开始日期" :disabled-date="disabledDate" format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item label="结束日期">
            <el-date-picker v-model="trafficQueryParams.endTime" type="date" placeholder="选择结束日期" :disabled-date="disabledDate" format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleTrafficQuery">查询</el-button>
          </el-form-item>
        </template>
      </el-form>
      <div ref="trafficChartRef" style="width: 100%; height: 300px"></div>
      <div class="traffic-summary">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="总流量">{{ formatFlow(trafficData.totalTrafficGb * 1024) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 管理端点对话框 -->
    <el-dialog :title="'管理端点 - Channel: ' + endpointsForm.channelName" v-model="endpointsOpen" width="900px" append-to-body>
      <el-tabs v-model="endpointsActiveTab">
        <el-tab-pane label="生成新端点" name="generate">
          <el-form ref="generateEndpointFormRef" :model="generateEndpointForm" :rules="generateEndpointRules" label-width="100px">
            <el-form-item label="位置" prop="location">
              <el-select v-model="generateEndpointForm.location" placeholder="请选择位置" style="width: 100%">
                <el-option v-for="item in locationOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="会话保持" prop="stickySessionTime">
              <el-input-number v-model="generateEndpointForm.stickySessionTime" :min="0" :max="60" style="width: 100%" placeholder="会话保持时间(分钟)，0为旋转模式" />
            </el-form-item>
            <el-form-item label="数量" prop="count">
              <el-input-number v-model="generateEndpointForm.count" :min="1" :max="50" style="width: 100%" placeholder="需要生成的端点数量" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitGenerateEndpoint">生成端点</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="现有端点" name="list">
          <el-table :data="endpointsList" style="width: 100%" v-loading="endpointsLoading">
            <el-table-column label="端点ID" prop="endpointId" width="80" />
            <el-table-column label="端点" prop="endpointString" :show-overflow-tooltip="true" />
            <el-table-column label="位置" prop="locationDesc" width="120" />
            <el-table-column label="会话保持" width="100">
              <template #default="scope">{{ scope.row.stickySessionTime || 0 }} 分钟</template>
            </el-table-column>
            <el-table-column label="生成时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.generatedAt) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                  {{ scope.row.status === 'active' ? '活跃' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button type="danger" link icon="Delete" @click="handleDeleteEndpoint(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 更新Channel对话框 -->
    <el-dialog :title="'更新Channel - ' + updateChannelForm.channelName" v-model="updateChannelOpen" width="500px" append-to-body>
      <el-form ref="updateChannelFormRef" :model="updateChannelForm" :rules="updateChannelRules" label-width="100px">
        <el-form-item label="Channel名称" prop="channelName">
          <el-input v-model="updateChannelForm.channelName" placeholder="请输入Channel名称" />
        </el-form-item>
        <el-form-item label="密码" prop="channelPassword">
          <el-input v-model="updateChannelForm.channelPassword" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="流量限制(GB)" prop="limitTrafficGb">
          <el-input-number v-model="updateChannelForm.limitTrafficGb" :min="0" style="width: 100%" placeholder="流量限制(GB)，0为无限流量" />
        </el-form-item>
        <el-form-item label="状态" prop="enable">
          <el-switch v-model="updateChannelForm.enable" :active-text="'启用'" :inactive-text="'禁用'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUpdateChannel">确 定</el-button>
          <el-button @click="updateChannelOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DynamicProxy">
import {
  deleteDynamicProxyEndpoint,
  delProxyInstance,
  generateDynamicProxyEndpoints,
  listDynamicProxyEndpoints,
  getDynamicProxyChannelTraffic,
  getProxyInstance,
  listProxyInstance,
  refreshProxyInstanceStatus,
  updateDynamicProxyChannel
} from '@/api/shop/proxy/index'
import { formatTime  } from '@/utils/timeFormatter'
import * as echarts from 'echarts'
import { nextTick, onMounted, ref } from 'vue'

const { proxy } = getCurrentInstance()

// echarts实例
let trafficChart = null

// 状态选项
const statusOptions = ref([
  { value: 'pending_creation', label: '待创建' },
  { value: 'active', label: '活跃' },
  { value: 'expired', label: '已过期' },
  { value: 'suspended', label: '已暂停' },
  { value: 'error', label: '错误' },
  { value: 'flow_exhausted', label: '流量耗尽' },
  { value: 'provision_failed', label: '配置失败' }
])

// 日期类型选项
const dateTypeOptions = ref([
  { value: 0, label: '今天' },
  { value: 1, label: '昨天' },
  { value: 2, label: '最近7天' },
  { value: 3, label: '最近30天' },
  { value: 4, label: '自定义' }
])

// 位置选项 (模拟数据)
const locationOptions = ref([
  { value: 'Global', label: '全球' },
  { value: 'US', label: '美国' },
  { value: 'GB', label: '英国' },
  { value: 'DE', label: '德国' },
  { value: 'FR', label: '法国' },
  { value: 'JP', label: '日本' },
  { value: 'SG', label: '新加坡' }
])

// 列表数据
const proxyList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 详情对话框
const detailOpen = ref(false)
const detailForm = ref({})

// 流量图表对话框
const trafficOpen = ref(false)
const trafficChartRef = ref(null)
const trafficForm = ref({
  instanceId: undefined,
  channelName: ''
})
const trafficQueryParams = ref({
  dateType: 0, // 默认今天
  startTime: '',
  endTime: ''
})
const trafficData = ref({
  items: [],
  totalTrafficGb: 0
})

// 管理端点对话框
const endpointsOpen = ref(false)
const endpointsActiveTab = ref('generate')
const endpointsForm = ref({
  instanceId: undefined,
  channelName: ''
})
const endpointsList = ref([])
const endpointsLoading = ref(false)

// 生成端点表单
const generateEndpointForm = ref({
  location: 'Global',
  stickySessionTime: 0,
  count: 1
})
const generateEndpointRules = ref({
  location: [{ required: true, message: '请选择位置', trigger: 'change' }],
  count: [{ required: true, message: '请输入数量', trigger: 'blur' }]
})

// 更新Channel对话框
const updateChannelOpen = ref(false)
const updateChannelForm = ref({
  instanceId: undefined,
  channelName: '',
  channelPassword: '',
  limitTrafficGb: 0,
  enable: true
})
const updateChannelRules = ref({
  channelName: [{ required: true, message: '请输入Channel名称', trigger: 'blur' }]
})

// 查询参数
const queryParams = ref({
  page: 1,
  limit: 10,
  ipType: 'DYNAMIC', // 只查询动态代理
  customerId: undefined,
  channelName: undefined,
  status: undefined
})

/** 查询代理列表 */
function getList() {
  loading.value = true
  listProxyInstance(queryParams.value)
    .then((response) => {
      proxyList.value = response.data.items
      total.value = response.data.meta.total
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm')
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.instanceId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case 'active':
      return 'success'
    case 'pending_creation':
      return 'info'
    case 'expired':
      return 'warning'
    case 'suspended':
      return 'warning'
    case 'error':
      return 'danger'
    case 'flow_exhausted':
      return 'danger'
    case 'provision_failed':
      return 'danger'
    default:
      return 'info'
  }
}

/** 获取状态标签 */
function getStatusLabel(status) {
  const found = statusOptions.value.find((item) => item.value === status)
  return found ? found.label : status
}

/** 格式化流量 */
function formatFlow(flowMb) {
  if (flowMb === null || flowMb === undefined) return '-'
  if (flowMb < 1024) return `${flowMb} MB`
  return `${(flowMb / 1024).toFixed(2)} GB`
}

/** 日期禁用判断 */
function disabledDate(time) {
  return time.getTime() > Date.now()
}

/** 详情按钮操作 */
function handleDetail(row) {
  const instanceId = row.instanceId
  getProxyInstance(instanceId).then((response) => {
    detailForm.value = response.data
    detailOpen.value = true
  })
}

/** 刷新状态按钮操作 */
function handleRefreshStatus(row) {
  const instanceId = row.instanceId || ids.value[0]
  refreshProxyInstanceStatus(instanceId).then(() => {
    proxy.$modal.msgSuccess('刷新状态成功')
    getList()
  })
}

/** 查看流量按钮操作 */
function handleViewTraffic(row) {
  trafficForm.value = {
    instanceId: row.instanceId,
    channelName: row.channelName
  }
  trafficQueryParams.value = {
    dateType: 0, // 默认今天
    startTime: '',
    endTime: ''
  }
  trafficOpen.value = true
  fetchTrafficData()
}

/** 获取流量数据 */
function fetchTrafficData() {
  const params = {
    dateType: trafficQueryParams.value.dateType
  }
  if (trafficQueryParams.value.dateType === 4) {
    params.startTime = trafficQueryParams.value.startTime ? formatTime(trafficQueryParams.value.startTime, '{y}-{m}-{d}') : ''
    params.endTime = trafficQueryParams.value.endTime ? formatTime(trafficQueryParams.value.endTime, '{y}-{m}-{d}') : ''
  }
  getDynamicProxyChannelTraffic(trafficForm.value.instanceId, params).then((response) => {
    trafficData.value = response.data
    nextTick(() => {
      initTrafficChart()
    })
  })
}

/** 流量数据查询 */
function handleTrafficQuery() {
  fetchTrafficData()
}

/** 初始化流量图表 */
function initTrafficChart() {
  if (trafficChart) {
    trafficChart.dispose()
  }
  trafficChart = echarts.init(trafficChartRef.value)

  // 准备数据
  const items = trafficData.value.items || []
  const dates = items.map((item) => formatTime(item.useTime, '{m}-{d}'))
  const values = items.map((item) => item.useTraffic)

  // 设置图表选项
  const option = {
    title: {
      text: '流量使用趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c} GB'
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '流量 (GB)'
    },
    series: [
      {
        name: '使用流量',
        type: 'line',
        data: values,
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }
    ]
  }

  trafficChart.setOption(option)
}

/** 管理端点按钮操作 */
function handleManageEndpoints(row) {
  endpointsForm.value = {
    instanceId: row.instanceId,
    channelName: row.channelName
  }
  endpointsActiveTab.value = 'generate'
  generateEndpointForm.value = {
    location: 'Global',
    stickySessionTime: 0,
    count: 1
  }
  endpointsOpen.value = true
  fetchEndpoints()
}

/** 获取端点列表 */
function fetchEndpoints() {
  endpointsLoading.value = true
  listDynamicProxyEndpoints(endpointsForm.value.instanceId)
    .then((response) => {
      endpointsList.value = response.data
      endpointsLoading.value = false
    })
    .catch(() => {
      endpointsLoading.value = false
    })
}

/** 提交生成端点表单 */
function submitGenerateEndpoint() {
  proxy.$refs['generateEndpointFormRef'].validate((valid) => {
    if (valid) {
      const data = {
        location: generateEndpointForm.value.location,
        stickySessionTime: generateEndpointForm.value.stickySessionTime,
        count: generateEndpointForm.value.count
      }
      generateDynamicProxyEndpoints(endpointsForm.value.instanceId, data).then((response) => {
        proxy.$modal.msgSuccess('生成端点成功')
        endpointsActiveTab.value = 'list'
        fetchEndpoints()
      })
    }
  })
}

/** 删除端点按钮操作 */
function handleDeleteEndpoint(row) {
  proxy.$modal
    .confirm('确认删除端点ID为"' + row.endpointId + '"的数据项?')
    .then(function () {
      return deleteDynamicProxyEndpoint(row.endpointId)
    })
    .then(() => {
      fetchEndpoints()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

/** 更新Channel按钮操作 */
function handleUpdateChannel(row) {
  updateChannelForm.value = {
    instanceId: row.instanceId,
    channelName: row.channelName,
    channelPassword: row.password || '',
    limitTrafficGb: row.isUnlimited ? 0 : row.totalFlow ? Math.floor(row.totalFlow / 1024) : 0,
    enable: row.status === 'active'
  }
  updateChannelOpen.value = true
}

/** 提交更新Channel表单 */
function submitUpdateChannel() {
  proxy.$refs['updateChannelFormRef'].validate((valid) => {
    if (valid) {
      const data = {
        channelName: updateChannelForm.value.channelName,
        channelPassword: updateChannelForm.value.channelPassword,
        limitTrafficGb: updateChannelForm.value.limitTrafficGb,
        enable: updateChannelForm.value.enable
      }
      updateDynamicProxyChannel(updateChannelForm.value.instanceId, data).then((response) => {
        proxy.$modal.msgSuccess('更新Channel成功')
        updateChannelOpen.value = false
        getList()
      })
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const instanceIds = row.instanceId || ids.value
  proxy.$modal
    .confirm('是否确认删除Channel编号为"' + instanceIds + '"的数据项?')
    .then(function () {
      return delProxyInstance(instanceIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

// 初始加载
getList()

// 监听窗口大小变化，重绘图表
onMounted(() => {
  window.addEventListener('resize', function () {
    if (trafficChart) {
      trafficChart.resize()
    }
  })
})
</script>

<style lang="scss" scoped>
.proxy-container {
  padding: 10px;
}

.traffic-query-form {
  margin-bottom: 20px;
}

.traffic-summary {
  margin-top: 20px;
}

.dialog-footer {
  padding-top: 16px;
  text-align: right;
}
</style>
