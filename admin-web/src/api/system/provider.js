import request from '@/utils/request'

// 获取供应商列表
// query 对象现在可以包含: keyword, page, limit, status, providerType
export function listProvider(query) {
  return request({
    url: '/system/providers', // 修正为正确的后端路径
    method: 'get',
    params: query
  })
}

// 获取供应商详细
export function getProvider(id) {
  return request({
    url: '/system/providers/' + id,
    method: 'get'
  })
}

// 新增供应商
// data 对象应包含: providerCode, providerName, providerType, configDetails, status, remark 等
export function addProvider(data) {
  return request({
    url: '/system/providers',
    method: 'post',
    data: data
  })
}

// 修改供应商
// id 是路径参数, data 对象应包含要更新的字段: providerCode, providerName, providerType, configDetails 等
export function updateProvider(id, data) {
  return request({
    url: '/system/providers/' + id,
    method: 'put',
    data: data
  })
}

// 删除供应商
export function delProvider(id) {
  return request({
    url: '/system/providers/' + id,
    method: 'delete'
  })
}

// 更新供应商状态 (新增，如果需要从前端直接调用)
// 后端有 @Put(':id/status')
export function updateProviderStatus(id, status) {
  return request({
    url: '/system/providers/' + id + '/status',
    method: 'put',
    data: { status } // Body should be { "status": "0" } or { "status": "1" }
  })
}

// 测试供应商连接 (新增)
// 后端有 @Post(':id/test-connection')
export function testProviderConnection(id) {
  return request({
    url: '/system/providers/' + id + '/test-connection',
    method: 'post'
    // No body needed for this request based on the backend controller
  })
}
