import request from '@/utils/request'

// 操作员手动充值功能已移除，请使用“调整余额”功能代替
// export function operatorRecharge(customerId, data) {
//   return request({
//     url: `/wallet/customer/${customerId}/recharge`,
//     method: 'post',
//     data
//   })
// }

// 查询客户余额信息
export function getCustomerWallet(customerId) {
  return request({
    url: `/wallet/customer/${customerId}`,
    method: 'get'
  })
}

// 查询余额交易列表（支持关联订单筛选）
export function listTransaction(query) {
  return request({
    url: '/wallet/transaction/list',
    method: 'get',
    params: {
      ...query,
      // 新增关联订单筛选
      relatedOrder: query.relatedOrder || undefined
    }
  })
}

// 查询充值记录列表（保留兼容性）
export function getRechargeList(query) {
  return listTransaction(query)
}

// 获取余额统计数据
export function getWalletStatistics(query) {
  return request({
    url: '/wallet/statistics',
    method: 'get',
    params: query
  })
}

// 获取客户交易记录
export function getCustomerTransactions(query) {
  return request({
    url: '/wallet/transactions',
    method: 'get',
    params: query
  })
}

// 调整客户余额
export function adjustBalance(customerId, data) {
  return request({
    url: `/wallet/customer/${customerId}/adjust`,
    method: 'post',
    data
  })
}

// 冻结/解冻客户余额
export function freezeUnfreezeBalance(customerId, data) {
  return request({
    url: `/wallet/customer/${customerId}/freeze`,
    method: 'post',
    data
  })
}

// 修改：创建充值订单（新流程）
export function createRechargeOrder(data) {
  return request({
    url: '/wallet/recharge/order', // 新的充值订单接口
    method: 'post',
    data: {
      ...data,
      operatorType: 'ADMIN' // 标识为管理员操作
    }
  })
}

// 查询交易详细（增强关联信息）
export function getTransaction(transactionId) {
  return request({
    url: `/wallet/transaction/${transactionId}`,
    method: 'get'
  })
}

// 新增：获取交易关联订单信息
export function getTransactionOrder(transactionId) {
  return request({
    url: `/wallet/transaction/${transactionId}/order`,
    method: 'get'
  })
}

// 新增：获取客户余额详情（包含余额快照）
export function getCustomerWalletDetail(customerId) {
  return request({
    url: `/wallet/customer/${customerId}/detail`,
    method: 'get'
  })
}

// 查询余额操作记录
export function getWalletOperationHistory(customerId, params) {
  return request({
    url: `/wallet/customer/${customerId}/history`,
    method: 'get',
    params: params
  })
}

// 导出交易记录
export function exportTransaction(query) {
  return request({
    url: '/wallet/transaction/export',
    method: 'get',
    params: query
  })
}

// 查询订单支付状态（用于轮询）
export function getOrderPaymentStatus(orderId) {
  return request({
    url: '/payment/order/status',
    method: 'get',
    params: { orderId }
  })
}
