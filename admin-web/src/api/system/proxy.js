import request from '@/utils/request'

// 获取静态代理实例列表
export function listStaticProxy(query) {
  return request({
    url: '/admin/system/proxy/static/list',
    method: 'get',
    params: query
  })
}

// 获取静态代理实例详细
export function getStaticProxy(id) {
  return request({
    url: '/admin/system/proxy/static/' + id,
    method: 'get'
  })
}

// 更换静态代理IP
export function replaceStaticProxyIP(id) {
  return request({
    url: '/admin/system/proxy/static/replaceIP/' + id,
    method: 'post'
  })
}

// 更新静态代理认证信息
export function updateStaticProxyCredentials(id, data) {
  return request({
    url: '/admin/system/proxy/static/credentials/' + id,
    method: 'put',
    data: data
  })
}

// 更新静态代理IP白名单
export function updateStaticProxyWhitelist(id, data) {
  return request({
    url: '/admin/system/proxy/static/whitelist/' + id,
    method: 'put',
    data: data
  })
}

// 获取动态代理Channel列表
export function listDynamicProxyChannel(query) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/list',
    method: 'get',
    params: query
  })
}

// 获取动态代理Channel详细
export function getDynamicProxyChannel(id) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/' + id,
    method: 'get'
  })
}

// 更新动态代理Channel设置
export function updateDynamicProxyChannel(id, data) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/' + id,
    method: 'put',
    data: data
  })
}

// 查看动态代理Channel流量使用情况
export function getDynamicProxyChannelTraffic(id) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/traffic/' + id,
    method: 'get'
  })
}

// 生成动态代理Endpoints
export function generateDynamicProxyEndpoints(id, data) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/generateEndpoints/' + id,
    method: 'post',
    data: data
  })
}

// 获取动态代理Endpoint列表
export function listDynamicProxyEndpoints(id) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/endpoints/' + id,
    method: 'get'
  })
}

// 删除动态代理Endpoint
export function deleteDynamicProxyEndpoint(channelId, endpointId) {
  return request({
    url: '/admin/system/proxy/dynamic/channel/endpoint/' + channelId + '/' + endpointId,
    method: 'delete'
  })
}
