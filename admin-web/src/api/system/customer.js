import request from '@/utils/request'

// 查询客户列表
export function listCustomer(query) {
  return request({
    url: '/system/customers',
    method: 'get',
    params: query
  })
}

// 查询客户详细
export function getCustomer(customerId) {
  return request({
    url: '/system/customers/' + customerId,
    method: 'get'
  })
}

// 新增客户
export function addCustomer(data) {
  return request({
    url: '/system/customers',
    method: 'post',
    data: data
  })
}

// 修改客户基本信息
export function updateCustomer(customerId, data) {
  return request({
    url: '/system/customers/' + customerId,
    method: 'put',
    data: data
  })
}

// 修改客户状态
export function updateCustomerStatus(customerId, status) {
  return request({
    url: '/system/customers/' + customerId + '/status',
    method: 'put',
    data: { status }
  })
}

// 获取客户订单
export function getCustomerOrders(customerId, params) {
  return request({
    url: '/system/customers/' + customerId + '/orders',
    method: 'get',
    params
  })
}

// 获取客户流量信息
export function getCustomerFlowInfo(customerId) {
  return request({
    url: '/system/customers/' + customerId + '/flow',
    method: 'get'
  })
}

// 获取客户流量使用记录
export function getCustomerFlowUsage(customerId, params) {
  return request({
    url: '/system/customers/' + customerId + '/flow/usage',
    method: 'get',
    params
  })
}

// 获取客户操作时间线
export function getCustomerTimeline(customerId, params) {
  return request({
    url: '/system/customers/' + customerId + '/timeline',
    method: 'get',
    params
  })
}

// 获取客户时间线统计信息
export function getCustomerTimelineStats(customerId) {
  return request({
    url: '/system/customers/' + customerId + '/timeline/stats',
    method: 'get'
  })
}
