import request from '@/utils/request'

// IP段分配
export function allocateIpSegments(data) {
  return request({
    url: '/products/ip-segments/allocate',
    method: 'post',
    data: data
  })
}

// IP段回收
export function deallocateIpSegments(data) {
  return request({
    url: '/products/ip-segments/deallocate',
    method: 'post',
    data: data
  })
}

// 获取IP段列表
export function listIpSegments(query) {
  return request({
    url: '/products/ip-segments/list',
    method: 'get',
    params: query
  })
}

// 获取IP段统计
export function getIpSegmentStats(productId) {
  return request({
    url: '/products/ip-segments/stats',
    method: 'get',
    params: { productId }
  })
}

// 获取产品IP段
export function getProductIpSegments(productId) {
  return request({
    url: `/products/ip-segments/product/${productId}`,
    method: 'get'
  })
}

// 更新IP段状态
export function updateIpSegmentStatus(segmentId, data) {
  return request({
    url: `/products/ip-segments/${segmentId}/status`,
    method: 'put',
    data: data
  })
}

// IP段重平衡
export function rebalanceIpSegments(data) {
  return request({
    url: '/products/ip-segments/rebalance',
    method: 'post',
    data: data
  })
}

// 获取仪表板数据
export function getIpManagementDashboard() {
  return request({
    url: '/products/ip-segments/dashboard',
    method: 'get'
  })
}

// 获取单个IP段详情
export function getIpSegment(segmentId) {
  return request({
    url: `/products/ip-segments/${segmentId}`,
    method: 'get'
  })
}

// 更新IP段信息
export function updateIpSegment(segmentId, data) {
  return request({
    url: `/products/ip-segments/${segmentId}`,
    method: 'put',
    data: data
  })
}