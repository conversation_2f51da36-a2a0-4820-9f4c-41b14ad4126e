import request from '@/utils/request'

// 查询供应商城市映射列表
export function listMapping(query) {
  return request({
    url: '/system/location/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询供应商城市映射详细
export function getMapping(id) {
  return request({
    url: '/system/location/mapping/' + id,
    method: 'get'
  })
}

// 新增供应商城市映射
export function addMapping(data) {
  return request({
    url: '/system/location/mapping',
    method: 'post',
    data: data
  })
}

// 修改供应商城市映射
export function updateMapping(data) {
  return request({
    url: '/system/location/mapping/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除供应商城市映射
export function delMapping(ids) {
  return request({
    url: '/system/location/mapping/' + ids,
    method: 'delete'
  })
}

// 更新映射状态
export function updateMappingStatus(id, status) {
  return request({
    url: `/system/location/mapping/${id}/status/${status}`,
    method: 'put'
  })
}

// 导出供应商城市映射
export function exportMapping(query) {
  return request({
    url: '/system/location/mapping/export',
    method: 'post',
    params: query
  })
}

// 根据供应商ID和原始城市ID查询映射
export function getMappingByProviderAndNativeCity(providerId, nativeCityId) {
  return request({
    url: `/system/location/mapping/provider/${providerId}/native/${nativeCityId}`,
    method: 'get'
  })
}

// 根据供应商ID和标准城市ID查询映射
export function getMappingByProviderAndStandardCity(providerId, standardCityId) {
  return request({
    url: `/system/location/mapping/provider/${providerId}/standard/${standardCityId}`,
    method: 'get'
  })
}