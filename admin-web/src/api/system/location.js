import request from '@/utils/request'

// 区域管理接口
export function listRegion(query) {
  return request({
    url: '/location/region/list',
    method: 'get',
    params: query
  })
}

export function getRegion(id) {
  return request({
    url: '/location/region/' + id,
    method: 'get'
  })
}

export function addRegion(data) {
  return request({
    url: '/location/region',
    method: 'post',
    data: data
  })
}

export function updateRegion(data) {
  return request({
    url: '/location/region/' + data.id,
    method: 'patch',
    data: data
  })
}

export function delRegion(id) {
  return request({
    url: '/location/region/' + id,
    method: 'delete'
  })
}

export function getAllRegions() {
  return request({
    url: '/location/region/all',
    method: 'get'
  })
}

// 国家管理接口
export function listCountry(query) {
  return request({
    url: '/location/country/list',
    method: 'get',
    params: query
  })
}

export function getCountry(id) {
  return request({
    url: '/location/country/' + id,
    method: 'get'
  })
}

export function addCountry(data) {
  return request({
    url: '/location/country',
    method: 'post',
    data: data
  })
}

export function updateCountry(data) {
  return request({
    url: '/location/country/' + data.id,
    method: 'patch',
    data: data
  })
}

export function delCountry(id) {
  return request({
    url: '/location/country/' + id,
    method: 'delete'
  })
}

export function getAllCountries() {
  return request({
    url: '/location/country/all',
    method: 'get'
  })
}

export function getCountriesByRegion(regionId) {
  return request({
    url: '/location/country/region/' + regionId,
    method: 'get'
  })
}

// 城市管理接口
export function listCity(query) {
  return request({
    url: '/location/city/list',
    method: 'get',
    params: query
  })
}

export function getCity(id) {
  return request({
    url: '/location/city/' + id,
    method: 'get'
  })
}

export function addCity(data) {
  return request({
    url: '/location/city',
    method: 'post',
    data: data
  })
}

export function updateCity(data) {
  return request({
    url: '/location/city/' + data.id,
    method: 'patch',
    data: data
  })
}

export function delCity(id) {
  return request({
    url: '/location/city/' + id,
    method: 'delete'
  })
}

export function getCitiesByCountry(countryId) {
  return request({
    url: '/location/city/country/' + countryId,
    method: 'get'
  })
}

export function getHotCities() {
  return request({
    url: '/location/city/hot',
    method: 'get'
  })
}

// 数据导入接口
export function parseLocationFile(data) {
  return request({
    url: '/location/parse',
    method: 'post',
    data: data
  })
}

export function importLocationData(data) {
  return request({
    url: '/location/import',
    method: 'post',
    data: data
  })
}

// 获取位置数据统计
export function getLocationStatistics() {
  return request({
    url: '/location/statistics',
    method: 'get'
  })
}

// 预览清除数据
export function previewClearData(params) {
  return request({
    url: '/location/preview-clear',
    method: 'post',
    data: params
  })
}

// 清除位置数据
export function clearLocationData(params) {
  return request({
    url: '/location/clear',
    method: 'post',
    data: params
  })
}

// 产品编辑页面专用接口
export function listLocationRegions() {
  return getAllRegions();
}

export function listLocationCountries(query = {}) {
  if (query.regionName) {
    // 根据区域名称查询国家
    return request({
      url: '/location/country/list',
      method: 'get',
      params: { regionName: query.regionName }
    });
  }
  return getAllCountries();
}

export function listLocationCities(query = {}) {
  if (query.countryName) {
    // 根据国家名称查询城市
    return request({
      url: '/location/city/list',
      method: 'get',
      params: { countryName: query.countryName }
    });
  }
  return request({
    url: '/location/city/list',
    method: 'get',
    params: query
  });
}

// 搜索标准化城市（用于产品编辑页面）
export function searchStandardCities(query) {
  return request({
    url: '/location/city/search',
    method: 'get',
    params: {
      keyword: query.keyword || '',
      pageSize: query.pageSize || 50,
      pageNum: query.pageNum || 1,
      status: '0' // 只查询启用的城市
    }
  })
}

// 获取城市详情（用于产品编辑页面）
export function getCityDetails(cityId) {
  return request({
    url: '/location/city/' + cityId,
    method: 'get'
  })
}

// 批量搜索城市（用于产品管理页面过滤）
export function batchSearchCities(query) {
  return request({
    url: '/location/city/batch-search',
    method: 'get',
    params: {
      regionName: query.regionName,
      countryName: query.countryName,
      cityName: query.cityName,
      pageSize: query.pageSize || 100,
      status: '0'
    }
  })
}

// ==================== 新增搜索接口 ====================

// 区域搜索接口
export function searchRegions(query) {
  return request({
    url: '/location/region/search',
    method: 'get',
    params: query
  })
}

// 国家搜索接口
export function searchCountries(query) {
  return request({
    url: '/location/country/search',
    method: 'get',
    params: query
  })
}

// 城市搜索接口
export function searchCities(query) {
  return request({
    url: '/location/city/search',
    method: 'get',
    params: query
  })
}

// 统一位置搜索接口
export function unifiedLocationSearch(query) {
  return request({
    url: '/location/unified-search',
    method: 'get',
    params: query
  })
}

// 清除搜索缓存
export function clearSearchCache() {
  return request({
    url: '/location/search/clear-cache',
    method: 'post'
  })
}

