import request from '@/utils/request'

// 登录方法
export function login(userName, password, code, uuid) {
  const data = {
    userName,
    password,
    code,
    uuid
  }
  return request({
    url: '/user-management/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/user-management/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/user-management/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/user-management/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/user-management/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get'
  })
}

// 是否开启用户注册功能
export function getRegisterUser() {
  return request({
    url: '/user-management/registerUser',
    headers: {
      isToken: false
    },
    method: 'get'
  })
}
