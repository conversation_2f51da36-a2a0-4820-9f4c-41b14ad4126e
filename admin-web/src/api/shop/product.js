import request from '@/utils/request'

// 查询产品列表
export function listProduct(query) {
  return request({
    url: '/system/products',
    method: 'get',
    params: query
  })
}

// 查询产品详细
export function getProduct(productId) {
  return request({
    url: '/system/products/' + productId,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data) {
  return request({
    url: '/system/products',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: '/system/products/' + (data.id || data.productId), // 优先使用id，兼容productId
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(productId) {
  return request({
    url: '/system/products/' + productId,
    method: 'delete'
  })
}

// 修改产品状态
export function changeProductStatus(productId, status) {
  return request({
    url: `/system/products/${productId}/status`,
    method: 'put',
    data: { status }
  })
}

// 导出产品
export function exportProduct(query) {
  return request({
    url: '/system/products/export',
    method: 'get',
    params: query
  })
}

// 同步产品价格
export function syncProducts(data) {
  return request({
    url: '/system/products/sync',
    method: 'post',
    data: data
  })
}

// 获取同步状态
export function getSyncStatus() {
  return request({
    url: '/system/products/sync/status',
    method: 'get'
  })
}

// 停止同步任务
export function stopSyncTask() {
  return request({
    url: '/system/products/sync-stop',
    method: 'post'
  })
}

// 清除同步状态
export function clearSyncStatus() {
  return request({
    url: '/system/products/sync-clear',
    method: 'post'
  })
}

// 手动更新价格
export function manualUpdatePrice(data) {
  return request({
    url: '/system/products/manual-price-update',
    method: 'post',
    data: data
  })
}

// 获取价格历史
export function getPriceHistory(productId, params) {
  return request({
    url: '/system/products/price-history/' + productId,
    method: 'get',
    params: params
  })
}

// 查询产品管理列表（包含扩展字段）
export function listProductManagement(query) {
  return request({
    url: '/system/products/management',
    method: 'get',
    params: query
  })
}

// 批量上下架产品
export function batchUpdateStatus(data) {
  return request({
    url: '/system/products/batch-status',
    method: 'post',
    data: data
  })
}

// 批量同步产品
export function batchSyncProducts(data) {
  return request({
    url: '/system/products/batch-sync',
    method: 'post',
    data: data
  })
}

// 批量更新同步策略
export function batchUpdateSyncStrategy(data) {
  return request({
    url: '/system/products/batch-sync-strategy',
    method: 'post',
    data: data
  })
}

// 获取产品IP段信息
export function getProductIpSegments(productId) {
  return request({
    url: '/system/products/ip-segment/' + productId,
    method: 'get'
  })
}

// 获取同步历史列表
export function getSyncHistoryList(query) {
  return request({
    url: '/system/sync-history',
    method: 'get',
    params: query
  })
}

// 获取同步历史详情
export function getSyncHistoryDetail(syncId) {
  return request({
    url: '/system/sync-history/' + syncId,
    method: 'get'
  })
}

// 获取同步产品详情
export function getSyncProductDetails(syncId, query) {
  return request({
    url: '/system/sync-history/' + syncId + '/details',
    method: 'get',
    params: query
  })
}

// 获取最近同步的产品
export function getRecentSyncedProducts(query) {
  return request({
    url: '/system/sync-history/recent/products',
    method: 'get',
    params: query
  })
}

// 分配IP段给产品
export function allocateIpSegment(data) {
  return request({
    url: '/system/products/ip-segment/allocate',
    method: 'post',
    data: data
  })
}

// 批量更新库存状态
export function updateInventoryStatus() {
  return request({
    url: '/system/products/inventory/update',
    method: 'post'
  })
}
