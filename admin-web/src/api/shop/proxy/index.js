import request from '@/utils/request'

// 查询代理实例列表
export function listProxyInstance(query) {
  return request({
    url: '/system/proxy/instances',
    method: 'get',
    params: query
  })
}

// 查询代理实例详细
export function getProxyInstance(instanceId) {
  return request({
    url: '/system/proxy/instances/' + instanceId,
    method: 'get'
  })
}

// 删除代理实例
export function delProxyInstance(instanceId) {
  return request({
    url: '/system/proxy/instances/' + instanceId,
    method: 'delete'
  })
}

// 刷新代理实例状态
export function refreshProxyInstanceStatus(instanceId) {
  return request({
    url: `/system/proxy/instances/${instanceId}/refresh-status`,
    method: 'post'
  })
}

// 替换静态IP
export function replaceStaticIP(instanceId, data) {
  return request({
    url: `/system/proxy/static/${instanceId}/replace`,
    method: 'post',
    data
  })
}

// 更新静态IP凭证
export function updateStaticCredentials(instanceId, data) {
  return request({
    url: `/system/proxy/static/${instanceId}/credentials`,
    method: 'post',
    data
  })
}

// 更新静态IP白名单
export function updateStaticWhitelist(instanceId, whitelistedIps) {
  return request({
    url: `/system/proxy/static/${instanceId}/whitelist`,
    method: 'post',
    data: { whitelistedIps }
  })
}

// 获取动态Channel流量使用
export function getDynamicProxyChannelTraffic(instanceId, params) {
  return request({
    url: `/system/proxy/dynamic/${instanceId}/traffic`,
    method: 'get',
    params
  })
}

// 为动态Channel生成Endpoints
export function generateDynamicProxyEndpoints(instanceId, data) {
  return request({
    url: `/system/proxy/dynamic/${instanceId}/endpoints`,
    method: 'post',
    data
  })
}

// 获取动态Channel的Endpoints列表
export function listDynamicProxyEndpoints(instanceId) {
  return request({
    url: `/system/proxy/dynamic/${instanceId}/endpoints`,
    method: 'get'
  })
}

// 更新动态Channel
export function updateDynamicProxyChannel(instanceId, data) {
  return request({
    url: `/system/proxy/dynamic/${instanceId}`,
    method: 'put', // Assuming it's a PUT request for updates
    data
  })
}

// 删除Endpoint
export function deleteDynamicProxyEndpoint(endpointId) {
  return request({
    url: `/system/proxy/dynamic/endpoints/${endpointId}`,
    method: 'delete'
  })
}
