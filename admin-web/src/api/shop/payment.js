import request from '@/utils/request'

// 查询支付记录列表
export function listPayment(query) {
  return request({
    url: '/system/payment/list',
    method: 'get',
    params: query
  })
}

// 查询支付记录详细
export function getPayment(paymentId) {
  return request({
    url: '/system/payment/' + paymentId,
    method: 'get'
  })
}

// 获取支付配置
export function getPaymentConfig() {
  return request({
    url: '/system/payment/config',
    method: 'get'
  })
}

// 更新支付配置
export function updatePaymentConfig(data) {
  return request({
    url: '/system/payment/config',
    method: 'put',
    data: data
  })
}

// 查询支付统计数据
export function getPaymentStats(params) {
  return request({
    url: '/system/payment/stats',
    method: 'get',
    params: params
  })
}

// 手动同步支付状态
export function syncPaymentStatus(orderId) {
  return request({
    url: '/system/payment/sync/' + orderId,
    method: 'post'
  })
}

// 获取或刷新支付二维码
export function getPaymentQRCode(transactionId) {
  return request({
    url: '/system/payment/qrcode/' + transactionId,
    method: 'get'
  })
}

// 重新生成支付二维码（废弃的交易）
export function regeneratePaymentQRCode(transactionId) {
  return request({
    url: '/system/payment/qrcode/' + transactionId + '/regenerate',
    method: 'post'
  })
}
