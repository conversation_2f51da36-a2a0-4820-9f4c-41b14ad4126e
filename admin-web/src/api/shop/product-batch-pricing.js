import request from '@/utils/request'

/**
 * 批量调价预览
 * @param {Object} data 预览参数
 * @param {Array} data.productIds 产品ID列表
 * @param {string} data.priceTarget 调价目标 ('price', 'discountPrice', 'both')
 * @param {string} data.adjustmentType 调价方式 ('fixed', 'percentage', 'amount')
 * @param {number} data.adjustmentValue 调整值
 */
export function batchUpdatePricesPreview(data) {
  return request({
    url: '/system/products/batch-update-prices/preview',
    method: 'post',
    data: data
  })
}

/**
 * 批量调价
 * @param {Object} data 调价参数
 * @param {Array} data.productIds 产品ID列表
 * @param {string} data.priceTarget 调价目标 ('price', 'discountPrice', 'both')
 * @param {string} data.adjustmentType 调价方式 ('fixed', 'percentage', 'amount')
 * @param {number} data.adjustmentValue 调整值
 * @param {string} data.reason 调价原因
 */
export function batchUpdatePrices(data) {
  return request({
    url: '/system/products/batch-update-prices',
    method: 'post',
    data: data
  })
}

/**
 * 调价目标类型枚举
 */
export const PriceTargetType = {
  SALE_PRICE: 'price',
  DISCOUNT_PRICE: 'discountPrice',
  BOTH: 'both'
}

/**
 * 调价方式类型枚举
 */
export const PriceAdjustmentType = {
  FIXED_VALUE: 'fixed',
  PERCENTAGE: 'percentage',
  AMOUNT: 'amount',
  COST_BASED: 'cost_based'
}

/**
 * 调价目标选项（用于前端下拉框）
 */
export const priceTargetOptions = [
  { label: '售价', value: PriceTargetType.SALE_PRICE },
  { label: '折扣价', value: PriceTargetType.DISCOUNT_PRICE },
  { label: '同时调整', value: PriceTargetType.BOTH }
]

/**
 * 调价方式选项（用于前端下拉框）
 */
export const adjustmentTypeOptions = [
  {
    label: '设置为固定值',
    value: PriceAdjustmentType.FIXED_VALUE,
    description: '将所有选中产品的价格直接设置为输入的固定价格',
    scenario: '统一促销价：全场T恤衫99元',
    placeholder: '请输入固定价格，如：99.99',
    unit: '元'
  },
  {
    label: '按百分比调整',
    value: PriceAdjustmentType.PERCENTAGE,
    description: '在当前价格基础上按百分比上浮或下调价格',
    scenario: '全场打折：输入-20表示8折，输入10表示涨价10%',
    placeholder: '请输入百分比，如：-15（表示85折）',
    unit: '%'
  },
  {
    label: '按固定金额调整',
    value: PriceAdjustmentType.AMOUNT,
    description: '在当前价格基础上增加或减少一个固定金额',
    scenario: '成本上涨：所有商品涨价5元，输入5；降价促销：减价10元，输入-10',
    placeholder: '请输入调整金额，如：-10（降价10元）',
    unit: '元'
  },
  {
    label: '基于成本定价',
    value: PriceAdjustmentType.COST_BASED,
    description: '根据产品成本价和指定的毛利率计算售价。公式：售价 = 成本 ÷ (1 - 毛利率%)，毛利率范围：0-99%',
    scenario: '保证利润',
    placeholder: '请输入毛利率0-99，如：30（表示30%毛利率）',
    unit: '%'
  }
]

/**
 * 获取调价方式的单位显示
 * @param {string} adjustmentType 调价方式
 * @returns {string} 单位字符串
 */
export function getAdjustmentUnit(adjustmentType) {
  const option = adjustmentTypeOptions.find((opt) => opt.value === adjustmentType)
  return option ? option.unit : ''
}

/**
 * 获取调价方式的输入提示
 * @param {string} adjustmentType 调价方式
 * @returns {string} 提示字符串
 */
export function getAdjustmentPlaceholder(adjustmentType) {
  const option = adjustmentTypeOptions.find((opt) => opt.value === adjustmentType)
  return option ? option.placeholder : '请输入调整值'
}

/**
 * 获取调价方式的描述
 * @param {string} adjustmentType 调价方式
 * @returns {string} 描述字符串
 */
export function getAdjustmentDescription(adjustmentType) {
  const option = adjustmentTypeOptions.find((opt) => opt.value === adjustmentType)
  return option ? option.description : ''
}

/**
 * 获取调价方式的使用场景示例
 * @param {string} adjustmentType 调价方式
 * @returns {string} 场景示例字符串
 */
export function getAdjustmentScenario(adjustmentType) {
  const option = adjustmentTypeOptions.find((opt) => opt.value === adjustmentType)
  return option ? option.scenario : ''
}

/**
 * 格式化价格变化显示
 * @param {number} priceChange 价格变化
 * @param {number} priceChangePercentage 价格变化百分比
 * @returns {string} 格式化后的字符串
 */
export function formatPriceChange(priceChange, priceChangePercentage) {
  const changeText = priceChange > 0 ? '+' : ''
  const percentageText = priceChangePercentage > 0 ? '+' : ''

  return `${changeText}${priceChange.toFixed(2)}元 (${percentageText}${priceChangePercentage.toFixed(2)}%)`
}

/**
 * 验证调价参数
 * @param {Object} data 调价参数
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export function validateBatchPricingData(data) {
  if (!data.productIds || data.productIds.length === 0) {
    return { valid: false, message: '请选择要调价的产品' }
  }

  if (!data.priceTarget) {
    return { valid: false, message: '请选择调价目标' }
  }

  if (!data.adjustmentType) {
    return { valid: false, message: '请选择调价方式' }
  }

  if (data.adjustmentValue === undefined || data.adjustmentValue === null || data.adjustmentValue === '') {
    return { valid: false, message: '请输入调整值' }
  }

  const adjustmentValue = Number(data.adjustmentValue)
  if (isNaN(adjustmentValue)) {
    return { valid: false, message: '调整值必须为数字' }
  }

  // 固定值不能为负数或零
  if (data.adjustmentType === PriceAdjustmentType.FIXED_VALUE && adjustmentValue <= 0) {
    return { valid: false, message: '固定价格必须大于0' }
  }

  // 百分比不能小于-100%
  if (data.adjustmentType === PriceAdjustmentType.PERCENTAGE && adjustmentValue <= -100) {
    return { valid: false, message: '百分比调整不能小于-100%' }
  }

  // 毛利率验证
  if (data.adjustmentType === PriceAdjustmentType.COST_BASED) {
    if (adjustmentValue >= 100) {
      return { valid: false, message: '毛利率不能大于等于100%' }
    }
    if (adjustmentValue < 0) {
      return { valid: false, message: '毛利率不能为负数' }
    }
  }

  // 调价原因为选填，如果填写则需要检查长度
  if (data.reason && data.reason.length > 200) {
    return { valid: false, message: '调价原因长度不能超过200个字符' }
  }

  return { valid: true, message: '' }
}
