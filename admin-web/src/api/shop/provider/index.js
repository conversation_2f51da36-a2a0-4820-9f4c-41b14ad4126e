import request from '@/utils/request'

// 查询供应商列表
export function listProvider(query) {
  return request({
    url: '/system/providers',
    method: 'get',
    params: query
  })
}

// 查询供应商详细
export function getProvider(providerId) {
  return request({
    url: '/system/providers/' + providerId,
    method: 'get'
  })
}

// 新增供应商
export function addProvider(data) {
  return request({
    url: '/system/providers',
    method: 'post',
    data: data
  })
}

// 修改供应商
export function updateProvider(data) {
  return request({
    url: '/system/providers/' + data.providerId,
    method: 'put',
    data: data
  })
}

// 删除供应商
export function delProvider(providerId) {
  return request({
    url: '/system/providers/' + providerId,
    method: 'delete'
  })
}

// 修改供应商状态
export function changeProviderStatus(providerId, status) {
  return request({
    url: `/system/providers/${providerId}/status`,
    method: 'put',
    data: { status }
  })
}

// 测试供应商API连接
export function testProviderConnection(providerId) {
  return request({
    url: `/system/providers/${providerId}/test-connection`,
    method: 'post'
  })
}
