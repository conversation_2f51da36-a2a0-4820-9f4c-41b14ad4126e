import request from '@/utils/request'

// 查询订单列表（支持订单类型筛选）
export function listOrder(query) {
  // 确保page和limit是数字类型
  const params = { ...query }
  if (params.page) params.page = Number(params.page)
  if (params.limit) params.limit = Number(params.limit)

  return request({
    url: '/system/orders',
    method: 'get',
    params: {
      ...params,
      // 确保orderType参数传递
      orderType: params.orderType || undefined
    }
  })
}

// 查询订单详细
export function getOrder(orderId) {
  return request({
    url: `/system/orders/${orderId}`,
    method: 'get'
  }).then((response) => response)
}

// 更新订单状态
export function updateOrderStatus(data) {
  return request({
    url: `/system/orders/${data.orderId}/status`,
    method: 'put',
    data: data
  })
}

// 订单退款
export function refundOrder(data) {
  // 将前端字段名映射为后端期望的字段名
  const mappedData = {
    amount: data.refundAmount,
    reason: data.refundReason,
    transactionId: data.transactionId // 添加交易号参数
  }

  return request({
    url: `/system/orders/${data.orderId}/refund`,
    method: 'put',
    data: mappedData
  })
}


// 取消订单
export function cancelOrder(orderId) {
  return request({
    url: `/system/orders/${orderId}/cancel`,
    method: 'put'
  })
}

// 新增：创建充值订单（管理员操作）
export function createRechargeOrder(data) {
  return request({
    url: '/system/orders/recharge',
    method: 'post',
    data: data
  })
}

// 新增：查询订单关联交易
export function getOrderTransaction(orderId) {
  return request({
    url: `/system/orders/${orderId}/transaction`,
    method: 'get'
  })
}

// 新增：获取订单支付状态
export function getOrderPaymentStatus(orderId) {
  return request({
    url: `/system/orders/${orderId}/payment-status`,
    method: 'get'
  })
}

// 批量取消订单
export function batchCancelOrder(orderIds) {
  return request({
    url: '/system/orders/batch-cancel',
    method: 'put',
    data: { orderIds }
  })
}

// 导出订单（支持订单类型筛选）
export function exportOrder(query) {
  return request({
    url: '/system/orders/export',
    method: 'get',
    params: query
  })
}
