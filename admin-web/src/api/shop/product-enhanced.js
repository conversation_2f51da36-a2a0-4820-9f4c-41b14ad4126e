import request from '@/utils/request'

// 产品管理API（增强版）

// 更新产品价格
export function updateProductPrice(data) {
  return request({
    url: '/system/products/' + data.productId,
    method: 'put',
    data: {
      price: data.price,
      // 其他字段保持不变，只更新价格
      _priceUpdateReason: data.reason // 价格更新原因（可选）
    }
  })
}

// 恢复产品为自动定价
export function revertProductToAutoPricing(productId) {
  return request({
    url: '/system/products/' + productId + '/actions/revert-to-auto-pricing',
    method: 'post'
  })
}

// 批量重新计算所有产品价格
export function recalculateAllPrices() {
  return request({
    url: '/system/products/tasks/recalculate-prices',
    method: 'post'
  })
}

// 获取产品价格历史
export function getProductPriceHistory(productId, query) {
  return request({
    url: '/system/products/' + productId + '/price-history',
    method: 'get',
    params: query
  })
}

// 获取价格统计信息
export function getProductPriceStats(query) {
  return request({
    url: '/system/products/price-stats',
    method: 'get',
    params: query
  })
}

// 批量更新产品价格
export function batchUpdateProductPrices(data) {
  return request({
    url: '/system/products/batch/update-prices',
    method: 'post',
    data: data
  })
}

// 批量恢复自动定价
export function batchRevertToAutoPricing(productIds) {
  return request({
    url: '/system/products/batch/revert-to-auto-pricing',
    method: 'post',
    data: {
      productIds: productIds
    }
  })
}

// 获取定价配置
export function getPricingConfig() {
  return request({
    url: '/system/config/pricing',
    method: 'get'
  })
}

// 更新定价配置
export function updatePricingConfig(data) {
  return request({
    url: '/system/config/pricing',
    method: 'put',
    data: data
  })
}

// 模拟价格计算（用于预览）
export function simulatePriceCalculation(data) {
  return request({
    url: '/system/products/simulate-price',
    method: 'post',
    data: data
  })
}

// 导出价格报告
export function exportPriceReport(query) {
  return request({
    url: '/system/products/export/price-report',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}