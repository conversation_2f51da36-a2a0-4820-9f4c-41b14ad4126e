import request from '@/utils/request'

// 创建充值交易
export function createRechargeTransaction(data) {
  return request({
    url: '/transactions/recharge',
    method: 'post',
    data: data
  })
}

// 获取充值二维码
export function getRechargeQRCode(transactionId) {
  return request({
    url: `/transactions/recharge/${transactionId}/qrcode`,
    method: 'get'
  })
}

// 模拟支付回调（测试用）
export function simulatePaymentCallback(data) {
  return request({
    url: '/transactions/recharge/webhook/payment',
    method: 'post',
    data: data
  })
}

// 获取充值统计
export function getRechargeStats() {
  return request({
    url: '/transactions/recharge/stats',
    method: 'get'
  })
}