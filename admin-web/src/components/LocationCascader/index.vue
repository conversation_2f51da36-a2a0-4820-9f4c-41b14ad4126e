<template>
  <div class="location-cascader">
    <!-- 大洲/区域选择器 -->
    <el-select
      v-model="selectedRegion"
      :placeholder="placeholders.region"
      :loading="regionLoading"
      clearable
      filterable
      @change="handleRegionChange"
      :style="{ width: width.region, marginRight: '8px' }"
    >
      <el-option
        v-for="item in regionOptions"
        :key="item.id"
        :label="item.regionName"
        :value="item.id"
      >
        <div class="region-option">
          <span>{{ item.regionName }}</span>
          <el-tag size="small" type="info" style="margin-left: 8px">
            {{ item.countryCount || 0 }}个国家
          </el-tag>
        </div>
      </el-option>
    </el-select>

    <!-- 国家选择器 -->
    <el-select
      v-model="selectedCountry"
      :placeholder="placeholders.country"
      :loading="countryLoading"
      :disabled="!selectedRegion"
      clearable
      filterable
      @change="handleCountryChange"
      :style="{ width: width.country, marginRight: '8px' }"
    >
      <el-option
        v-for="item in countryOptions"
        :key="item.id"
        :label="item.countryName"
        :value="item.id"
      >
        <div class="country-option">
          <img
            v-if="item.countryCode"
            :src="`https://flagcdn.com/16x12/${item.countryCode.toLowerCase()}.png`"
            style="width: 16px; height: 12px; margin-right: 6px;"
            @error="$event.target.style.display = 'none'"
          />
          <span>{{ item.countryName }}</span>
          <el-tag size="small" type="info" style="margin-left: 8px">
            {{ item.cityCount || 0 }}个城市
          </el-tag>
        </div>
      </el-option>
    </el-select>

    <!-- 城市选择器 -->
    <el-select
      v-model="selectedCity"
      :placeholder="placeholders.city"
      :loading="cityLoading"
      :disabled="!selectedCountry"
      clearable
      filterable
      @change="handleCityChange"
      :style="{ width: width.city }"
    >
      <el-option
        v-for="item in cityOptions"
        :key="item.id"
        :label="item.displayName"
        :value="item.id"
      >
        <div class="city-option">
          <img
            v-if="item.countryCode"
            :src="`https://flagcdn.com/16x12/${item.countryCode.toLowerCase()}.png`"
            style="width: 16px; height: 12px; margin-right: 6px;"
            @error="$event.target.style.display = 'none'"
          />
          <span>{{ item.cityName }}</span>
          <el-tag size="small" type="info" style="margin-left: 6px">
            {{ item.regionName }}
          </el-tag>
        </div>
      </el-option>
    </el-select>

    <!-- 清空按钮 -->
    <el-button 
      v-if="clearable && hasSelection"
      type="text" 
      icon="Close" 
      @click="handleClearAll"
      style="margin-left: 8px;"
      title="清空所有筛选"
    />
  </div>
</template>

<script>
import { ref, watch, onMounted, computed } from 'vue'
import { listRegion, listCountry, listCity } from '@/api/system/location'

export default {
  name: 'LocationCascader',
  props: {
    // v-model 支持
    modelValue: {
      type: Object,
      default: () => ({ region: null, country: null, city: null })
    },
    // 占位符配置
    placeholders: {
      type: Object,
      default: () => ({
        region: '请选择大洲/区域',
        country: '请选择国家',
        city: '请选择城市'
      })
    },
    // 宽度配置
    width: {
      type: Object,
      default: () => ({
        region: '140px',
        country: '150px', 
        city: '180px'
      })
    },
    // 是否显示清空按钮
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否在初始化时加载热门数据
    loadHotOnInit: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // 内部选择状态
    const selectedRegion = ref(null)
    const selectedCountry = ref(null) 
    const selectedCity = ref(null)

    // 选项列表
    const regionOptions = ref([])
    const countryOptions = ref([])
    const cityOptions = ref([])

    // 加载状态
    const regionLoading = ref(false)
    const countryLoading = ref(false)
    const cityLoading = ref(false)

    // 缓存机制
    const countryCache = new Map()
    const cityCache = new Map()

    // 计算是否有选择
    const hasSelection = computed(() => {
      return selectedRegion.value || selectedCountry.value || selectedCity.value
    })

    // 监听父组件传入的值变化
    watch(() => props.modelValue, (newValue) => {
      if (newValue) {
        selectedRegion.value = newValue.region
        selectedCountry.value = newValue.country
        selectedCity.value = newValue.city

        // 如果有初始值，需要级联加载对应的选项
        if (newValue.region && newValue.region !== selectedRegion.value) {
          loadCountries(newValue.region)
        }
        if (newValue.country && newValue.country !== selectedCountry.value) {
          loadCities(newValue.country)
        }
      }
    }, { immediate: true, deep: true })

    // 数据加载方法
    const loadRegions = async () => {
      regionLoading.value = true
      try {
        const response = await listRegion({ pageNum: 1, pageSize: 100 })
        regionOptions.value = response.rows || []
      } catch (error) {
        console.error('Failed to fetch regions:', error)
        regionOptions.value = []
      } finally {
        regionLoading.value = false
      }
    }

    const loadCountries = async (regionId) => {
      if (!regionId) {
        countryOptions.value = []
        return
      }

      // 检查缓存
      if (countryCache.has(regionId)) {
        countryOptions.value = countryCache.get(regionId)
        return
      }

      countryLoading.value = true
      try {
        const response = await listCountry({ 
          pageNum: 1, 
          pageSize: 200,
          regionId: regionId
        })
        const countries = response.rows || []
        countryOptions.value = countries
        countryCache.set(regionId, countries)
      } catch (error) {
        console.error('Failed to fetch countries:', error)
        countryOptions.value = []
      } finally {
        countryLoading.value = false
      }
    }

    const loadCities = async (countryId) => {
      if (!countryId) {
        cityOptions.value = []
        return
      }

      // 检查缓存
      if (cityCache.has(countryId)) {
        cityOptions.value = cityCache.get(countryId)
        return
      }

      cityLoading.value = true
      try {
        const response = await listCity({ 
          pageNum: 1, 
          pageSize: 500,
          countryId: countryId
        })
        const cities = response.rows || []
        
        // 格式化城市数据，添加 displayName
        const formattedCities = cities.map(city => ({
          ...city,
          displayName: `${city.cityName}${city.country?.countryName ? ` (${city.country.countryName})` : ''}`,
          countryCode: city.country?.countryCode,
          countryName: city.country?.countryName,
          regionName: city.country?.region?.regionName
        }))
        
        cityOptions.value = formattedCities
        cityCache.set(countryId, formattedCities)
      } catch (error) {
        console.error('Failed to fetch cities:', error)
        cityOptions.value = []
      } finally {
        cityLoading.value = false
      }
    }

    // 事件处理方法
    const handleRegionChange = (regionId) => {
      // 清空下级选择
      selectedCountry.value = null
      selectedCity.value = null
      countryOptions.value = []
      cityOptions.value = []

      // 加载国家列表
      if (regionId) {
        loadCountries(regionId)
      }

      emitChange()
    }

    const handleCountryChange = (countryId) => {
      // 清空下级选择
      selectedCity.value = null
      cityOptions.value = []

      // 加载城市列表
      if (countryId) {
        loadCities(countryId)
      }

      emitChange()
    }

    const handleCityChange = () => {
      emitChange()
    }

    const handleClearAll = () => {
      selectedRegion.value = null
      selectedCountry.value = null
      selectedCity.value = null
      countryOptions.value = []
      cityOptions.value = []
      emitChange()
    }

    // 发射选择变化事件
    const emitChange = () => {
      const selection = {
        region: selectedRegion.value,
        country: selectedCountry.value,
        city: selectedCity.value
      }

      emit('update:modelValue', selection)
      emit('change', selection)
    }

    // 组件挂载时初始化
    onMounted(async () => {
      await loadRegions()
      
      // 如果有初始值，级联加载
      if (props.modelValue?.region) {
        await loadCountries(props.modelValue.region)
      }
      if (props.modelValue?.country) {
        await loadCities(props.modelValue.country)
      }
    })

    return {
      selectedRegion,
      selectedCountry,
      selectedCity,
      regionOptions,
      countryOptions,
      cityOptions,
      regionLoading,
      countryLoading,
      cityLoading,
      hasSelection,
      handleRegionChange,
      handleCountryChange,
      handleCityChange,
      handleClearAll
    }
  }
}
</script>

<style scoped>
.location-cascader {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.region-option,
.country-option,
.city-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.location-cascader .el-select {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .location-cascader {
    flex-direction: column;
    align-items: stretch;
  }
  
  .location-cascader .el-select {
    margin-bottom: 8px;
    margin-right: 0 !important;
  }
}
</style>