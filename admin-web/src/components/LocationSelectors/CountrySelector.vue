<template>
  <el-select
    v-model="selectedCountry"
    :placeholder="placeholder"
    :loading="loading"
    :size="size"
    :disabled="!regionId && requireRegion"
    filterable
    remote
    reserve-keyword
    :remote-method="searchCountries"
    @change="handleChange"
    @focus="handleFocus"
    @clear="handleClear"
    clearable
    :style="{ width: width }"
  >
    <el-option
      v-for="country in countryOptions"
      :key="country.id"
      :label="country.countryName"
      :value="country.id"
    >
      <div class="country-option">
        <img
          v-if="country.countryCode"
          :src="`https://flagcdn.com/16x12/${country.countryCode.toLowerCase()}.png`"
          style="width: 16px; height: 12px; margin-right: 6px;"
          @error="handleImageError"
          alt="flag"
        />
        <span class="country-name">{{ country.countryName }}</span>
        <el-tag size="small" type="info" style="margin-left: 8px">
          {{ country.cityCount || 0 }}个城市
        </el-tag>
      </div>
    </el-option>

    <!-- 空状态 -->
    <template #empty>
      <div class="empty-state">
        <span v-if="requireRegion && !regionId">请先选择区域</span>
        <span v-else-if="loading">搜索中...</span>
        <span v-else>暂无数据</span>
      </div>
    </template>
  </el-select>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { searchCountries as apiSearchCountries } from '@/api/system/location'
import { debounce } from 'lodash-es'
import locationCache from '@/utils/locationCache'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  regionId: {
    type: [String, Number],
    default: null
  },
  requireRegion: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择国家'
  },
  size: {
    type: String,
    default: 'default'
  },
  width: {
    type: String,
    default: '150px'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedCountry = ref(props.modelValue)
const countryOptions = ref([])
const loading = ref(false)

// 计算是否可以搜索
const canSearch = computed(() => {
  return !props.requireRegion || props.regionId
})

// 搜索国家数据（带缓存）
const searchCountriesWithCache = async (keyword) => {
  if (!canSearch.value) {
    countryOptions.value = []
    return
  }

  const cacheParams = {
    keyword: keyword || '',
    regionId: props.regionId || '',
    pageNum: 1,
    pageSize: 100
  }
  
  // 获取缓存数据
  const cached = locationCache.get('country', cacheParams)
  if (cached) {
    // 立即显示缓存数据，避免跳动
    countryOptions.value = cached.data
    
    // 检查是否需要静默刷新
    if (locationCache.shouldRefresh('country', cacheParams)) {
      // 静默刷新数据
      try {
        const response = await apiSearchCountries({
          keyword: keyword || undefined,
          regionId: props.regionId || undefined,
          pageNum: 1,
          pageSize: 100
        })
        const newData = response.data?.rows || response.rows || []
        
        // 更新缓存
        locationCache.set('country', cacheParams, newData)
        
        // 静默更新选项（如果数据有变化）
        if (JSON.stringify(newData) !== JSON.stringify(countryOptions.value)) {
          countryOptions.value = newData
        }
      } catch (error) {
        console.warn('静默刷新国家数据失败:', error)
      }
    }
    return
  }
  
  // 没有缓存数据，显示加载状态
  loading.value = true
  try {
    const response = await apiSearchCountries({
      keyword: keyword || undefined,
      regionId: props.regionId || undefined,
      pageNum: 1,
      pageSize: 100
    })
    const newData = response.data?.rows || response.rows || []
    
    // 更新数据和缓存
    countryOptions.value = newData
    locationCache.set('country', cacheParams, newData)
  } catch (error) {
    console.error('搜索国家失败:', error)
    countryOptions.value = []
  } finally {
    loading.value = false
  }
}

const debouncedSearch = debounce(async (keyword) => {
  await searchCountriesWithCache(keyword)
}, 300)

const searchCountries = (keyword) => {
  debouncedSearch(keyword)
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  
  // 找到选中的国家信息
  const selectedItem = countryOptions.value.find(item => item.id === value)
  emit('change', value, selectedItem)
}

const handleFocus = () => {
  if (canSearch.value && countryOptions.value.length === 0) {
    searchCountries('')
  }
}

const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null, null)
}

const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 监听regionId变化，重新加载国家列表
watch(() => props.regionId, (newRegionId, oldRegionId) => {
  if (newRegionId !== oldRegionId) {
    // 只有当oldRegionId不是null时才清空选择（避免初始化时清空）
    if (oldRegionId !== null) {
      selectedCountry.value = null
      emit('update:modelValue', null)
      emit('change', null, null)
    }
    
    // 清空选项但不清空缓存，让新的搜索能够从缓存中获取数据
    countryOptions.value = []
    
    // 如果有新的regionId，自动加载数据
    if (newRegionId && canSearch.value) {
      searchCountries('')
    }
  }
})

// 监听父组件传入的值变化
watch(() => props.modelValue, (newVal) => {
  selectedCountry.value = newVal
}, { immediate: true })

// 组件挂载时处理初始值
onMounted(() => {
  // 如果有初始值且有regionId，自动加载数据以确保选项可用
  if (props.modelValue && props.regionId && canSearch.value) {
    searchCountries('')
  }
})

// 暴露方法供父组件调用
defineExpose({
  reload: () => searchCountries(''),
  clear: handleClear
})
</script>

<style scoped>
.country-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.country-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>