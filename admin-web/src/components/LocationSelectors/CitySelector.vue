<template>
  <el-select
    v-model="selectedCity"
    :placeholder="placeholder"
    :loading="loading"
    :size="size"
    :disabled="!countryId && requireCountry"
    filterable
    remote
    reserve-keyword
    :remote-method="searchCities"
    @change="handleChange"
    @focus="handleFocus"
    @clear="handleClear"
    clearable
    :style="{ width: width }"
  >
    <el-option
      v-for="city in cityOptions"
      :key="city.id"
      :label="city.displayName"
      :value="city.id"
    >
      <div class="city-option">
        <img
          v-if="city.countryCode"
          :src="`https://flagcdn.com/16x12/${city.countryCode.toLowerCase()}.png`"
          style="width: 16px; height: 12px; margin-right: 6px;"
          @error="handleImageError"
          alt="flag"
        />
        <span class="city-name">{{ city.cityName }}</span>
        <div class="city-tags">
          <el-tag size="small" type="info" style="margin-left: 6px">
            {{ city.regionName }}
          </el-tag>
          <el-tag v-if="city.isHot" size="small" type="warning" style="margin-left: 4px">
            热门
          </el-tag>
        </div>
      </div>
    </el-option>

    <!-- 空状态 -->
    <template #empty>
      <div class="empty-state">
        <span v-if="requireCountry && !countryId">请先选择国家</span>
        <span v-else-if="loading">搜索中...</span>
        <span v-else>暂无数据</span>
      </div>
    </template>
  </el-select>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { searchCities as apiSearchCities } from '@/api/system/location'
import { debounce } from 'lodash-es'
import locationCache from '@/utils/locationCache'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  countryId: {
    type: [String, Number],
    default: null
  },
  regionId: {
    type: [String, Number],
    default: null
  },
  requireCountry: {
    type: Boolean,
    default: false
  },
  hotOnly: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择城市'
  },
  size: {
    type: String,
    default: 'default'
  },
  width: {
    type: String,
    default: '180px'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedCity = ref(props.modelValue)
const cityOptions = ref([])
const loading = ref(false)

// 计算是否可以搜索
const canSearch = computed(() => {
  return !props.requireCountry || props.countryId
})

// 搜索城市数据（带缓存）
const searchCitiesWithCache = async (keyword) => {
  if (!canSearch.value) {
    cityOptions.value = []
    return
  }

  const cacheParams = {
    keyword: keyword || '',
    countryId: props.countryId || '',
    regionId: props.regionId || '',
    hotOnly: props.hotOnly,
    pageNum: 1,
    pageSize: 100
  }
  
  // 获取缓存数据
  const cached = locationCache.get('city', cacheParams)
  if (cached) {
    // 立即显示缓存数据，避免跳动
    cityOptions.value = cached.data
    
    // 检查是否需要静默刷新
    if (locationCache.shouldRefresh('city', cacheParams)) {
      // 静默刷新数据
      try {
        const response = await apiSearchCities({
          keyword: keyword || undefined,
          countryId: props.countryId || undefined,
          regionId: props.regionId || undefined,
          hotOnly: props.hotOnly,
          pageNum: 1,
          pageSize: 100
        })
        const newData = response.data?.rows || response.rows || []
        
        // 更新缓存
        locationCache.set('city', cacheParams, newData)
        
        // 静默更新选项（如果数据有变化）
        if (JSON.stringify(newData) !== JSON.stringify(cityOptions.value)) {
          cityOptions.value = newData
        }
      } catch (error) {
        console.warn('静默刷新城市数据失败:', error)
      }
    }
    return
  }
  
  // 没有缓存数据，显示加载状态
  loading.value = true
  try {
    const response = await apiSearchCities({
      keyword: keyword || undefined,
      countryId: props.countryId || undefined,
      regionId: props.regionId || undefined,
      hotOnly: props.hotOnly,
      pageNum: 1,
      pageSize: 100
    })
    const newData = response.data?.rows || response.rows || []
    
    // 更新数据和缓存
    cityOptions.value = newData
    locationCache.set('city', cacheParams, newData)
  } catch (error) {
    console.error('搜索城市失败:', error)
    cityOptions.value = []
  } finally {
    loading.value = false
  }
}

const debouncedSearch = debounce(async (keyword) => {
  await searchCitiesWithCache(keyword)
}, 300)

const searchCities = (keyword) => {
  debouncedSearch(keyword)
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  
  // 找到选中的城市信息
  const selectedItem = cityOptions.value.find(item => item.id === value)
  emit('change', value, selectedItem)
}

const handleFocus = () => {
  if (canSearch.value && cityOptions.value.length === 0) {
    searchCities('')
  }
}

const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null, null)
}

const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 监听countryId变化
watch(() => props.countryId, (newCountryId, oldCountryId) => {
  if (newCountryId !== oldCountryId) {
    // 只有当oldCountryId不是null时才清空选择（避免初始化时清空）
    if (oldCountryId !== null) {
      selectedCity.value = null
      emit('update:modelValue', null)
      emit('change', null, null)
    }
    
    // 清空选项但不清空缓存，让新的搜索能够从缓存中获取数据
    cityOptions.value = []
    
    // 如果有新的countryId，自动加载数据
    if (newCountryId && canSearch.value) {
      searchCities('')
    }
  }
})

// 监听regionId变化（如果没有countryId限制）
watch(() => props.regionId, (newRegionId, oldRegionId) => {
  if (!props.requireCountry && newRegionId !== oldRegionId) {
    // 清空当前选择
    selectedCity.value = null
    emit('update:modelValue', null)
    emit('change', null, null)
    
    // 清空选项但不清空缓存，让新的搜索能够从缓存中获取数据
    cityOptions.value = []
    
    // 如果有新的regionId，自动加载数据
    if (newRegionId && canSearch.value) {
      searchCities('')
    }
  }
})

// 监听hotOnly变化
watch(() => props.hotOnly, () => {
  if (canSearch.value) {
    searchCities('')
  }
})

// 监听父组件传入的值变化
watch(() => props.modelValue, (newVal) => {
  selectedCity.value = newVal
}, { immediate: true })

// 组件挂载时处理初始值
onMounted(() => {
  // 如果有初始值且有countryId，自动加载数据以确保选项可用
  if (props.modelValue && props.countryId && canSearch.value) {
    searchCitiesWithCache('')
  }
})

// 暴露方法供父组件调用
defineExpose({
  reload: () => searchCities(''),
  clear: handleClear
})
</script>

<style scoped>
.city-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.city-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.city-tags {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>