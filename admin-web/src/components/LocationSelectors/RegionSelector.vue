<template>
  <el-select
    v-model="selectedRegion"
    :placeholder="placeholder"
    :loading="loading"
    :size="size"
    filterable
    remote
    reserve-keyword
    :remote-method="searchRegions"
    @change="handleChange"
    @focus="handleFocus"
    @clear="handleClear"
    clearable
    :style="{ width: width }"
  >
    <el-option
      v-for="region in regionOptions"
      :key="region.id"
      :label="region.regionName"
      :value="region.id"
    >
      <div class="region-option">
        <span>{{ region.regionName }}</span>
        <el-tag size="small" type="info" style="margin-left: 8px">
          {{ region.countryCount || 0 }}个国家
        </el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { searchRegions as apiSearchRegions } from '@/api/system/location'
import { debounce } from 'lodash-es'
import locationCache from '@/utils/locationCache'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择区域'
  },
  size: {
    type: String,
    default: 'default'
  },
  width: {
    type: String,
    default: '140px'
  },
  // 是否在挂载时自动加载数据
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedRegion = ref(props.modelValue)
const regionOptions = ref([])
const loading = ref(false)

// 搜索区域数据（带缓存）
const searchRegionsWithCache = async (keyword) => {
  const cacheParams = {
    keyword: keyword || '',
    pageNum: 1,
    pageSize: 100
  }
  
  // 获取缓存数据
  const cached = locationCache.get('region', cacheParams)
  if (cached) {
    // 立即显示缓存数据，避免跳动
    regionOptions.value = cached.data
    
    // 检查是否需要静默刷新
    if (locationCache.shouldRefresh('region', cacheParams)) {
      // 静默刷新数据
      try {
        const response = await apiSearchRegions(cacheParams)
        const newData = response.data?.rows || response.rows || []
        
        // 更新缓存
        locationCache.set('region', cacheParams, newData)
        
        // 静默更新选项（如果数据有变化）
        if (JSON.stringify(newData) !== JSON.stringify(regionOptions.value)) {
          regionOptions.value = newData
        }
      } catch (error) {
        console.warn('静默刷新区域数据失败:', error)
      }
    }
    return
  }
  
  // 没有缓存数据，显示加载状态
  loading.value = true
  try {
    const response = await apiSearchRegions(cacheParams)
    const newData = response.data?.rows || response.rows || []
    
    // 更新数据和缓存
    regionOptions.value = newData
    locationCache.set('region', cacheParams, newData)
  } catch (error) {
    console.error('搜索区域失败:', error)
    regionOptions.value = []
  } finally {
    loading.value = false
  }
}

// 防抖搜索，避免频繁请求
const debouncedSearch = debounce(async (keyword) => {
  await searchRegionsWithCache(keyword)
}, 300)

const searchRegions = (keyword) => {
  debouncedSearch(keyword)
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  
  // 找到选中的区域信息
  const selectedItem = regionOptions.value.find(item => item.id === value)
  emit('change', value, selectedItem)
}

const handleFocus = () => {
  // 焦点时加载默认数据（优先使用缓存）
  if (regionOptions.value.length === 0) {
    searchRegions('')
  }
}

const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null, null)
}

// 监听父组件传入的值变化
watch(() => props.modelValue, (newVal) => {
  selectedRegion.value = newVal
}, { immediate: true })

// 组件挂载时自动加载数据
onMounted(() => {
  if (props.autoLoad) {
    searchRegions('')
  }
})

// 暴露方法供父组件调用
defineExpose({
  reload: () => searchRegions(''),
  clear: handleClear
})
</script>

<style scoped>
.region-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.region-option span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>