-- ============================================
-- 产品地理位置标准化迁移SQL (最终版本)
-- 执行时间: 2025-06-29
-- 目标: 更新剩余3个产品的location_city_id字段
-- ============================================

-- 开始事务确保数据一致性
START TRANSACTION;

-- 更新前查看当前状态
SELECT 
    id, product_name, city, location_city_id,
    'BEFORE UPDATE' as status
FROM shop_product 
WHERE id IN (11, 30, 38);

-- 执行更新操作
-- 产品ID 11: 洛杉矶 -> location_city_id 1666
UPDATE shop_product 
SET location_city_id = 1666 
WHERE id = 11 AND city = '洛杉矶';

-- 产品ID 30: 旧金山 -> location_city_id 1667  
UPDATE shop_product 
SET location_city_id = 1667 
WHERE id = 30 AND city = '旧金山';

-- 产品ID 38: 利雅得 -> location_city_id 1668
UPDATE shop_product 
SET location_city_id = 1668 
WHERE id = 38 AND city = '利雅得';

-- 验证更新结果
SELECT 
    p.id,
    p.product_name,
    p.city as original_city,
    p.location_city_id,
    lc.city_name as standardized_city,
    lco.country_name,
    lr.region_name,
    'AFTER UPDATE' as status
FROM shop_product p
LEFT JOIN sys_location_city lc ON p.location_city_id = lc.id
LEFT JOIN sys_location_country lco ON lc.country_id = lco.id
LEFT JOIN sys_location_region lr ON lco.region_id = lr.id
WHERE p.id IN (11, 30, 38);

-- 统计迁移完成情况
SELECT 
    COUNT(*) as total_products,
    SUM(CASE WHEN location_city_id IS NOT NULL THEN 1 ELSE 0 END) as products_with_location_id,
    ROUND(SUM(CASE WHEN location_city_id IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completion_percentage
FROM shop_product;

-- 如果验证通过，提交事务
COMMIT;

-- 可选: 添加外键约束确保数据完整性 (如果需要)
-- ALTER TABLE shop_product 
-- ADD CONSTRAINT fk_product_location_city 
-- FOREIGN KEY (location_city_id) REFERENCES sys_location_city(id) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Migration completed successfully!' as result;
