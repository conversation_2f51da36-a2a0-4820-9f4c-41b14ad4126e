#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 获取 worktree ID
const worktreeId = path.basename(process.cwd());
const worktreeEnvPath = `.env.worktree-${worktreeId}`;

console.log(`\n🔧 Worktree Dev Runner`);
console.log(`📁 Working directory: ${process.cwd()}`);
console.log(`🏷️  Worktree ID: ${worktreeId}`);

// 检查并加载 worktree 配置
if (fs.existsSync(worktreeEnvPath)) {
  console.log(`✅ Loading worktree config: ${worktreeEnvPath}`);
  require('dotenv').config({ path: worktreeEnvPath });
  
  // 显示加载的端口配置
  console.log(`📡 Loaded ports:`);
  console.log(`   - Server: ${process.env.SERVER_PORT || process.env.APP_PORT}`);
  console.log(`   - Admin: ${process.env.VITE_ADMIN_WEB_PORT}`);
  console.log(`   - Shop: ${process.env.VITE_SHOP_WEB_PORT}`);
} else {
  console.log(`⚠️  No worktree config found, using defaults from .env.local`);
}

// 获取要运行的命令
const [,, ...args] = process.argv;
const command = args[0] || 'dev';

console.log(`\n🚀 Running: npm run ${command}\n`);

// 准备环境变量，移除调试器相关的选项
const cleanEnv = { ...process.env };
delete cleanEnv.NODE_OPTIONS; // 移除可能包含调试器标志的选项

// 运行原始命令，传递清理后的环境变量
const child = spawn('npm', ['run', command], { 
  stdio: 'inherit',
  env: {
    ...cleanEnv,
    FORCE_COLOR: '1' // 保持彩色输出
  },
  shell: true
});

// 处理退出
child.on('exit', (code) => {
  process.exit(code);
});