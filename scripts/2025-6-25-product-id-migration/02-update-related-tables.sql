-- ==========================================
-- 关联表数据更新脚本
-- 创建日期: 2025-06-25
-- 目标: 更新所有引用product_id的表，使其指向正确的主键id
-- 风险级别: HIGH - 数据一致性关键操作
-- ==========================================

-- 设置会话变量
SET @start_time = NOW();
SET @script_version = 'related-tables-update-v1.0';

-- 记录开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'RELATED_TABLES_UPDATE_START', @start_time, 'START', 'Starting related tables product_id update');

-- 禁用外键检查（已在之前脚本中删除了外键约束）
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- ==========================================
-- 第一步：更新shop_order表
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'UPDATE_SHOP_ORDER_START', NOW(), 'START');

-- 查看更新前的数据状态
SELECT 'SHOP_ORDER: Before Update' as Info;
SELECT 
    COUNT(*) as total_orders,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_order 
WHERE product_id IS NOT NULL;

-- 更新shop_order表的product_id为对应的主键id
UPDATE shop_order o
INNER JOIN product_id_mapping m ON o.product_id = m.old_product_id
SET o.product_id = m.new_id;

-- 获取影响的行数
SELECT ROW_COUNT() as shop_order_updated_rows;

-- 验证更新结果
SELECT 'SHOP_ORDER: After Update' as Info;
SELECT 
    COUNT(*) as total_orders,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as orphaned_records
FROM shop_order 
WHERE product_id IS NOT NULL;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, affected_rows)
VALUES (@script_version, 'UPDATE_SHOP_ORDER_COMPLETE', NOW(), 'SUCCESS', ROW_COUNT());

-- ==========================================
-- 第二步：更新shop_proxy_instances表
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'UPDATE_PROXY_INSTANCES_START', NOW(), 'START');

-- 查看更新前的数据状态
SELECT 'SHOP_PROXY_INSTANCES: Before Update' as Info;
SELECT 
    COUNT(*) as total_instances,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_proxy_instances 
WHERE product_id IS NOT NULL;

-- 更新shop_proxy_instances表的product_id为对应的主键id
UPDATE shop_proxy_instances pi
INNER JOIN product_id_mapping m ON pi.product_id = m.old_product_id
SET pi.product_id = m.new_id;

-- 验证更新结果
SELECT 'SHOP_PROXY_INSTANCES: After Update' as Info;
SELECT 
    COUNT(*) as total_instances,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as orphaned_records
FROM shop_proxy_instances 
WHERE product_id IS NOT NULL;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, affected_rows)
VALUES (@script_version, 'UPDATE_PROXY_INSTANCES_COMPLETE', NOW(), 'SUCCESS', ROW_COUNT());

-- ==========================================
-- 第三步：更新shop_sync_product_detail表
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'UPDATE_SYNC_DETAIL_START', NOW(), 'START');

-- 查看更新前的数据状态
SELECT 'SHOP_SYNC_PRODUCT_DETAIL: Before Update' as Info;
SELECT 
    COUNT(*) as total_details,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_sync_product_detail 
WHERE product_id IS NOT NULL;

-- 更新shop_sync_product_detail表的product_id为对应的主键id
UPDATE shop_sync_product_detail spd
INNER JOIN product_id_mapping m ON spd.product_id = m.old_product_id
SET spd.product_id = m.new_id;

-- 验证更新结果
SELECT 'SHOP_SYNC_PRODUCT_DETAIL: After Update' as Info;
SELECT 
    COUNT(*) as total_details,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as orphaned_records
FROM shop_sync_product_detail 
WHERE product_id IS NOT NULL;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, affected_rows)
VALUES (@script_version, 'UPDATE_SYNC_DETAIL_COMPLETE', NOW(), 'SUCCESS', ROW_COUNT());

-- ==========================================
-- 第四步：检查其他可能的表（如果存在）
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CHECK_OTHER_TABLES_START', NOW(), 'START');

-- 检查是否还有其他表引用了product_id
SELECT 'OTHER TABLES WITH PRODUCT_ID REFERENCES' as Info;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
AND COLUMN_NAME = 'product_id'
AND TABLE_NAME NOT IN ('shop_product', 'shop_order', 'shop_proxy_instances', 'shop_sync_product_detail', 'product_id_mapping')
ORDER BY TABLE_NAME;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CHECK_OTHER_TABLES_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第五步：最终数据一致性验证
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'FINAL_VALIDATION_START', NOW(), 'START');

-- 验证所有表的数据一致性
SELECT 'FINAL DATA CONSISTENCY VALIDATION' as Info;

-- 检查shop_order表
SELECT 
    'shop_order' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT id FROM shop_product) THEN 1 END) as valid_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as invalid_references
FROM shop_order 
WHERE product_id IS NOT NULL

UNION ALL

-- 检查shop_proxy_instances表
SELECT 
    'shop_proxy_instances' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT id FROM shop_product) THEN 1 END) as valid_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as invalid_references
FROM shop_proxy_instances 
WHERE product_id IS NOT NULL

UNION ALL

-- 检查shop_sync_product_detail表
SELECT 
    'shop_sync_product_detail' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT id FROM shop_product) THEN 1 END) as valid_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT id FROM shop_product) THEN 1 END) as invalid_references
FROM shop_sync_product_detail 
WHERE product_id IS NOT NULL;

-- 创建验证报告
CREATE TABLE migration_validation_report_20250625 AS
SELECT 
    'Data Migration Validation Report' as report_type,
    NOW() as generated_at,
    (SELECT COUNT(*) FROM shop_order WHERE product_id IS NOT NULL) as shop_order_records,
    (SELECT COUNT(*) FROM shop_proxy_instances WHERE product_id IS NOT NULL) as proxy_instances_records,
    (SELECT COUNT(*) FROM shop_sync_product_detail WHERE product_id IS NOT NULL) as sync_detail_records,
    (SELECT COUNT(*) FROM shop_product) as total_products;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'FINAL_VALIDATION_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 重新启用安全更新和外键检查
-- ==========================================

SET SQL_SAFE_UPDATES = 1;
SET FOREIGN_KEY_CHECKS = 1;

-- 记录完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'RELATED_TABLES_UPDATE_COMPLETE', NOW(), 'SUCCESS', 
CONCAT('Related tables update completed in ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds'));

-- ==========================================
-- 执行结果报告
-- ==========================================

SELECT 'RELATED TABLES UPDATE SUMMARY' as Info;
SELECT 
    step_name,
    execution_time,
    status,
    affected_rows,
    notes
FROM migration_execution_log 
WHERE script_name = @script_version 
ORDER BY id;

SELECT CONCAT('Update completed at: ', NOW()) as Info;
SELECT CONCAT('Total execution time: ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds') as Info;

-- ==========================================
-- 回滚脚本（如有需要）
-- ==========================================

/*
ROLLBACK INSTRUCTIONS:
======================

如果需要回滚关联表的更新，可以执行以下操作：

-- 恢复shop_order表
UPDATE shop_order o
INNER JOIN order_migration_backup_20250625 b ON o.order_id = b.order_id
SET o.product_id = b.product_id;

-- 恢复shop_proxy_instances表
UPDATE shop_proxy_instances pi
INNER JOIN proxy_instances_migration_backup_20250625 b ON pi.instance_id = b.instance_id
SET pi.product_id = b.product_id;

-- 恢复shop_sync_product_detail表
UPDATE shop_sync_product_detail spd
INNER JOIN sync_detail_migration_backup_20250625 b ON spd.detail_id = b.detail_id
SET spd.product_id = b.product_id;

注意：回滚操作会使数据返回到迁移前的不一致状态
*/