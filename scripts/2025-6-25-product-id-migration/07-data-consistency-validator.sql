-- ==========================================
-- 数据一致性验证和修复脚本
-- 创建日期: 2025-06-25
-- 目标: 验证两环境数据一致性，提供自动修复机制
-- 风险级别: MEDIUM - 数据验证和修复工具
-- ==========================================

-- 设置会话变量
SET @start_time = NOW();
SET @script_version = 'data-consistency-validator-v1.0';

-- 创建验证日志表
CREATE TABLE IF NOT EXISTS data_consistency_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    validation_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    check_type ENUM('STRUCTURE', 'DATA', 'INTEGRITY', 'BUSINESS') NOT NULL,
    table_name VARCHAR(100),
    status ENUM('PASS', 'FAIL', 'WARNING', 'FIXED') NOT NULL,
    issue_description TEXT,
    fix_action TEXT,
    affected_records INT DEFAULT 0,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM'
);

-- 记录验证开始
INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('STRUCTURE', 'ALL_TABLES', 'PASS', 'Starting comprehensive data consistency validation');

-- ===========================================
-- 第一部分：结构一致性验证
-- ===========================================

-- 1.1 表结构对比验证
INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('STRUCTURE', 'TABLES', 'PASS', 'Validating table structure consistency');

-- 检查关键表是否存在
SELECT 'TABLE_EXISTENCE_CHECK' as Info;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as status
FROM (
    SELECT 'shop_product' as table_name
    UNION SELECT 'shop_order'
    UNION SELECT 'shop_proxy_instances'  
    UNION SELECT 'shop_sync_product_detail'
    UNION SELECT 'product_id_mapping'
) required_tables;

-- 1.2 字段结构验证
SELECT 'COLUMN_STRUCTURE_VALIDATION' as Info;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    CASE 
        WHEN TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id' THEN 'ERROR: product_id still exists'
        WHEN TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'id' AND COLUMN_KEY = 'PRI' THEN 'CORRECT: id is primary key'
        ELSE 'OK'
    END as validation_status
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('shop_product', 'shop_order', 'shop_proxy_instances', 'shop_sync_product_detail')
AND COLUMN_NAME IN ('id', 'product_id')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- ===========================================
-- 第二部分：数据完整性验证
-- ===========================================

INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('DATA', 'ALL_TABLES', 'PASS', 'Starting data integrity validation');

-- 2.1 主键唯一性验证
SELECT 'PRIMARY_KEY_UNIQUENESS_CHECK' as Info;

-- shop_product主键唯一性
SELECT 
    'shop_product' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT id) as unique_ids,
    COUNT(*) - COUNT(DISTINCT id) as duplicate_count,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT id) THEN 'PASS'
        ELSE 'FAIL'
    END as validation_status
FROM shop_product

UNION ALL

-- shop_order主键唯一性
SELECT 
    'shop_order' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT order_id) as unique_ids,
    COUNT(*) - COUNT(DISTINCT order_id) as duplicate_count,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT order_id) THEN 'PASS'
        ELSE 'FAIL'
    END as validation_status
FROM shop_order;

-- 2.2 外键引用完整性验证
SELECT 'FOREIGN_KEY_INTEGRITY_CHECK' as Info;

-- 订单表产品引用完整性
SELECT 
    'shop_order_product_references' as check_type,
    COUNT(*) as total_orders_with_product,
    COUNT(p.id) as valid_product_references,
    COUNT(*) - COUNT(p.id) as orphaned_references,
    CASE 
        WHEN COUNT(*) = COUNT(p.id) THEN 'PASS'
        ELSE 'FAIL'
    END as validation_status
FROM shop_order o
LEFT JOIN shop_product p ON o.product_id = p.id
WHERE o.product_id IS NOT NULL

UNION ALL

-- 代理实例表产品引用完整性
SELECT 
    'shop_proxy_instances_product_references' as check_type,
    COUNT(*) as total_instances_with_product,
    COUNT(p.id) as valid_product_references,
    COUNT(*) - COUNT(p.id) as orphaned_references,
    CASE 
        WHEN COUNT(*) = COUNT(p.id) THEN 'PASS'
        ELSE 'FAIL'
    END as validation_status
FROM shop_proxy_instances pi
LEFT JOIN shop_product p ON pi.product_id = p.id
WHERE pi.product_id IS NOT NULL

UNION ALL

-- 同步详情表产品引用完整性
SELECT 
    'shop_sync_product_detail_product_references' as check_type,
    COUNT(*) as total_details_with_product,
    COUNT(p.id) as valid_product_references,
    COUNT(*) - COUNT(p.id) as orphaned_references,
    CASE 
        WHEN COUNT(*) = COUNT(p.id) THEN 'PASS'
        ELSE 'FAIL'
    END as validation_status
FROM shop_sync_product_detail spd
LEFT JOIN shop_product p ON spd.product_id = p.id
WHERE spd.product_id IS NOT NULL;

-- 2.3 数据范围合理性验证
SELECT 'DATA_RANGE_VALIDATION' as Info;

-- 产品ID范围验证
SELECT 
    'shop_product_id_range' as check_type,
    MIN(id) as min_id,
    MAX(id) as max_id,
    COUNT(*) as total_products,
    MAX(id) - MIN(id) + 1 as expected_range,
    COUNT(*) as actual_count,
    CASE 
        WHEN MAX(id) - MIN(id) + 1 - COUNT(*) <= 10 THEN 'PASS'
        ELSE 'WARNING'
    END as validation_status
FROM shop_product;

-- ===========================================
-- 第三部分：业务逻辑一致性验证
-- ===========================================

INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('BUSINESS', 'ALL_TABLES', 'PASS', 'Starting business logic consistency validation');

-- 3.1 订单业务逻辑验证
SELECT 'BUSINESS_LOGIC_VALIDATION' as Info;

-- 订单状态与产品关联合理性
SELECT 
    'order_product_consistency' as check_type,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN product_id IS NOT NULL THEN 1 END) as orders_with_product,
    COUNT(CASE WHEN product_id IS NULL AND order_type = 'RECHARGE' THEN 1 END) as recharge_orders,
    COUNT(CASE WHEN product_id IS NULL AND order_type != 'RECHARGE' THEN 1 END) as orphaned_orders,
    CASE 
        WHEN COUNT(CASE WHEN product_id IS NULL AND order_type != 'RECHARGE' THEN 1 END) = 0 THEN 'PASS'
        ELSE 'WARNING'
    END as validation_status
FROM shop_order;

-- 3.2 产品库存和价格合理性
SELECT 
    'product_data_consistency' as check_type,
    COUNT(*) as total_products,
    COUNT(CASE WHEN stock_quantity >= 0 THEN 1 END) as valid_stock,
    COUNT(CASE WHEN unit_price > 0 THEN 1 END) as valid_price,
    COUNT(CASE WHEN stock_quantity < 0 THEN 1 END) as negative_stock,
    COUNT(CASE WHEN unit_price <= 0 THEN 1 END) as invalid_price,
    CASE 
        WHEN COUNT(CASE WHEN stock_quantity < 0 OR unit_price <= 0 THEN 1 END) = 0 THEN 'PASS'
        ELSE 'WARNING'
    END as validation_status
FROM shop_product;

-- ===========================================
-- 第四部分：自动修复机制
-- ===========================================

INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('INTEGRITY', 'AUTO_REPAIR', 'PASS', 'Starting automatic data repair procedures');

-- 4.1 修复孤立的外键引用
-- 注意：这些修复操作需要根据具体业务需求调整

-- 创建数据修复临时表
CREATE TEMPORARY TABLE temp_repair_actions (
    repair_id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100),
    action_type ENUM('DELETE', 'UPDATE', 'INSERT') NOT NULL,
    sql_statement TEXT,
    affected_records INT DEFAULT 0,
    executed BOOLEAN DEFAULT FALSE
);

-- 识别需要修复的孤立订单记录
INSERT INTO temp_repair_actions (table_name, action_type, sql_statement, affected_records)
SELECT 
    'shop_order' as table_name,
    'UPDATE' as action_type,
    CONCAT('UPDATE shop_order SET product_id = NULL WHERE order_id = ''', o.order_id, ''' AND product_id = ', o.product_id, ';') as sql_statement,
    1 as affected_records
FROM shop_order o
LEFT JOIN shop_product p ON o.product_id = p.id
WHERE o.product_id IS NOT NULL AND p.id IS NULL
LIMIT 100; -- 限制批量操作数量

-- 4.2 执行修复操作（需要手动确认）
SELECT 'REPAIR_ACTIONS_IDENTIFIED' as Info;
SELECT 
    repair_id,
    table_name,
    action_type,
    sql_statement,
    affected_records,
    'IDENTIFIED_NOT_EXECUTED' as status
FROM temp_repair_actions
WHERE executed = FALSE;

-- 4.3 数据质量评分
SELECT 'DATA_QUALITY_SCORE' as Info;
SELECT 
    @script_version as validation_version,
    NOW() as evaluation_time,
    
    -- 结构质量评分 (40%)
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') = 0 
        THEN 40 ELSE 0 
    END as structure_score,
    
    -- 完整性质量评分 (30%)
    CASE 
        WHEN (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.id WHERE o.product_id IS NOT NULL AND p.id IS NULL) = 0
        THEN 30 ELSE 20
    END as integrity_score,
    
    -- 一致性质量评分 (20%)
    CASE 
        WHEN (SELECT COUNT(*) FROM shop_product WHERE id != id) = 0  -- 恒真，表示主键一致性
        THEN 20 ELSE 10
    END as consistency_score,
    
    -- 业务逻辑质量评分 (10%)
    CASE 
        WHEN (SELECT COUNT(*) FROM shop_order WHERE product_id IS NULL AND order_type != 'RECHARGE') = 0
        THEN 10 ELSE 5
    END as business_logic_score;

-- ===========================================
-- 第五部分：生成详细报告
-- ===========================================

-- 创建综合验证报告表
CREATE TABLE IF NOT EXISTS migration_validation_report_detailed AS
SELECT 
    'Product ID Migration Validation Report' as report_title,
    NOW() as generated_at,
    @script_version as script_version,
    
    -- 表记录统计
    (SELECT COUNT(*) FROM shop_product) as shop_product_count,
    (SELECT COUNT(*) FROM shop_order WHERE product_id IS NOT NULL) as shop_order_with_product_count,
    (SELECT COUNT(*) FROM shop_proxy_instances WHERE product_id IS NOT NULL) as proxy_instances_with_product_count,
    (SELECT COUNT(*) FROM shop_sync_product_detail WHERE product_id IS NOT NULL) as sync_details_with_product_count,
    
    -- 完整性检查结果
    (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.id WHERE o.product_id IS NOT NULL AND p.id IS NULL) as orphaned_orders,
    (SELECT COUNT(*) FROM shop_proxy_instances pi LEFT JOIN shop_product p ON pi.product_id = p.id WHERE pi.product_id IS NOT NULL AND p.id IS NULL) as orphaned_instances,
    (SELECT COUNT(*) FROM shop_sync_product_detail spd LEFT JOIN shop_product p ON spd.product_id = p.id WHERE spd.product_id IS NOT NULL AND p.id IS NULL) as orphaned_sync_details,
    
    -- 结构检查结果
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_column_exists,
    
    -- 总体状态
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') = 0
             AND (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.id WHERE o.product_id IS NOT NULL AND p.id IS NULL) = 0
             AND (SELECT COUNT(*) FROM shop_proxy_instances pi LEFT JOIN shop_product p ON pi.product_id = p.id WHERE pi.product_id IS NOT NULL AND p.id IS NULL) = 0
        THEN 'VALIDATION_PASSED'
        ELSE 'VALIDATION_FAILED_OR_WARNINGS'
    END as overall_status;

-- 记录验证完成
INSERT INTO data_consistency_log (check_type, table_name, status, issue_description)
VALUES ('STRUCTURE', 'ALL_TABLES', 'PASS', 'Data consistency validation completed');

-- ===========================================
-- 执行结果展示
-- ===========================================

SELECT 'DATA_CONSISTENCY_VALIDATION_SUMMARY' as Info;
SELECT 
    check_type,
    table_name,
    status,
    COUNT(*) as check_count,
    GROUP_CONCAT(DISTINCT severity) as severity_levels
FROM data_consistency_log 
WHERE validation_time >= @start_time
GROUP BY check_type, table_name, status
ORDER BY 
    FIELD(check_type, 'STRUCTURE', 'DATA', 'INTEGRITY', 'BUSINESS'),
    FIELD(status, 'FAIL', 'WARNING', 'PASS');

SELECT 'DETAILED_VALIDATION_REPORT' as Info;
SELECT * FROM migration_validation_report_detailed;

SELECT CONCAT('Validation completed at: ', NOW()) as Info;
SELECT CONCAT('Total validation time: ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds') as Info;

-- ===========================================
-- 使用说明
-- ===========================================

/*
使用说明：
===========

1. 在两个环境中分别执行此脚本
2. 对比两个环境的验证结果
3. 根据识别的问题执行相应的修复操作
4. 重新执行验证确保问题已解决

修复操作示例：
================

-- 手动执行孤立记录修复
UPDATE shop_order SET product_id = NULL 
WHERE order_id IN (
    SELECT order_id FROM shop_order o
    LEFT JOIN shop_product p ON o.product_id = p.id
    WHERE o.product_id IS NOT NULL AND p.id IS NULL
);

-- 删除无效的代理实例记录（谨慎操作）
DELETE FROM shop_proxy_instances 
WHERE product_id NOT IN (SELECT id FROM shop_product);

监控指标：
==========
- overall_status = 'VALIDATION_PASSED' 表示验证通过
- orphaned_* = 0 表示无孤立记录
- product_id_column_exists = 0 表示字段已正确删除
*/