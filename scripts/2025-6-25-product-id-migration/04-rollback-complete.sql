-- ==========================================
-- 完整回滚脚本 - 产品ID迁移回滚
-- 创建日期: 2025-06-25
-- 目标: 完全回滚product_id删除操作，恢复到原始状态
-- 风险级别: HIGH - 紧急恢复操作
-- ==========================================

-- 设置会话变量
SET @start_time = NOW();
SET @script_version = 'product-id-rollback-v1.0';

-- 创建回滚日志表
CREATE TABLE IF NOT EXISTS rollback_execution_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    script_name VARCHAR(100),
    step_name VARCHAR(200),
    execution_time DATETIME,
    status ENUM('START', 'SUCCESS', 'ERROR', 'WARNING'),
    error_message TEXT,
    affected_rows INT DEFAULT 0,
    notes TEXT
);

-- 记录回滚开始
INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'ROLLBACK_START', @start_time, 'START', 'Starting complete product_id migration rollback');

-- 禁用外键检查和安全更新
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- ==========================================
-- 第一步：验证备份表存在
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'VERIFY_BACKUPS_START', NOW(), 'START');

-- 检查必要的备份表是否存在
SELECT 'BACKUP TABLES VERIFICATION' as Info;
SELECT 
    TABLE_NAME,
    'EXISTS' as status
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN (
    'shop_product_complete_backup_20250625',
    'order_migration_backup_20250625',
    'proxy_instances_migration_backup_20250625',
    'sync_detail_migration_backup_20250625',
    'product_id_mapping'
)
ORDER BY TABLE_NAME;

-- 检查备份表的记录数
SELECT 'BACKUP DATA COUNTS' as Info;
SELECT 
    'shop_product_complete_backup_20250625' as table_name,
    COUNT(*) as record_count
FROM shop_product_complete_backup_20250625
UNION ALL
SELECT 
    'order_migration_backup_20250625' as table_name,
    COUNT(*) as record_count
FROM order_migration_backup_20250625
UNION ALL
SELECT 
    'proxy_instances_migration_backup_20250625' as table_name,
    COUNT(*) as record_count
FROM proxy_instances_migration_backup_20250625
UNION ALL
SELECT 
    'sync_detail_migration_backup_20250625' as table_name,
    COUNT(*) as record_count
FROM sync_detail_migration_backup_20250625;

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'VERIFY_BACKUPS_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第二步：恢复shop_product表结构和数据
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'RESTORE_SHOP_PRODUCT_START', NOW(), 'START');

-- 备份当前修改后的表（以防需要重新迁移）
CREATE TABLE shop_product_modified_backup_rollback AS SELECT * FROM shop_product;

-- 删除当前的shop_product表
DROP TABLE IF EXISTS shop_product;

-- 从备份重建shop_product表
CREATE TABLE shop_product AS SELECT * FROM shop_product_complete_backup_20250625;

-- 重新添加主键（如果需要）
ALTER TABLE shop_product ADD PRIMARY KEY (id);

-- 验证恢复结果
SELECT 'SHOP_PRODUCT RESTORATION VERIFICATION' as Info;
SELECT 
    COUNT(*) as total_products,
    COUNT(DISTINCT id) as distinct_ids,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    COUNT(CASE WHEN id IS NOT NULL THEN 1 END) as non_null_ids,
    COUNT(CASE WHEN product_id IS NOT NULL THEN 1 END) as non_null_product_ids,
    MIN(id) as min_id,
    MAX(id) as max_id,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_product;

-- 检查product_id字段是否已恢复
SELECT 
    COUNT(*) as product_id_column_restored
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'shop_product' 
AND COLUMN_NAME = 'product_id';

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status, affected_rows)
VALUES (@script_version, 'RESTORE_SHOP_PRODUCT_COMPLETE', NOW(), 'SUCCESS', (SELECT COUNT(*) FROM shop_product));

-- ==========================================
-- 第三步：恢复关联表数据
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'RESTORE_RELATED_TABLES_START', NOW(), 'START');

-- 恢复shop_order表
UPDATE shop_order o
INNER JOIN order_migration_backup_20250625 b ON o.order_id = b.order_id
SET o.product_id = b.product_id;

SELECT 'SHOP_ORDER RESTORATION' as Info;
SELECT 
    COUNT(*) as total_orders,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_order WHERE product_id IS NOT NULL;

-- 恢复shop_proxy_instances表
UPDATE shop_proxy_instances pi
INNER JOIN proxy_instances_migration_backup_20250625 b ON pi.instance_id = b.instance_id
SET pi.product_id = b.product_id;

SELECT 'SHOP_PROXY_INSTANCES RESTORATION' as Info;
SELECT 
    COUNT(*) as total_instances,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_proxy_instances WHERE product_id IS NOT NULL;

-- 恢复shop_sync_product_detail表
UPDATE shop_sync_product_detail spd
INNER JOIN sync_detail_migration_backup_20250625 b ON spd.detail_id = b.detail_id
SET spd.product_id = b.product_id;

SELECT 'SHOP_SYNC_PRODUCT_DETAIL RESTORATION' as Info;
SELECT 
    COUNT(*) as total_details,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_sync_product_detail WHERE product_id IS NOT NULL;

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'RESTORE_RELATED_TABLES_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第四步：恢复索引结构
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'RESTORE_INDEXES_START', NOW(), 'START');

-- 重新创建product_id的唯一索引
ALTER TABLE shop_product ADD UNIQUE INDEX uk_product_id (product_id);

-- 重新创建其他可能需要的索引
ALTER TABLE shop_product ADD INDEX idx_product_code (product_code) IF NOT EXISTS;
ALTER TABLE shop_product ADD INDEX idx_product_name (product_name) IF NOT EXISTS;
ALTER TABLE shop_product ADD INDEX idx_product_type_status (product_type, status) IF NOT EXISTS;
ALTER TABLE shop_product ADD INDEX idx_provider_id (provider_id) IF NOT EXISTS;

-- 显示恢复后的索引结构
SELECT 'RESTORED INDEX STRUCTURE' as Info;
SHOW INDEX FROM shop_product;

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'RESTORE_INDEXES_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第五步：数据一致性验证
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CONSISTENCY_VALIDATION_START', NOW(), 'START');

-- 验证所有表的数据一致性是否回到原始状态
SELECT 'ROLLBACK CONSISTENCY VALIDATION' as Info;

-- 检查shop_product表
SELECT 
    'shop_product' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN id != product_id THEN 1 END) as inconsistent_id_product_id,
    COUNT(CASE WHEN product_id = id + 1 THEN 1 END) as product_id_offset_by_one
FROM shop_product

UNION ALL

-- 检查shop_order表的引用
SELECT 
    'shop_order_references' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT product_id FROM shop_product) THEN 1 END) as valid_product_id_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT product_id FROM shop_product) THEN 1 END) as invalid_product_id_references
FROM shop_order 
WHERE product_id IS NOT NULL

UNION ALL

-- 检查shop_proxy_instances表的引用
SELECT 
    'shop_proxy_instances_references' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT product_id FROM shop_product) THEN 1 END) as valid_product_id_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT product_id FROM shop_product) THEN 1 END) as invalid_product_id_references
FROM shop_proxy_instances 
WHERE product_id IS NOT NULL

UNION ALL

-- 检查shop_sync_product_detail表的引用
SELECT 
    'shop_sync_product_detail_references' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN product_id IN (SELECT product_id FROM shop_product) THEN 1 END) as valid_product_id_references,
    COUNT(CASE WHEN product_id NOT IN (SELECT product_id FROM shop_product) THEN 1 END) as invalid_product_id_references
FROM shop_sync_product_detail 
WHERE product_id IS NOT NULL;

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CONSISTENCY_VALIDATION_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第六步：清理迁移相关表
-- ==========================================

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CLEANUP_MIGRATION_TABLES_START', NOW(), 'START');

-- 可选：删除迁移过程中创建的表（保留备份表）
-- DROP TABLE IF EXISTS product_id_mapping;
-- DROP TABLE IF EXISTS migration_execution_log;
-- DROP TABLE IF EXISTS migration_validation_report_20250625;
-- DROP TABLE IF EXISTS migration_final_report_20250625;

-- 创建回滚完成报告
CREATE TABLE rollback_completion_report_20250625 AS
SELECT 
    'Product ID Migration Rollback Report' as report_type,
    NOW() as completed_at,
    @script_version as script_version,
    (SELECT COUNT(*) FROM shop_product) as shop_product_count,
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_column_restored,
    (SELECT COUNT(*) FROM shop_order WHERE product_id IS NOT NULL) as shop_order_with_product_id,
    (SELECT COUNT(*) FROM shop_proxy_instances WHERE product_id IS NOT NULL) as proxy_instances_with_product_id,
    (SELECT COUNT(*) FROM shop_sync_product_detail WHERE product_id IS NOT NULL) as sync_details_with_product_id,
    TIMESTAMPDIFF(SECOND, @start_time, NOW()) as rollback_duration_seconds,
    'ROLLBACK_COMPLETED' as status;

INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CLEANUP_MIGRATION_TABLES_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 重新启用外键检查和安全更新
-- ==========================================

SET SQL_SAFE_UPDATES = 1;
SET FOREIGN_KEY_CHECKS = 1;

-- 记录完成
INSERT INTO rollback_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'ROLLBACK_COMPLETE', NOW(), 'SUCCESS', 
CONCAT('Complete rollback completed in ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds'));

-- ==========================================
-- 执行结果报告
-- ==========================================

SELECT 'ROLLBACK EXECUTION SUMMARY' as Info;
SELECT 
    step_name,
    execution_time,
    status,
    affected_rows,
    notes
FROM rollback_execution_log 
WHERE script_name = @script_version 
ORDER BY id;

SELECT CONCAT('Rollback completed at: ', NOW()) as Info;
SELECT CONCAT('Total rollback time: ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds') as Info;

-- 显示最终状态
SELECT 'ROLLBACK COMPLETION REPORT' as Info;
SELECT * FROM rollback_completion_report_20250625;

-- ==========================================
-- 验证回滚成功
-- ==========================================

SELECT 'FINAL ROLLBACK VERIFICATION' as Info;
SELECT 
    'Original inconsistent state restored' as verification_note,
    COUNT(*) as total_products,
    COUNT(CASE WHEN product_id = id + 1 THEN 1 END) as products_with_offset_restored,
    COUNT(CASE WHEN id = product_id THEN 1 END) as products_with_consistent_ids,
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_column_exists
FROM shop_product;

SELECT '=== ROLLBACK OPERATION COMPLETED SUCCESSFULLY ===' as Status;