# 🚨 应急回滚和恢复方案

## 📋 回滚决策矩阵

### 立即回滚触发条件 (红色警报)
| 条件 | 阈值 | 影响级别 | 回滚策略 |
|------|------|----------|----------|
| 数据不一致性 | > 1% | CRITICAL | 立即执行完整回滚 |
| 孤立外键记录 | > 0 | CRITICAL | 立即执行完整回滚 |
| 核心功能失效 | 任何核心API故障 | CRITICAL | 立即执行完整回滚 |
| 迁移执行时间 | > 30分钟 | HIGH | 评估后决定回滚 |
| 业务中断 | > 5分钟 | HIGH | 立即执行完整回滚 |

### 延迟回滚触发条件 (黄色警报)
| 条件 | 阈值 | 影响级别 | 处理策略 |
|------|------|----------|----------|
| 性能下降 | > 20% | MEDIUM | 监控1小时后决定 |
| 非核心功能异常 | 个别功能点 | MEDIUM | 数据修复优先 |
| 日志错误增加 | > 正常10倍 | MEDIUM | 分析原因后决定 |

## 🔄 分级回滚策略

### Level 1: 应用层快速回滚 (< 5分钟)
```bash
# 1. 立即切换到维护模式
echo "MAINTENANCE_MODE=true" > /app/.env

# 2. 停止相关服务
systemctl stop shop-web
systemctl stop admin-web
systemctl stop api-server

# 3. 切换到备份版本
docker-compose -f docker-compose.backup.yml up -d

# 4. 验证基础服务
curl -f http://localhost:3000/health || echo "Service check failed"
```

### Level 2: 数据层部分回滚 (5-15分钟)
```sql
-- 1. 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- 2. 恢复关联表数据
UPDATE shop_order o
INNER JOIN order_migration_backup_20250625 b ON o.order_id = b.order_id
SET o.product_id = b.product_id;

UPDATE shop_proxy_instances pi
INNER JOIN proxy_instances_migration_backup_20250625 b ON pi.instance_id = b.instance_id
SET pi.product_id = b.product_id;

UPDATE shop_sync_product_detail spd
INNER JOIN sync_detail_migration_backup_20250625 b ON spd.detail_id = b.detail_id
SET spd.product_id = b.product_id;

-- 3. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;
```

### Level 3: 完整回滚 (15-30分钟)
```sql
-- 执行完整回滚脚本
SOURCE 04-rollback-complete.sql;

-- 验证核心指标
SELECT 
    'ROLLBACK_VERIFICATION' as check_type,
    (SELECT COUNT(*) FROM shop_product) as product_count,
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_exists,
    (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.product_id WHERE o.product_id IS NOT NULL AND p.product_id IS NULL) as orphaned_orders;
```

## 🛠️ 自动化回滚脚本

### 快速回滚检测脚本
```bash
#!/bin/bash
# emergency-rollback-detector.sh

# 设置阈值
MAX_ORPHANED_RECORDS=0
MAX_RESPONSE_TIME=5000  # 毫秒
MAX_ERROR_RATE=0.01     # 1%

# 检查数据完整性
ORPHANED_COUNT=$(mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} -sN -e "
    SELECT COUNT(*) FROM shop_order o 
    LEFT JOIN shop_product p ON o.product_id = p.id 
    WHERE o.product_id IS NOT NULL AND p.id IS NULL;
")

echo "Orphaned records: $ORPHANED_COUNT"

# 检查API响应时间
API_RESPONSE=$(curl -w "%{time_total}" -s -o /dev/null http://localhost:3000/api/shop/products)
API_TIME=$(echo "$API_RESPONSE * 1000" | bc)

echo "API response time: ${API_TIME}ms"

# 决策逻辑
if [ "$ORPHANED_COUNT" -gt "$MAX_ORPHANED_RECORDS" ]; then
    echo "❌ CRITICAL: Data integrity compromised"
    echo "Initiating emergency rollback..."
    ./execute-emergency-rollback.sh
    exit 1
fi

if [ "$(echo "$API_TIME > $MAX_RESPONSE_TIME" | bc)" -eq 1 ]; then
    echo "⚠️ WARNING: API response time exceeded threshold"
    echo "Consider performance rollback..."
    exit 2
fi

echo "✅ All checks passed"
exit 0
```

### 自动回滚执行脚本
```bash
#!/bin/bash
# execute-emergency-rollback.sh

echo "🚨 EMERGENCY ROLLBACK INITIATED"
echo "Timestamp: $(date)"

# 1. 立即通知相关人员
curl -X POST -H 'Content-type: application/json' \
    --data '{"text":"🚨 EMERGENCY: Database rollback initiated for product_id migration"}' \
    $SLACK_WEBHOOK_URL

# 2. 创建回滚日志
echo "$(date): Emergency rollback started" >> /var/log/emergency-rollback.log

# 3. 执行数据库回滚
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} < 04-rollback-complete.sql

if [ $? -eq 0 ]; then
    echo "✅ Database rollback completed successfully"
    echo "$(date): Database rollback completed" >> /var/log/emergency-rollback.log
else
    echo "❌ Database rollback failed"
    echo "$(date): Database rollback FAILED" >> /var/log/emergency-rollback.log
    exit 1
fi

# 4. 重启应用服务
systemctl restart api-server
systemctl restart shop-web
systemctl restart admin-web

# 5. 验证回滚成功
sleep 30
./emergency-rollback-detector.sh

if [ $? -eq 0 ]; then
    echo "✅ Emergency rollback completed successfully"
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"✅ Emergency rollback completed successfully"}' \
        $SLACK_WEBHOOK_URL
else
    echo "❌ Emergency rollback verification failed"
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"❌ Emergency rollback verification failed - Manual intervention required"}' \
        $SLACK_WEBHOOK_URL
fi
```

## 📊 监控和预警系统

### 实时监控仪表板
```sql
-- 创建监控视图
CREATE VIEW migration_health_monitor AS
SELECT 
    NOW() as check_time,
    
    -- 基础数据统计
    (SELECT COUNT(*) FROM shop_product) as product_count,
    (SELECT COUNT(*) FROM shop_order WHERE product_id IS NOT NULL) as order_count,
    (SELECT COUNT(*) FROM shop_proxy_instances WHERE product_id IS NOT NULL) as instance_count,
    
    -- 完整性指标
    (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.id WHERE o.product_id IS NOT NULL AND p.id IS NULL) as orphaned_orders,
    (SELECT COUNT(*) FROM shop_proxy_instances pi LEFT JOIN shop_product p ON pi.product_id = p.id WHERE pi.product_id IS NOT NULL AND p.id IS NULL) as orphaned_instances,
    
    -- 结构指标
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_exists,
    
    -- 健康状态
    CASE 
        WHEN (SELECT COUNT(*) FROM shop_order o LEFT JOIN shop_product p ON o.product_id = p.id WHERE o.product_id IS NOT NULL AND p.id IS NULL) = 0
             AND (SELECT COUNT(*) FROM shop_proxy_instances pi LEFT JOIN shop_product p ON pi.product_id = p.id WHERE pi.product_id IS NOT NULL AND p.id IS NULL) = 0
             AND (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') = 0
        THEN 'HEALTHY'
        ELSE 'CRITICAL'
    END as health_status;
```

### 预警触发器
```sql
-- 创建预警触发器
DELIMITER $$

CREATE TRIGGER migration_health_alert
AFTER INSERT ON shop_order
FOR EACH ROW
BEGIN
    DECLARE orphaned_count INT DEFAULT 0;
    
    -- 检查新插入的订单是否引用了无效的产品ID
    SELECT COUNT(*) INTO orphaned_count
    FROM shop_order o
    LEFT JOIN shop_product p ON o.product_id = p.id
    WHERE o.product_id = NEW.product_id AND p.id IS NULL;
    
    -- 如果发现孤立记录，插入警报
    IF orphaned_count > 0 THEN
        INSERT INTO migration_alert_log (alert_time, alert_type, message, severity)
        VALUES (NOW(), 'ORPHANED_REFERENCE', 
                CONCAT('New order ', NEW.order_id, ' references invalid product_id: ', NEW.product_id),
                'CRITICAL');
    END IF;
END$$

DELIMITER ;
```

## 🔧 数据修复工具集

### 自动数据修复脚本
```sql
-- auto-repair-toolkit.sql

-- 1. 修复孤立的订单记录
UPDATE shop_order 
SET product_id = NULL, 
    order_type = 'INVALID_PRODUCT_REFERENCE',
    notes = CONCAT(IFNULL(notes, ''), ' [Auto-repaired: Invalid product reference]')
WHERE product_id NOT IN (SELECT id FROM shop_product)
AND product_id IS NOT NULL;

-- 2. 修复孤立的代理实例（需要业务确认）
-- 选项A: 设置为NULL
UPDATE shop_proxy_instances 
SET product_id = NULL,
    status = 'SUSPENDED',
    notes = CONCAT(IFNULL(notes, ''), ' [Auto-repaired: Invalid product reference]')
WHERE product_id NOT IN (SELECT id FROM shop_product)
AND product_id IS NOT NULL;

-- 选项B: 删除记录（高风险操作）
-- DELETE FROM shop_proxy_instances 
-- WHERE product_id NOT IN (SELECT id FROM shop_product);

-- 3. 清理同步历史记录
DELETE FROM shop_sync_product_detail 
WHERE product_id NOT IN (SELECT id FROM shop_product)
AND product_id IS NOT NULL;
```

## 📞 应急联系流程

### 紧急联系人
```yaml
Emergency Contacts:
  - Primary DBA: +86-xxx-xxxx-xxxx
  - Lead Developer: +86-xxx-xxxx-xxxx  
  - System Admin: +86-xxx-xxxx-xxxx
  - Business Owner: +86-xxx-xxxx-xxxx

Communication Channels:
  - Slack: #emergency-alerts
  - Email: <EMAIL>
  - Phone: Emergency hotline

Escalation Timeline:
  - 0-5 min: Technical team
  - 5-15 min: Lead developer + DBA
  - 15-30 min: Management team
  - 30+ min: Business stakeholders
```

### 事故响应检查清单
```markdown
## 🚨 Emergency Response Checklist

### 立即响应 (0-5分钟)
- [ ] 确认问题严重性
- [ ] 启动应急响应
- [ ] 通知技术团队
- [ ] 开始记录事故日志

### 问题评估 (5-10分钟)
- [ ] 运行诊断脚本
- [ ] 检查监控指标
- [ ] 评估业务影响
- [ ] 决定回滚策略

### 执行回滚 (10-30分钟)
- [ ] 执行相应级别回滚
- [ ] 监控回滚进度
- [ ] 验证系统恢复
- [ ] 更新事故状态

### 事后处理 (30分钟+)
- [ ] 全面系统检查
- [ ] 根因分析
- [ ] 更新应急预案
- [ ] 团队复盘会议
```

## 📝 回滚后验证清单

### 数据完整性验证
```sql
-- 执行全面验证
SOURCE 07-data-consistency-validator.sql;

-- 检查关键指标
SELECT 
    'POST_ROLLBACK_VERIFICATION' as check_type,
    NOW() as check_time,
    (SELECT COUNT(*) FROM shop_product) as product_count,
    (SELECT COUNT(*) FROM shop_order) as order_count,
    (SELECT overall_status FROM migration_validation_report_detailed LIMIT 1) as validation_status;
```

### 业务功能验证
```bash
# API功能测试
curl -f http://localhost:3000/api/shop/products || echo "Products API failed"
curl -f http://localhost:3000/api/shop/orders || echo "Orders API failed"

# 前端页面测试  
curl -f http://localhost:3001/ || echo "Shop frontend failed"
curl -f http://localhost:3002/ || echo "Admin frontend failed"
```

通过这套完整的应急回滚和恢复方案，你可以在任何阶段快速、安全地恢复系统到稳定状态，确保业务连续性和数据安全。