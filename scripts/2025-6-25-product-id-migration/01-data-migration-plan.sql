-- ==========================================
-- 激进方案：全局删除product_id字段数据迁移计划
-- 创建日期: 2025-06-25
-- 目标: 统一使用主键id，删除product_id字段
-- 风险级别: HIGH - 重大架构变更
-- ==========================================

-- 设置会话变量
SET @start_time = NOW();
SET @script_version = 'product-id-migration-v1.0';

-- 创建迁移日志表
CREATE TABLE IF NOT EXISTS migration_execution_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    script_name VARCHAR(100),
    step_name VARCHAR(200),
    execution_time DATETIME,
    status ENUM('START', 'SUCCESS', 'ERROR', 'ROLLBACK'),
    error_message TEXT,
    affected_rows INT DEFAULT 0,
    notes TEXT
);

-- 记录迁移开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'MIGRATION_START', @start_time, 'START', 'Starting product_id field deletion migration');

-- ==========================================
-- 第一阶段：数据一致性分析
-- ==========================================

-- 记录分析开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DATA_ANALYSIS_START', NOW(), 'START');

-- 1. 检查shop_product表的数据一致性
SELECT 'SHOP_PRODUCT DATA CONSISTENCY CHECK' as Info;
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN id = product_id THEN 1 END) as consistent_records,
    COUNT(CASE WHEN id != product_id THEN 1 END) as inconsistent_records,
    COUNT(CASE WHEN product_id = id + 1 THEN 1 END) as offset_by_one,
    MIN(id) as min_id,
    MAX(id) as max_id,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_product;

-- 2. 检查各关联表的product_id使用情况
SELECT 'RELATED TABLES PRODUCT_ID USAGE' as Info;

-- 订单表
SELECT 'shop_order' as table_name, COUNT(*) as total_records, 
       COUNT(DISTINCT product_id) as distinct_product_ids,
       MIN(product_id) as min_product_id, MAX(product_id) as max_product_id
FROM shop_order WHERE product_id IS NOT NULL
UNION ALL
-- 代理实例表
SELECT 'shop_proxy_instances' as table_name, COUNT(*) as total_records,
       COUNT(DISTINCT product_id) as distinct_product_ids,
       MIN(product_id) as min_product_id, MAX(product_id) as max_product_id
FROM shop_proxy_instances WHERE product_id IS NOT NULL
UNION ALL
-- 同步产品详情表
SELECT 'shop_sync_product_detail' as table_name, COUNT(*) as total_records,
       COUNT(DISTINCT product_id) as distinct_product_ids,
       MIN(product_id) as min_product_id, MAX(product_id) as max_product_id
FROM shop_sync_product_detail WHERE product_id IS NOT NULL;

-- 3. 检查外键约束和索引
SELECT 'FOREIGN KEY CONSTRAINTS CHECK' as Info;
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND (REFERENCED_TABLE_NAME = 'shop_product' OR TABLE_NAME = 'shop_product')
AND COLUMN_NAME LIKE '%product_id%'
ORDER BY TABLE_NAME;

-- 记录分析完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DATA_ANALYSIS_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第二阶段：数据映射策略
-- ==========================================

-- 记录映射策略开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'MAPPING_STRATEGY_START', NOW(), 'START');

-- 创建产品ID映射表
CREATE TABLE IF NOT EXISTS product_id_mapping (
    old_product_id INT NOT NULL COMMENT '原product_id值',
    new_id INT NOT NULL COMMENT '对应的主键id值',
    product_name VARCHAR(255) COMMENT '产品名称（用于验证）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (old_product_id),
    UNIQUE KEY uk_new_id (new_id)
) COMMENT '产品ID映射表，用于追踪迁移过程';

-- 填充映射表
INSERT INTO product_id_mapping (old_product_id, new_id, product_name)
SELECT product_id, id, product_name
FROM shop_product
ORDER BY id;

-- 验证映射表
SELECT 'PRODUCT ID MAPPING VERIFICATION' as Info;
SELECT 
    COUNT(*) as total_mappings,
    COUNT(CASE WHEN old_product_id = new_id + 1 THEN 1 END) as offset_mappings,
    MIN(old_product_id - new_id) as min_offset,
    MAX(old_product_id - new_id) as max_offset
FROM product_id_mapping;

-- 记录映射策略完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'MAPPING_STRATEGY_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第三阶段：关联表数据更新计划
-- ==========================================

-- 记录更新计划开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'UPDATE_PLAN_START', NOW(), 'START');

-- 生成更新计划摘要
SELECT 'TABLES TO UPDATE SUMMARY' as Info;
SELECT 
    'shop_order' as table_name,
    'product_id -> id (使用映射表)' as update_strategy,
    COUNT(*) as records_to_update
FROM shop_order o 
INNER JOIN product_id_mapping m ON o.product_id = m.old_product_id
UNION ALL
SELECT 
    'shop_proxy_instances' as table_name,
    'product_id -> id (使用映射表)' as update_strategy,
    COUNT(*) as records_to_update
FROM shop_proxy_instances pi
INNER JOIN product_id_mapping m ON pi.product_id = m.old_product_id
UNION ALL
SELECT 
    'shop_sync_product_detail' as table_name,
    'product_id -> id (使用映射表)' as update_strategy,
    COUNT(*) as records_to_update
FROM shop_sync_product_detail spd
INNER JOIN product_id_mapping m ON spd.product_id = m.old_product_id;

-- 记录更新计划完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'UPDATE_PLAN_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第四阶段：回滚准备
-- ==========================================

-- 记录回滚准备开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'ROLLBACK_PREP_START', NOW(), 'START');

-- 创建备份表
CREATE TABLE product_migration_backup_20250625 AS
SELECT 
    'shop_product' as source_table,
    id, product_id, product_name, product_code
FROM shop_product;

CREATE TABLE order_migration_backup_20250625 AS
SELECT 
    'shop_order' as source_table,
    order_id, product_id, customer_id, order_no
FROM shop_order;

CREATE TABLE proxy_instances_migration_backup_20250625 AS
SELECT 
    'shop_proxy_instances' as source_table,
    instance_id, product_id, customer_id, instance_name
FROM shop_proxy_instances;

CREATE TABLE sync_detail_migration_backup_20250625 AS
SELECT 
    'shop_sync_product_detail' as source_table,
    detail_id, sync_id, product_id, action
FROM shop_sync_product_detail;

-- 记录回滚准备完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'ROLLBACK_PREP_COMPLETE', NOW(), 'SUCCESS', 'Backup tables created for rollback capability');

-- ==========================================
-- 执行总结
-- ==========================================

SELECT 'MIGRATION PLAN ANALYSIS COMPLETE' as Info;
SELECT 
    step_name,
    execution_time,
    status,
    notes
FROM migration_execution_log 
WHERE script_name = @script_version 
ORDER BY id;

SELECT CONCAT('Analysis completed at: ', NOW()) as Info;
SELECT CONCAT('Total execution time: ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds') as Info;

-- ==========================================
-- 下一步行动计划
-- ==========================================

/*
NEXT STEPS EXECUTION PLAN:
==========================

1. 执行外键约束删除脚本（fix-foreign-keys-production.sql）
2. 执行关联表数据更新脚本（02-update-related-tables.sql）
3. 执行TypeORM实体配置更新
4. 执行shop_product表结构变更脚本（03-alter-shop-product.sql）
5. 执行应用代码更新
6. 进行全面测试验证

CRITICAL SUCCESS FACTORS:
========================
- 数据一致性验证通过
- 完整备份和回滚机制
- 分步骤执行，每步验证
- 应用代码同步更新
- 全面功能测试

ROLLBACK TRIGGERS:
==================
- 数据不一致性发现
- 关键功能失效
- 性能严重下降
- 业务中断超过容忍度
*/