# 前端字段命名策略

## 🎯 **核心原则**

为了避免混淆，前端采用以下字段命名策略：

### 1. **产品实体 (Product)**
```typescript
interface shopProduct {
  id: number;           // ✅ 产品主键ID，统一使用id
  productName: string;  // 产品名称
  // ... 其他字段
}
```

### 2. **订单实体 (Order)**
```typescript
interface Order {
  orderId: string;                 // 订单ID
  productId: number | null;        // ✅ 关联的产品ID，充值订单时为null
  // ... 其他字段
}
```

### 3. **API调用参数**

#### 创建静态IP订单 (与后端API一致)
```typescript
interface CreateStaticIpOrderDto {
  id: string;          // ✅ 后端期望接收id字段
  quantity: number;
  duration: number;
  // ...
}
```

#### 创建普通订单 (保持兼容性)
```typescript
interface CreateOrderParams {
  productId: number;   // ✅ 保持传统命名
  quantity: number;
  // ...
}
```

### 4. **状态管理 (Store)**
```typescript
interface StoreState {
  selectedId: number | null;       // ✅ 选中的产品ID
  selectedLocationId: string | number | null;
  // ...
}
```

## 🔄 **字段使用场景**

### ✅ **使用 `id` 的场景**
1. **Product实体**: `product.id`
2. **产品查询**: `getProductById(id)`
3. **产品选择**: `setSelectedId(id)`
4. **静态IP订单创建**: `{ id: "123" }`

### ✅ **使用 `productId` 的场景**
1. **Order实体**: `order.productId`
2. **订单查询**: `order.productId === product.id`
3. **普通订单创建**: `{ productId: 123 }`
4. **代理实例**: `proxy.productId`

## ⚠️ **特殊情况处理**

### 充值订单
```typescript
// 充值订单时，productId为null
const rechargeOrder: Order = {
  orderId: "RECHARGE_001",
  productId: null,  // ✅ 充值订单没有关联产品
  orderType: "RECHARGE",
  // ...
}
```

### 前端组件中的适配
```typescript
// 从URL参数获取产品ID
const productId = searchParams.get("productId");  // URL参数保持productId
const product = await getProductById(productId);  // API调用时转换为id

// 订单创建时的字段映射
const orderData = {
  id: product.id,  // 静态IP订单API要求id字段
  quantity: 1,
  // ...
};
```

## 📋 **Migration Checklist**

### ✅ 已完成
- [x] 类型定义更新
- [x] API接口定义修改  
- [x] 状态管理字段统一
- [x] 产品相关服务更新

### 🔄 进行中
- [ ] 组件代码适配
- [ ] URL参数处理
- [ ] 表单字段映射

### ⏳ 待完成
- [ ] 全面测试验证
- [ ] 文档更新

## 🎉 **最终效果**

实现前后端字段名的最大一致性，同时保持业务逻辑的清晰性：

```typescript
// 前端
const product = await getProductById(id);        // ✅ 统一使用id
const order = await createOrder({ productId });  // ✅ 订单中保持productId引用

// 后端
product.id                                       // ✅ 产品主键
order.productId                                  // ✅ 订单的产品引用
```

这种策略既满足了统一性要求，又避免了业务逻辑中的混淆。