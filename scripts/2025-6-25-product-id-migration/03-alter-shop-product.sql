-- ==========================================
-- shop_product表结构变更脚本
-- 创建日期: 2025-06-25
-- 目标: 删除product_id字段，使用主键id作为唯一标识
-- 风险级别: HIGH - 表结构关键变更
-- ==========================================

-- 设置会话变量
SET @start_time = NOW();
SET @script_version = 'shop-product-alter-v1.0';

-- 记录开始
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'SHOP_PRODUCT_ALTER_START', @start_time, 'START', 'Starting shop_product table structure modification');

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 第一步：备份当前表结构和数据
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'BACKUP_TABLE_START', NOW(), 'START');

-- 创建完整的表结构和数据备份
CREATE TABLE shop_product_complete_backup_20250625 AS 
SELECT * FROM shop_product;

-- 备份表结构定义
CREATE TABLE shop_product_structure_backup_20250625 (
    backup_info TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 记录原表结构
INSERT INTO shop_product_structure_backup_20250625 (backup_info)
SELECT CONCAT('Original CREATE TABLE statement: ', CREATE_OPTIONS) 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product';

-- 记录所有索引信息
CREATE TABLE shop_product_indexes_backup_20250625 AS
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE,
    INDEX_TYPE,
    INDEX_COMMENT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'shop_product'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'BACKUP_TABLE_COMPLETE', NOW(), 'SUCCESS', 'Complete table backup created');

-- ==========================================
-- 第二步：验证数据完整性
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DATA_INTEGRITY_CHECK_START', NOW(), 'START');

-- 最终数据完整性检查
SELECT 'FINAL DATA INTEGRITY CHECK BEFORE ALTER' as Info;
SELECT 
    COUNT(*) as total_products,
    COUNT(DISTINCT id) as distinct_ids,
    COUNT(DISTINCT product_id) as distinct_product_ids,
    COUNT(CASE WHEN id = product_id THEN 1 END) as id_equals_product_id,
    COUNT(CASE WHEN product_id = id + 1 THEN 1 END) as product_id_offset_by_one,
    MIN(id) as min_id,
    MAX(id) as max_id,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM shop_product;

-- 检查是否有空值
SELECT 
    COUNT(CASE WHEN id IS NULL THEN 1 END) as null_ids,
    COUNT(CASE WHEN product_id IS NULL THEN 1 END) as null_product_ids
FROM shop_product;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DATA_INTEGRITY_CHECK_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第三步：删除product_id相关的索引
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DROP_INDEXES_START', NOW(), 'START');

-- 删除可能存在的product_id相关索引
ALTER TABLE shop_product DROP INDEX IF EXISTS uk_product_id;
ALTER TABLE shop_product DROP INDEX IF EXISTS idx_product_id;
ALTER TABLE shop_product DROP INDEX IF EXISTS product_id;

-- 记录删除的索引
SELECT 'INDEXES DROPPED' as Info;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DROP_INDEXES_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第四步：删除product_id字段
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'DROP_PRODUCT_ID_COLUMN_START', NOW(), 'START');

-- 关键步骤：删除product_id字段
ALTER TABLE shop_product DROP COLUMN product_id;

-- 验证字段已删除
SELECT 'PRODUCT_ID COLUMN VERIFICATION' as Info;
SELECT COUNT(*) as product_id_column_exists
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'shop_product' 
AND COLUMN_NAME = 'product_id';

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'DROP_PRODUCT_ID_COLUMN_COMPLETE', NOW(), 'SUCCESS', 'product_id column successfully dropped');

-- ==========================================
-- 第五步：优化表结构和索引
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'OPTIMIZE_TABLE_START', NOW(), 'START');

-- 确保主键存在且正确
ALTER TABLE shop_product DROP PRIMARY KEY;
ALTER TABLE shop_product ADD PRIMARY KEY (id);

-- 重新创建关键索引
ALTER TABLE shop_product ADD INDEX idx_product_code (product_code);
ALTER TABLE shop_product ADD INDEX idx_product_name (product_name);
ALTER TABLE shop_product ADD INDEX idx_product_type_status (product_type, status);
ALTER TABLE shop_product ADD INDEX idx_provider_id (provider_id);

-- 如果有地理位置相关字段，添加对应索引
ALTER TABLE shop_product ADD INDEX idx_location (location_city_id) IF NOT EXISTS;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'OPTIMIZE_TABLE_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第六步：表结构验证
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'STRUCTURE_VALIDATION_START', NOW(), 'START');

-- 显示新的表结构
SELECT 'NEW TABLE STRUCTURE' as Info;
SHOW CREATE TABLE shop_product;

-- 显示新的索引结构
SELECT 'NEW INDEX STRUCTURE' as Info;
SHOW INDEX FROM shop_product;

-- 验证数据完整性
SELECT 'POST-ALTER DATA VERIFICATION' as Info;
SELECT 
    COUNT(*) as total_products,
    COUNT(DISTINCT id) as distinct_ids,
    MIN(id) as min_id,
    MAX(id) as max_id,
    COUNT(CASE WHEN id IS NULL THEN 1 END) as null_ids
FROM shop_product;

-- 与备份表比较记录数
SELECT 'RECORD COUNT COMPARISON' as Info;
SELECT 
    (SELECT COUNT(*) FROM shop_product) as current_count,
    (SELECT COUNT(*) FROM shop_product_complete_backup_20250625) as backup_count,
    (SELECT COUNT(*) FROM shop_product) - (SELECT COUNT(*) FROM shop_product_complete_backup_20250625) as difference;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'STRUCTURE_VALIDATION_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 第七步：清理映射表
-- ==========================================

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CLEANUP_START', NOW(), 'START');

-- 保留映射表用于回滚，但可以考虑在确认成功后删除
-- DROP TABLE IF EXISTS product_id_mapping;

-- 创建最终迁移报告
CREATE TABLE migration_final_report_20250625 AS
SELECT 
    'shop_product Migration Final Report' as report_type,
    NOW() as completed_at,
    @script_version as script_version,
    (SELECT COUNT(*) FROM shop_product) as final_product_count,
    (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'shop_product' AND COLUMN_NAME = 'product_id') as product_id_column_exists,
    TIMESTAMPDIFF(SECOND, @start_time, NOW()) as total_execution_seconds;

INSERT INTO migration_execution_log (script_name, step_name, execution_time, status)
VALUES (@script_version, 'CLEANUP_COMPLETE', NOW(), 'SUCCESS');

-- ==========================================
-- 重新启用外键检查
-- ==========================================

SET FOREIGN_KEY_CHECKS = 1;

-- 记录完成
INSERT INTO migration_execution_log (script_name, step_name, execution_time, status, notes)
VALUES (@script_version, 'SHOP_PRODUCT_ALTER_COMPLETE', NOW(), 'SUCCESS', 
CONCAT('shop_product table modification completed in ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds'));

-- ==========================================
-- 执行结果报告
-- ==========================================

SELECT 'SHOP_PRODUCT ALTER SUMMARY' as Info;
SELECT 
    step_name,
    execution_time,
    status,
    notes
FROM migration_execution_log 
WHERE script_name = @script_version 
ORDER BY id;

SELECT CONCAT('Table modification completed at: ', NOW()) as Info;
SELECT CONCAT('Total execution time: ', TIMESTAMPDIFF(SECOND, @start_time, NOW()), ' seconds') as Info;

-- 显示最终状态
SELECT 'FINAL STATUS SUMMARY' as Info;
SELECT * FROM migration_final_report_20250625;

-- ==========================================
-- 回滚脚本（紧急恢复）
-- ==========================================

/*
EMERGENCY ROLLBACK INSTRUCTIONS:
================================

如果需要紧急回滚到原始状态，请执行以下步骤：

1. 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

2. 重命名当前表
RENAME TABLE shop_product TO shop_product_modified_backup;

3. 从备份恢复原始表
CREATE TABLE shop_product AS SELECT * FROM shop_product_complete_backup_20250625;

4. 恢复原始主键
ALTER TABLE shop_product ADD PRIMARY KEY (id);

5. 从索引备份恢复索引结构
-- 根据 shop_product_indexes_backup_20250625 表中的信息手动重建索引

6. 恢复关联表数据
-- 执行 02-update-related-tables.sql 的回滚部分

7. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

WARNING: 回滚操作将丢失所有迁移后的改进
*/