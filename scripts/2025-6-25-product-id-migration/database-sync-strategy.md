# 数据库同步策略方案

## 🎯 目标环境

### 测试环境 (已调试完成)
- **Host**: `**************:34781`
- **Database**: `proxy_shop`
- **状态**: 迁移脚本已调试完成，数据结构已验证

### 生产环境 (待迁移)
- **Host**: `proxy-shop-db-mysql-8:3306` 
- **Database**: `proxy_shop`
- **状态**: 需要执行同步迁移

## 📋 同步执行策略

### 阶段一：预迁移数据同步 (Pre-Migration Sync)

#### 1.1 生产环境数据备份
```sql
-- 创建完整数据备份
CREATE DATABASE proxy_shop_backup_20250625;

-- 备份关键表
CREATE TABLE proxy_shop_backup_20250625.shop_product AS SELECT * FROM shop_product;
CREATE TABLE proxy_shop_backup_20250625.shop_order AS SELECT * FROM shop_order;
CREATE TABLE proxy_shop_backup_20250625.shop_proxy_instances AS SELECT * FROM shop_proxy_instances;
CREATE TABLE proxy_shop_backup_20250625.shop_sync_product_detail AS SELECT * FROM shop_sync_product_detail;
```

#### 1.2 数据一致性预分析
```sql
-- 在生产环境执行数据分析（01-data-migration-plan.sql的分析部分）
-- 对比测试环境的分析结果，确保数据结构一致性
```

### 阶段二：同步测试环境改进到生产 (Test → Production)

#### 2.1 结构同步检查
```sql
-- 比较两个环境的表结构
SELECT 
    'PRODUCTION' as env,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'proxy_shop'
AND TABLE_NAME IN ('shop_product', 'shop_order', 'shop_proxy_instances', 'shop_sync_product_detail')

UNION ALL

-- 在测试环境执行相同查询进行对比
```

#### 2.2 数据差异识别
```sql
-- 识别生产环境独有的数据
-- 这些数据在测试环境不存在，需要特殊处理
```

### 阶段三：生产环境迁移执行 (Production Migration)

#### 3.1 维护窗口设置
```bash
# 设置应用只读模式
# 停止相关服务写入操作
```

#### 3.2 执行迁移脚本序列
```sql
-- 1. 数据分析和备份
SOURCE 01-data-migration-plan.sql;

-- 2. 关联表更新
SOURCE 02-update-related-tables.sql;

-- 3. 主表结构变更
SOURCE 03-alter-shop-product.sql;

-- 4. 验证和清理
-- 执行数据一致性检查
```

### 阶段四：生产数据修复 (Production Data Repair)

#### 4.1 增量数据处理
```sql
-- 处理生产环境独有的数据记录
-- 这些数据在迁移映射表中可能不存在

-- 创建增量映射
INSERT INTO product_id_mapping (old_product_id, new_id, product_name)
SELECT DISTINCT 
    o.product_id as old_product_id,
    COALESCE(p.id, o.product_id - 1) as new_id,
    COALESCE(p.product_name, 'UNKNOWN_PRODUCT') as product_name
FROM shop_order o
LEFT JOIN shop_product p ON p.id = o.product_id - 1
WHERE o.product_id NOT IN (SELECT old_product_id FROM product_id_mapping);
```

#### 4.2 孤立数据清理
```sql
-- 识别和处理孤立的外键引用
SELECT 'ORPHANED_ORDERS' as type, COUNT(*) as count
FROM shop_order o
LEFT JOIN shop_product p ON o.product_id = p.id
WHERE p.id IS NULL AND o.product_id IS NOT NULL

UNION ALL

SELECT 'ORPHANED_INSTANCES' as type, COUNT(*) as count  
FROM shop_proxy_instances pi
LEFT JOIN shop_product p ON pi.product_id = p.id
WHERE p.id IS NULL AND pi.product_id IS NOT NULL;
```

## 🔐 数据一致性保障机制

### 验证检查点
```sql
-- 1. 记录数量一致性
SELECT 
    'BEFORE_MIGRATION' as checkpoint,
    (SELECT COUNT(*) FROM shop_product) as products,
    (SELECT COUNT(*) FROM shop_order WHERE product_id IS NOT NULL) as orders,
    (SELECT COUNT(*) FROM shop_proxy_instances WHERE product_id IS NOT NULL) as instances;

-- 迁移后执行相同查询验证
```

### 业务逻辑验证
```sql
-- 2. 业务关系完整性
SELECT 
    o.order_id,
    o.product_id,
    p.id as product_primary_key,
    p.product_name,
    CASE 
        WHEN o.product_id = p.id THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
    END as status
FROM shop_order o
INNER JOIN shop_product p ON o.product_id = p.id
LIMIT 10;
```

## 🚨 应急回滚方案

### 立即回滚触发条件
1. 数据不一致性 > 1%
2. 关键业务功能失效
3. 外键约束错误 > 0
4. 迁移执行时间 > 30分钟

### 快速回滚执行
```sql
-- 执行完整回滚脚本
SOURCE 04-rollback-complete.sql;

-- 验证回滚成功
SELECT 'POST_ROLLBACK_VERIFICATION' as status,
       COUNT(*) as product_count,
       COUNT(CASE WHEN product_id IS NOT NULL THEN 1 END) as products_with_product_id
FROM shop_product;
```

## 📊 监控和告警

### 关键指标监控
1. **数据量变化**: 迁移前后记录数对比
2. **执行时间**: 每个步骤的耗时监控  
3. **错误率**: 失败操作的百分比
4. **业务影响**: 核心功能可用性检查

### 成功标准
- ✅ 所有表记录数量保持不变
- ✅ 外键引用100%有效
- ✅ 核心业务功能正常
- ✅ 应用接口响应正常
- ✅ 无数据丢失或损坏

## 🎯 执行时间规划

### 建议执行窗口
- **时间**: 凌晨2:00-6:00 (业务低峰期)
- **准备时间**: 1小时
- **执行时间**: 2-3小时  
- **验证时间**: 1小时
- **缓冲时间**: 1小时

### 人员配置
- **DBA**: 负责脚本执行和数据库监控
- **后端开发**: 负责应用层验证和业务测试
- **运维**: 负责服务状态监控和应急响应
- **测试**: 负责功能验证和回归测试