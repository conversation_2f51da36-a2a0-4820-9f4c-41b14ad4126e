{"permissions": {"allow": ["Bash(git checkout:*)", "Bash(git pull:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "mcp__mysql__execute_sql", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(tree:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git restore:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(uvx mcp-feedback-enhanced@latest test:*)", "Bash(uv tool install:*)", "Bash(mcp-feedback-enhanced test:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(sudo ln:*)", "Bash(ls:*)", "<PERSON><PERSON>(uv:*)", "Bash(sudo kill:*)", "Bash(sudo rm:*)", "Bash(sudo dpkg:*)", "Bash(git stash:*)", "Bash(git pull:*)", "Bash(git rm:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(tree:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(hash -r)", "<PERSON><PERSON>(claude -v)", "Bash(git branch:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "mcp__mcp-hub__dbhub-execute_sql", "Bash(git commit:*)", "mcp__mcp-feedback-enhanced__interactive_feedback", "mcp__mcp-hub__context7-resolve-library-id", "mcp__mcp-hub__context7-get-library-docs", "Bash(node:*)", "Bash(ss:*)", "Bash(npx tsc:*)", "Bash(./shop-web/node_modules/.bin/tsc:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx:*)", "Bash(npm --prefix shop-web run build:prod)", "Bash(bash:*)", "Bash(npm run lint)", "mcp__zen__analyze", "Bash(rg:*)", "mcp__zen__thinkdeep", "mcp__zen__chat", "mcp__zen__codereview", "mcp__zen__precommit", "mcp__mcp-hub__sequential-thinking-sequentialthinking", "WebFetch(domain:webaim.org)", "mcp__mcp-hub__time-get_current_time", "mcp__zen__debug", "Bash(npm run logs:error:*)", "mcp__zen__debug", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(rg:*)", "<PERSON><PERSON>(comm:*)", "mcp__zen__analyze", "WebFetch(domain:zpayz.cn)", "Bash(ping:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm test:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(cat:*)", "mcp__mcp-hub__server-redis-mcp-list", "mcp__mcp-hub__server-redis-mcp-get", "mcp__zen__planner", "mcp__zen__listmodels", "mcp__zen__consensus", "mcp__mcp-hub__tavily-mcp-tavily-search", "mcp__ide__getDiagnostics", "mcp__ide__executeCode", "<PERSON><PERSON>(chmod:*)", "Bash(./execute-fix-production.sh:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(apt search:*)", "<PERSON><PERSON>(mysql:*)", "Bash(kill:*)", "Bash(./scripts/get-admin-token.sh:*)", "Bash(git merge:*)", "Bash(git update-index:*)", "Bash(git reset:*)", "mcp__serena__find_file", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__find_symbol", "mcp__serena__read_file", "mcp__serena__execute_shell_command"], "deny": ["mcp__serena__create_text_file", "mcp__serena__delete_lines", "mcp__serena__insert_at_line", "mcp__serena__insert_after_symbol", "mcp__serena__insert_before_symbol", "mcp__serena__replace_lines", "mcp__serena__replace_symbol_body"], "env": {"development": {"MYSQL_HOST": "**************", "MYSQL_PORT": "34781", "MYSQL_USER": "root", "MYSQL_PASSWORD": "UPWEOgIvx9vL9QLvzmJL", "MYSQL_DATABASE": "proxy_shop"}, "prod": {"MYSQL_HOST": "**************", "MYSQL_PORT": "37443", "MYSQL_USER": "root", "MYSQL_PASSWORD": "xxx", "MYSQL_DATABASE": "proxy_shop"}}}}