---
description:
globs:
alwaysApply: true
---
# GoMaskIP 项目总览与规则

## 配色方案

主色调: #865DDC (丁香紫)
辅助色: #57C5DC (湖水蓝)
强调色: #80DEEA (明亮青蓝)
背景色: #F8F7FC (淡雅紫灰) 和 #FFFFFF (纯白)
主要文本: #1A202C (深板岩灰)
次要文本: #718096 (中性灰蓝)
成功: #48BB78 (清新绿)
错误: #F56565 (柔和红)
警告: #ED8936 (活力橙)
信息: #4299E1 (天空蓝)

## 1. 项目结构与用户类型

我的项目实际路径: C:\git\github\proxy-shop

-   `server/`: 后端项目 (NestJS)
-   `admin-web/`: Web 管理后台前端项目 (Vue)
-   `shop-web/`: 代理商城官网前端项目 (React)



### 用户类型

-   **商城用户 (Customer)**:
    -   通过 `shop-web/` 注册的用户。
    -   实体文件: `server/src/module/shop/auth/entities`
-   **后台管理用户 (User)**:
    -   用于管理后台的用户。
    -   实体文件: `server/src/module/system/user/entities/sys-user.entity.ts`

## 2. 各项目技术栈及详细规则

### 2.1 server (proxy-server)

-   **描述**: 后端服务
-   **主要技术栈**:
    -   **框架**: NestJS (v10)
    -   **HTTP 客户端**: Axios
    -   **数据库**: TypeORM (与MySQL2配合)
    -   **缓存**: ioredis (Redis)
    -   **认证**: Passport, JWT, bcryptjs
    -   **配置**: @nestjs/config, dotenv
    -   **日志**: Winston, winston-daily-rotate-file
    -   **任务调度**: @nestjs/schedule, cron
    -   **API 文档**: Swagger (@nestjs/swagger)
    -   **文件操作**: fs-extra, archiver, cos-nodejs-sdk-v5 (腾讯云COS)
    -   **数据验证/转换**: class-validator, class-transformer
    -   **工具库**: lodash, dayjs, uuid, chalk, js-yaml, iconv-lite, mime-types, useragent
    -   **其他**: express-rate-limit, helmet, @nestjs/event-emitter### 2.2 admin-web (若依管理系统)

-   **描述**: 管理后台前端
-   **主要技术栈**:
    -   **框架**: Vue 3
    -   **UI 组件库**: Element Plus
    -   **状态管理**: Pinia
    -   **构建工具**: Vite
    -   **路由**: Vue Router
    -   **HTTP 客户端**: Axios
    -   **富文本编辑器**: @vueup/vue-quill
    -   **图表**: Echarts
    -   **日期处理**: Day.js
    -   **其他工具**: clipboard, file-saver, fuse.js, js-beautify, js-cookie, jsencrypt, nprogress, vue-cropper, vuedraggable

### 2.3 shop-web (proxy-shop 前端)

-   **描述**: 代理商店前端
-   **主要技术栈**:
    -   **框架**: React (v18)
    -   **构建工具**: Vite
    -   **UI 框架/组件**: Tailwind CSS, Radix UI (大量 `@radix-ui/react-*` 组件), Shadcn/ui (可能，基于Radix UI和Tailwind)
    -   **状态管理**: Zustand, zukeeper, zustand-devtools
    -   **表单管理**: React Hook Form, @hookform/resolvers (与Zod配合)
    **数据请求**: React Query (@tanstack/react-query), Axios
    **路由**: React Router DOM
    **动画/交互**: cmdk, embla-carousel-react, input-otp, vaul, tailwindcss-animate
    **图标**: lucide-react
    **日期选择**: react-day-picker, date-fns
    **主题**: next-themes
    **样式工具**: class-variance-authority, clsx, tailwind-merge
    **Toast 通知**: sonner
    **图表**: recharts
    **模式模拟**: msw (Mock Service Worker)
    **其他**: zod (数据校验), react-resizable-panels

### 3. 帮助开发的工具

#### 数据库
这是最新的数据库结构的文件: `server\db\proxy_shop.sql`
需要修改数据库请添加脚本到这个目录`server\db\migrations`, 文件格式为类似:20240521_add_menu_items.sql

#### server项目生成的接口文档文件
这是自动生成是本系统的接口文档文件,即是 `server` 项目生成的 `server\openApi.json`
它通过后端工具重新生成, 不需要用户直接修改


#### API 路由设计.md
使用PS C:\git\github\proxy-shop\server\scripts\api-docs> node .\generateApiDocs.js
会使用`server\openApi.json` 生成 API 路由设计文档已生成: C:\git\github\proxy-shop\server\doc\API 路由设计.md

#### shop-web 的接口请求函数和类型的自动生成
PS C:\git\github\proxy-shop\shop-web\scripts> node .\generate-api.cjs
会使用`server\openApi.json` 生成 API 路由设计文档已生成:
..\src\services\generated\api.ts
- ..\src\services\generated\types.d.ts

####  接口路径的命名规则
/api/shop/*  使用在 `shop-web/`: 代理商城官网前端项目 (React)
其它/api/* 可以使用在 `admin-web/`: Web 管理后台前端项目 (Vue)，因为要兼容以前ruoyi的设计

