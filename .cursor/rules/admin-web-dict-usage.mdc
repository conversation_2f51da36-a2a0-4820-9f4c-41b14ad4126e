---
description:
globs: *.vue
alwaysApply: false
---
### admin-web 字典使用规则

在 `admin-web` 项目中，字典用于管理各种状态、类型等枚举值。以下是字典的常见使用方式：

1.  **引入和声明字典**：

    使用 `useDict` hook 来引入所需的字典。

    ```
    import { useDict } from '@/utils/dict'
    const { shop_product_type } = useDict('shop_product_type')
    ```

2.  **在表单中使用字典（例如 `el-radio-group`）**：

    ```
    <el-radio-group v-model="form.status">
             <el-radio v-for="dict in sys_general_status" :key="dict.value" :label="dict.label">{{ dict.value }}</el-radio>
           </el-radio-group>
    ```

3.  **在查询表单中使用字典（例如 `el-select`）**：

    ```
     <el-select v-model="queryParams.status" placeholder="客户状态" clearable style="width: 200px">
             <el-option v-for="dict in sys_general_status" :key="dict.value" :label="dict.label" :value="dict.value" />
           </el-select>
    ```

4.  **在表格中使用字典标签（`dict-tag` 组件）**：

    ```
          <template #default="scope">
                <dict-tag :options="sys_job_group" :value="scope.row.jobGroup" />
             </template>
    ```

请参照以上模式在项目中使用字典。
