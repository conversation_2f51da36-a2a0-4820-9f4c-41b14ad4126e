{"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.disableAutomaticTypeAcquisition": false, "typescript.workspaceSymbols.scope": "currentProject", "typescript.preferences.useAliasesForRenames": false, "typescript.suggest.classMemberSnippets.enabled": false, "eslint.workingDirectories": ["shop-web", "server", "admin-web"], "typescript.updateImportsOnFileMove.enabled": "always", "typescript.suggest.paths": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/logs": true}, "files.exclude": {"**/.git": false, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.idea": true, "**/node_modules": false, "**/dist": true, ".yoyo": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json,pnpm-lock.yaml,yarn.lock", "tsconfig.json": "tsconfig.*.json", "vite.config.*": "vitest.config.*"}}