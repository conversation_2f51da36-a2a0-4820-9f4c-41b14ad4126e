{"version": "2.0.0", "tasks": [{"label": "Start Server Dev", "type": "shell", "command": "npm run start", "options": {"cwd": "${workspaceFolder}/server"}, "group": {"kind": "build", "isDefault": false}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Admin-Web Dev", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/admin-web"}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Shop-Web Dev", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/shop-web"}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Server (Worktree)", "type": "shell", "command": "npm run dev:worktree", "options": {"cwd": "${workspaceFolder}/server"}, "presentation": {"reveal": "always", "panel": "dedicated", "group": "worktree", "clear": true}, "problemMatcher": [], "isBackground": true}, {"label": "Start Admin Web (Worktree)", "type": "shell", "command": "npm run dev:worktree", "options": {"cwd": "${workspaceFolder}/admin-web"}, "presentation": {"reveal": "always", "panel": "dedicated", "group": "worktree", "clear": true}, "problemMatcher": [], "isBackground": true}, {"label": "Start Shop Web (Worktree)", "type": "shell", "command": "npm run dev:worktree", "options": {"cwd": "${workspaceFolder}/shop-web"}, "presentation": {"reveal": "always", "panel": "dedicated", "group": "worktree", "clear": true}, "problemMatcher": [], "isBackground": true}, {"label": "🚀 Start All Services (Worktree)", "dependsOn": ["Start Server (Worktree)", "Start Admin Web (Worktree)", "Start Shop Web (Worktree)"], "dependsOrder": "parallel", "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}, {"label": "Setup Worktree Ports", "type": "shell", "command": "node scripts/setup-worktree-ports.js ${input:portOffset}", "options": {"cwd": "${workspaceFolder}"}, "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}], "inputs": [{"id": "portOffset", "type": "promptString", "description": "Enter port offset (e.g., 100, 200, etc.)", "default": "100"}]}