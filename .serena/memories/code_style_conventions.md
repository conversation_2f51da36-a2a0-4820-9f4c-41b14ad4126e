# 代码风格和约定

## 通用规范
- 遵循现有代码模式
- TypeScript 严格模式
- 不添加任何注释，除非明确要求

## 后端 (NestJS)
- DTOs 使用 class-validator
- 模块结构: Controller → Service → Repository
- 关键装饰器:
  - `@RequirePermission()` - 权限控制
  - `@RequireRole()` - 角色控制
  - `@Public()` - 跳过认证
  - `@OperLog()` - 操作日志

## 前端规范

### React (shop-web)
- 使用 Tailwind CSS 进行样式设计
- 配色方案:
  - 主色调: #865DDC (丁香紫)
  - 辅助色: #57C5DC (湖水蓝)
  - 强调色: #80DEEA (明亮青蓝)
- 状态管理: Zustand
- 数据管理: React Query
- 表单验证: React Hook Form + Zod

### Vue.js (admin-web)
- 使用 Element Plus UI 组件
- 状态管理: Pinia
- 时间处理: 使用 `import { formatTime } from '@/utils/timeFormatter'`

## 字典系统使用
- 动态字典Hook: `useDictionary()`, `useDictionaries()`
- 缓存: React Query自动管理24小时缓存
- API: `/api/shop/dict/data/type/{dictType}`

## 安全规范
- 从不引入暴露或记录秘密和密钥的代码
- 从不将秘密或密钥提交到仓库
- 遵循安全最佳实践