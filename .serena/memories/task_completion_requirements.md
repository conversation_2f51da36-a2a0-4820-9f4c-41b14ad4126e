# 任务完成要求

## 代码质量检查
- 运行 lint 和 typecheck 命令确保代码正确
- server: `npm run lint`
- shop-web: `npm run lint`
- admin-web: 目前不支持lint

## 提交要求
- 仅在必要时创建新文件，优先编辑现有文件
- 不主动创建文档文件，除非用户明确要求
- 从不提交更改，除非用户明确要求
- 做且仅做用户要求的事情

## 测试
- 从不假设特定的测试框架或测试脚本
- 检查 README 或搜索代码库以确定测试方法
- 验证解决方案是否可能通过测试

## 任务调度
- 所有异步任务都通过 server 的 monitor/job 模块执行
- 统一的任务调度机制

## 调试注意事项
- 不要关闭正在运行的调试进程
- 可以通过构建来测试代码
- 不要运行需要保持进程运行的命令(npm run dev等)

## 开发环境
- 终端环境: debian的zsh
- 数据库部署在远程服务器，使用mcp工具查看或执行任务
- 依赖服务: MySQL 8.0+, Redis, SMTP