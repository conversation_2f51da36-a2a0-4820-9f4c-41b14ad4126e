# 开发命令参考

## 服务器 (server/)
```bash
cd server
npm run start:dev    # 开发模式
npm run build        # 构建生产版本
npm run test         # 运行测试
npm run lint         # 代码检查
```

## 管理后台 (admin-web/)
```bash
cd admin-web
npm run dev          # 开发模式
npm run build        # 生产构建
npm run preview      # 预览构建
```

## 商城前端 (shop-web/)
```bash
cd shop-web
npm run dev          # 开发模式
npm run build        # 生产构建
npm run lint         # 代码检查
```

## API文档生成
```bash
# 生成 server 的 API 设计文档
cd server/scripts/api-docs
node generateApiDocs.js

# 生成 shop-web 前端 API 客户端和类型
cd shop-web/scripts
node generate-api.cjs
```

## 测试相关
```bash
# 获取测试token
node server/scripts/get-project-token.js
```

## 注意事项
- 不要运行 npm run dev 和 npm run build 类型的命令保持进程运行
- 可以通过构建来测试代码npm run lint
- admin-web项目现在不支持lint