# 项目概览 - GoMaskIP电子商务平台

## 项目描述
GoMaskIP是一个为代理服务而设的全栈电子商务平台，包含三个主要应用：

1. **server/** - NestJS 后端 API (TypeScript)
2. **admin-web/** - Vue.js 管理仪表盘 (若依管理系统)  
3. **shop-web/** - React 客户商城

## 技术栈

### 后端 (NestJS)
- NestJS (v10) + TypeORM + MySQL2
- ioredis (Redis) + Passport + JWT + bcryptjs
- 日志: <PERSON>, winston-daily-rotate-file
- API文档: Swagger (@nestjs/swagger)
- 验证: class-validator, class-transformer

### 商城前端 (React)
- React (v18) + Vite
- UI: Tailwind CSS + Radix UI + Shadcn/ui
- 状态: Zustand + React Query (@tanstack/react-query)
- 表单: React Hook Form + Zod
- HTTP: Axios

### 管理后台 (Vue.js)
- Vue 3 + Element Plus + Pinia + Vite
- HTTP: Axios

## 架构设计
- 模块结构: 每个功能都是一个模块(system、shop、monitor)
- 分层架构: Controller → Service → Repository (TypeORM)
- 认证授权: JWT + Passport.js + RBAC