{"openapi": "3.0.0", "paths": {"/api/version": {"get": {"operationId": "VersionController_getVersion", "summary": "获取系统版本信息（包含实时数据库状态）", "parameters": [], "responses": {"200": {"description": "返回系统版本信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"version": {"type": "string", "description": "版本号"}, "name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "应用描述"}, "buildTime": {"type": "string", "description": "构建时间"}, "nodeVersion": {"type": "string", "description": "Node.js 版<PERSON>"}, "environment": {"type": "string", "description": "运行环境"}, "gitCommit": {"type": "string", "description": "Git 提交哈希"}, "gitBranch": {"type": "string", "description": "Git 分支"}, "database": {"type": "object", "description": "数据库连接信息", "properties": {"mysql": {"type": "object", "properties": {"host": {"type": "string"}, "port": {"type": "number"}, "database": {"type": "string"}, "status": {"type": "string", "enum": ["connected", "disconnected", "unknown"]}}}, "redis": {"type": "object", "properties": {"host": {"type": "string"}, "port": {"type": "number"}, "status": {"type": "string", "enum": ["connected", "disconnected", "unknown"]}}}}}, "email": {"type": "object", "description": "邮件服务配置信息", "properties": {"host": {"type": "string", "description": "SMTP服务器地址"}, "port": {"type": "number", "description": "SMTP端口"}, "user": {"type": "string", "description": "邮箱用户名（已脱敏）"}, "secure": {"type": "boolean", "description": "是否启用SSL/TLS"}, "status": {"type": "string", "enum": ["configured", "missing", "unknown"], "description": "配置状态"}}}}}}}}}, "tags": ["系统信息"]}}, "/api/version/simple": {"get": {"operationId": "VersionController_getSimpleVersion", "summary": "获取简单版本信息", "parameters": [], "responses": {"200": {"description": "返回简单版本字符串", "content": {"application/json": {"schema": {"type": "object", "properties": {"version": {"type": "string", "description": "版本字符串"}}}}}}}, "tags": ["系统信息"]}}, "/api/version/health": {"get": {"operationId": "VersionController_getHealth", "summary": "健康检查", "parameters": [], "responses": {"200": {"description": "返回服务健康状态", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "服务状态"}, "timestamp": {"type": "string", "description": "检查时间"}, "version": {"type": "string", "description": "版本信息"}, "uptime": {"type": "number", "description": "运行时间（秒）"}}}}}}}, "tags": ["系统信息"]}}, "/api/user-management/login": {"post": {"operationId": "UserManagementController_login", "summary": "用户登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/logout": {"post": {"operationId": "UserManagementController_logout", "summary": "退出登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/register": {"post": {"operationId": "UserManagementController_register", "summary": "用户注册", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/registerUser": {"get": {"operationId": "UserManagementController_registerUser", "summary": "账号自助-是否开启用户注册功能", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/captchaImage": {"get": {"operationId": "UserManagementController_captchaImage", "summary": "获取验证图片", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/getInfo": {"get": {"operationId": "UserManagementController_getInfo", "summary": "用户信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/user-management/getRouters": {"get": {"operationId": "UserManagementController_getRouters", "summary": "路由信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户验证"]}}, "/api/common/upload": {"post": {"operationId": "UploadController_singleFileUpload", "summary": "文件上传", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/common/upload/chunk/uploadId": {"get": {"operationId": "UploadController_getChunkUploadId", "summary": "获取切片上传任务Id", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/common/upload/chunk": {"post": {"operationId": "UploadController_chunkFileUpload", "summary": "文件切片上传", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/common/upload/chunk/merge": {"post": {"operationId": "UploadController_chunkMergeFile", "summary": "合并切片", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChunkMergeFileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/common/upload/chunk/result": {"get": {"operationId": "UploadController_getChunkUploadResult", "summary": "获取切片上传结果", "parameters": [{"name": "uploadId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/common/upload/cos/authorization": {"get": {"operationId": "UploadController_getAuthorization", "summary": "获取cos上传密钥", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/api/system/config": {"post": {"operationId": "ConfigController_create", "summary": "参数设置-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConfigDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/参数设置"]}, "put": {"operationId": "ConfigController_update", "summary": "参数设置-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConfigDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/config/list": {"get": {"operationId": "ConfigController_findAll", "summary": "参数设置-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListConfigDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/config/{id}": {"get": {"operationId": "ConfigController_findOne", "summary": "参数设置-详情(id)", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}, "delete": {"operationId": "ConfigController_remove", "summary": "参数设置-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/config/configKey/{id}": {"get": {"operationId": "ConfigController_findOneByconfigKey", "summary": "参数设置-详情(config<PERSON><PERSON>)【走缓存】", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/config/refreshCache": {"delete": {"operationId": "ConfigController_refreshCache", "summary": "参数设置-刷新缓存", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/config/export": {"post": {"operationId": "ConfigController_export", "summary": "导出参数管理为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListConfigDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/参数设置"]}}, "/api/system/dept": {"post": {"operationId": "DeptController_create", "summary": "部门管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeptDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}, "put": {"operationId": "DeptController_update", "summary": "部门管理-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeptDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}}, "/api/system/dept/list": {"get": {"operationId": "DeptController_findAll", "summary": "部门管理-列表", "parameters": [{"name": "deptName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}}, "/api/system/dept/{id}": {"get": {"operationId": "DeptController_findOne", "summary": "部门管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}, "delete": {"operationId": "DeptController_remove", "summary": "部门管理-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}}, "/api/system/dept/list/exclude/{id}": {"get": {"operationId": "DeptController_findListExclude", "summary": "部门管理-黑名单", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/部门管理"]}}, "/api/system/dict/type": {"post": {"operationId": "DictController_createType", "summary": "字典类型-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDictTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}, "put": {"operationId": "DictController_updateType", "summary": "字典类型-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDictTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/type/refreshCache": {"delete": {"operationId": "DictController_refreshCache", "summary": "字典数据-刷新缓存", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/type/{id}": {"delete": {"operationId": "DictController_deleteType", "summary": "字典类型-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}, "get": {"operationId": "DictController_findOneType", "summary": "字典类型-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/type/list": {"get": {"operationId": "DictController_findAllType", "summary": "字典类型-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/type/optionselect": {"get": {"operationId": "DictController_findOptionselect", "summary": "全部字典类型-下拉数据", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/data": {"post": {"operationId": "DictController_createDictData", "summary": "字典数据-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDictDataDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}, "put": {"operationId": "DictController_updateDictData", "summary": "字典数据-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDictDataDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/data/{id}": {"delete": {"operationId": "DictController_deleteDictData", "summary": "字典数据-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}, "get": {"operationId": "DictController_findOneDictData", "summary": "字典数据-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/data/list": {"get": {"operationId": "DictController_findAllData", "summary": "字典数据-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/data/type/{id}": {"get": {"operationId": "DictController_findOneDataType", "summary": "字典数据-类型-详情【走缓存】", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/type/export": {"post": {"operationId": "DictController_export", "summary": "导出字典组为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDictType"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/system/dict/data/export": {"post": {"operationId": "DictController_exportData", "summary": "导出字典内容为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDictType"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/字典管理"]}}, "/api/location/region/search": {"get": {"operationId": "LocationController_searchRegions", "summary": "搜索区域", "parameters": [{"name": "keyword", "required": false, "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"minimum": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "type": "number"}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["地理位置管理"]}}, "/api/location/country/search": {"get": {"operationId": "LocationController_searchCountries", "summary": "搜索国家", "parameters": [{"name": "keyword", "required": false, "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "regionId", "required": false, "in": "query", "description": "区域ID", "schema": {"type": "number"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"minimum": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "type": "number"}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["地理位置管理"]}}, "/api/location/city/search": {"get": {"operationId": "LocationController_searchCities", "summary": "搜索城市", "parameters": [{"name": "keyword", "required": false, "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "countryId", "required": false, "in": "query", "description": "国家ID", "schema": {"type": "number"}}, {"name": "regionId", "required": false, "in": "query", "description": "区域ID", "schema": {"type": "number"}}, {"name": "hotOnly", "required": false, "in": "query", "description": "是否只返回热门城市", "schema": {"type": "boolean"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"minimum": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "type": "number"}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["地理位置管理"]}}, "/api/location/unified-search": {"get": {"operationId": "LocationController_unifiedLocationSearch", "summary": "统一位置搜索", "parameters": [{"name": "keyword", "required": false, "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "regionId", "required": false, "in": "query", "description": "区域ID", "schema": {"type": "number"}}, {"name": "countryId", "required": false, "in": "query", "description": "国家ID", "schema": {"type": "number"}}, {"name": "searchType", "required": false, "in": "query", "description": "搜索类型", "schema": {"default": "all", "enum": ["region", "country", "city", "all"], "type": "string"}}, {"name": "hotOnly", "required": false, "in": "query", "description": "是否只返回热门", "schema": {"type": "boolean"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"minimum": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "type": "number"}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["地理位置管理"]}}, "/api/location/region": {"post": {"operationId": "LocationController_createRegion", "summary": "创建区域", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLocationRegionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/region/list": {"get": {"operationId": "LocationController_findAllRegions", "summary": "获取区域列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "regionCode", "required": false, "in": "query", "description": "区域代码", "schema": {"type": "string"}}, {"name": "regionName", "required": false, "in": "query", "description": "区域名称", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/region/all": {"get": {"operationId": "LocationController_findAllEnabledRegions", "summary": "获取所有启用的区域", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/region/{id}": {"get": {"operationId": "LocationController_findOneRegion", "summary": "获取区域详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "patch": {"operationId": "LocationController_updateRegion", "summary": "更新区域", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationRegionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "delete": {"operationId": "LocationController_removeRegion", "summary": "删除区域", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/country": {"post": {"operationId": "LocationController_createCountry", "summary": "创建国家", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLocationCountryDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/country/list": {"get": {"operationId": "LocationController_findAllCountries", "summary": "获取国家列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "regionId", "required": false, "in": "query", "description": "区域ID", "schema": {"type": "number"}}, {"name": "regionName", "required": false, "in": "query", "description": "区域名称", "schema": {"type": "string"}}, {"name": "countryCode", "required": false, "in": "query", "description": "国家代码", "schema": {"type": "string"}}, {"name": "countryName", "required": false, "in": "query", "description": "国家名称", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/country/all": {"get": {"operationId": "LocationController_findAllEnabledCountries", "summary": "获取所有启用的国家", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/country/region/{regionId}": {"get": {"operationId": "LocationController_findCountriesByRegion", "summary": "根据区域获取国家列表", "parameters": [{"name": "regionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/country/{id}": {"get": {"operationId": "LocationController_findOneCountry", "summary": "获取国家详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "patch": {"operationId": "LocationController_updateCountry", "summary": "更新国家", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationCountryDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "delete": {"operationId": "LocationController_removeCountry", "summary": "删除国家", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/city": {"post": {"operationId": "LocationController_createCity", "summary": "创建城市", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLocationCityDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/city/list": {"get": {"operationId": "LocationController_findAllCities", "summary": "获取城市列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "countryId", "required": false, "in": "query", "description": "国家ID", "schema": {"type": "number"}}, {"name": "countryName", "required": false, "in": "query", "description": "国家名称", "schema": {"type": "string"}}, {"name": "cityName", "required": false, "in": "query", "description": "城市名称", "schema": {"type": "string"}}, {"name": "isHot", "required": false, "in": "query", "description": "是否热门城市", "schema": {"type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "状态", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/city/country/{countryId}": {"get": {"operationId": "LocationController_findCitiesByCountry", "summary": "根据国家获取城市列表", "parameters": [{"name": "countryId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/city/hot": {"get": {"operationId": "LocationController_findHotCities", "summary": "获取热门城市", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/city/{id}": {"get": {"operationId": "LocationController_findOneCity", "summary": "获取城市详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "patch": {"operationId": "LocationController_updateCity", "summary": "更新城市", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationCityDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}, "delete": {"operationId": "LocationController_removeCity", "summary": "删除城市", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/parse": {"post": {"operationId": "LocationController_parseLocationFile", "summary": "解析位置数据文件", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/import": {"post": {"operationId": "LocationController_importLocationData", "summary": "导入地理位置数据", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/statistics": {"get": {"operationId": "LocationController_getStatistics", "summary": "获取位置数据统计", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/preview-clear": {"post": {"operationId": "LocationController_previewClearData", "summary": "预览清除数据", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/clear": {"post": {"operationId": "LocationController_clearLocationData", "summary": "清除地理位置数据", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/location/search/clear-cache": {"post": {"operationId": "LocationController_clearSearchCache", "summary": "清除搜索缓存", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["地理位置管理"]}}, "/api/system/location/mapping": {"post": {"operationId": "SupplierLocationMappingController_create", "summary": "创建供应商城市映射", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSupplierLocationMappingDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/batch": {"post": {"operationId": "SupplierLocationMappingController_batchCreate", "summary": "批量创建供应商城市映射", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchCreateSupplierLocationMappingDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/list": {"get": {"operationId": "SupplierLocationMappingController_findAll", "summary": "查询供应商城市映射列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "standardCityId", "required": false, "in": "query", "description": "标准城市ID", "schema": {"type": "number"}}, {"name": "providerId", "required": false, "in": "query", "description": "供应商ID", "schema": {"type": "number"}}, {"name": "supplierNativeCityId", "required": false, "in": "query", "description": "供应商城市ID", "schema": {"type": "string"}}, {"name": "supplierNativeCityName", "required": false, "in": "query", "description": "供应商城市名称", "schema": {"type": "string"}}, {"name": "mappingStatus", "required": false, "in": "query", "description": "映射状态", "schema": {"type": "string"}}, {"name": "minConfidenceLevel", "required": false, "in": "query", "description": "最小置信度", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/{id}": {"get": {"operationId": "SupplierLocationMappingController_findOne", "summary": "查询供应商城市映射详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}, "put": {"operationId": "SupplierLocationMappingController_update", "summary": "更新供应商城市映射", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSupplierLocationMappingDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/{ids}": {"delete": {"operationId": "SupplierLocationMappingController_remove", "summary": "删除供应商城市映射", "parameters": [{"name": "ids", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/{id}/status/{status}": {"put": {"operationId": "SupplierLocationMappingController_updateStatus", "summary": "更新映射状态", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/provider/{providerId}/native/{nativeCityId}": {"get": {"operationId": "SupplierLocationMappingController_findByProviderAndNativeCity", "summary": "根据供应商ID和原始城市ID查询映射", "parameters": [{"name": "providerId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "nativeCityId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/location/mapping/provider/{providerId}/standard/{standardCityId}": {"get": {"operationId": "SupplierLocationMappingController_findByProviderAndStandardCity", "summary": "根据供应商ID和标准城市ID查询映射", "parameters": [{"name": "providerId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "standardCityId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["供应商城市映射管理"], "security": [{"bearer": []}]}}, "/api/system/menu": {"post": {"operationId": "MenuController_create", "summary": "菜单管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/菜单管理"]}, "put": {"operationId": "MenuController_update", "summary": "菜单管理-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}}, "/api/system/menu/list": {"get": {"operationId": "MenuController_findAll", "summary": "菜单管理-列表", "parameters": [{"name": "menuName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}}, "/api/system/menu/treeselect": {"get": {"operationId": "MenuController_treeSelect", "summary": "菜单管理-树表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}}, "/api/system/menu/roleMenuTreeselect/{menuId}": {"get": {"operationId": "MenuController_roleMenuTreeselect", "summary": "菜单管理-角色-树表", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}}, "/api/system/menu/{menuId}": {"get": {"operationId": "MenuController_findOne", "summary": "菜单管理-详情", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}, "delete": {"operationId": "MenuController_remove", "summary": "菜单管理-删除", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/菜单管理"]}}, "/api/system/notice": {"post": {"operationId": "NoticeController_create", "summary": "通知公告-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNoticeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/通知公告"]}, "put": {"operationId": "NoticeController_update", "summary": "通知公告-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNoticeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/通知公告"]}}, "/api/system/notice/list": {"get": {"operationId": "NoticeController_findAll", "summary": "通知公告-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListNoticeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/通知公告"]}}, "/api/system/notice/{id}": {"get": {"operationId": "NoticeController_findOne", "summary": "通知公告-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/通知公告"]}, "delete": {"operationId": "NoticeController_remove", "summary": "通知公告-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/通知公告"]}}, "/api/system/post": {"post": {"operationId": "PostController_create", "summary": "岗位管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePostDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/岗位管理"]}, "put": {"operationId": "PostController_update", "summary": "岗位管理-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePostDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/岗位管理"]}}, "/api/system/post/list": {"get": {"operationId": "PostController_findAll", "summary": "岗位管理-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListPostDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/岗位管理"]}}, "/api/system/post/{id}": {"get": {"operationId": "PostController_findOne", "summary": "岗位管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/岗位管理"]}}, "/api/system/post/{ids}": {"delete": {"operationId": "PostController_remove", "summary": "岗位管理-删除", "parameters": [{"name": "ids", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/岗位管理"]}}, "/api/system/post/export": {"post": {"operationId": "PostController_export", "summary": "导出岗位管理xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListPostDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/岗位管理"]}}, "/api/system/role": {"post": {"operationId": "RoleController_create", "summary": "角色管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/角色管理"]}, "put": {"operationId": "RoleController_update", "summary": "角色管理-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/list": {"get": {"operationId": "RoleController_findAll", "summary": "角色管理-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/deptTree/{id}": {"get": {"operationId": "RoleController_deptTree", "summary": "角色管理-部门树", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/{id}": {"get": {"operationId": "RoleController_findOne", "summary": "角色管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}, "delete": {"operationId": "RoleController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/dataScope": {"put": {"operationId": "RoleController_dataScope", "summary": "角色管理-数据权限修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/changeStatus": {"put": {"operationId": "RoleController_changeStatus", "summary": "角色管理-停用角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/authUser/allocatedList": {"get": {"operationId": "RoleController_authUserAllocatedList", "summary": "角色管理-角色已分配用户列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "roleId", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocatedListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/authUser/unallocatedList": {"get": {"operationId": "RoleController_authUserUnAllocatedList", "summary": "角色管理-角色未分配用户列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "roleId", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocatedListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/authUser/cancel": {"put": {"operationId": "RoleController_authUserCancel", "summary": "角色管理-解绑角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserCancelDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/authUser/cancelAll": {"put": {"operationId": "RoleController_authUserCancelAll", "summary": "角色管理-批量解绑角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserCancelAllDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/authUser/selectAll": {"put": {"operationId": "RoleController_authUserSelectAll", "summary": "角色管理-批量绑定角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserSelectAllDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/system/role/export": {"post": {"operationId": "RoleController_export", "summary": "导出角色管理xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListRoleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/角色管理"]}}, "/api/tool/gen/list": {"get": {"operationId": "ToolController_findAll", "summary": "数据表列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/db/list": {"get": {"operationId": "ToolController_genDbList", "summary": "查询数据库列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/importTable": {"post": {"operationId": "ToolController_genImportTable", "summary": "导入表", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TableName"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/synchDb/{tableName}": {"get": {"operationId": "ToolController_synchDb", "summary": "同步表", "parameters": [{"name": "tableName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/{id}": {"get": {"operationId": "ToolController_gen", "summary": "查询表详细信息", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}, "delete": {"operationId": "ToolController_remove", "summary": "删除表数据", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen": {"put": {"operationId": "ToolController_genUpdate", "summary": "修改代码生成信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenTableUpdate"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/batchGenCode/zip": {"get": {"operationId": "ToolController_batchGenCode", "summary": "生成代码", "parameters": [{"name": "tableNames", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/tool/gen/preview/{id}": {"get": {"operationId": "ToolController_preview", "summary": "查看代码", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/系统工具"]}}, "/api/system/user/profile": {"get": {"operationId": "UserController_profile", "summary": "个人中心-用户信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}, "put": {"operationId": "UserController_updateProfile", "summary": "个人中心-修改用户信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/profile/avatar": {"post": {"operationId": "UserController_avatar", "summary": "个人中心-上传用户头像", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/profile/updatePwd": {"put": {"operationId": "UserController_updatePwd", "summary": "个人中心-修改密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePwdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user": {"post": {"operationId": "UserController_create", "summary": "用户-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}, "get": {"operationId": "UserController_findPostAndRoleAll", "summary": "用户-角色+岗位", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}, "put": {"operationId": "UserController_update", "summary": "用户-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/list": {"get": {"operationId": "UserController_findAll", "summary": "用户-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "deptId", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "nick<PERSON><PERSON>", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "email", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/deptTree": {"get": {"operationId": "UserController_deptTree", "summary": "用户-部门树", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/authRole/{id}": {"get": {"operationId": "UserController_authRole", "summary": "用户-分配角色-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/authRole": {"put": {"operationId": "UserController_updateAuthRole", "summary": "用户-角色信息-更新", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/{userId}": {"get": {"operationId": "UserController_findOne", "summary": "用户-详情", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/changeStatus": {"put": {"operationId": "UserController_changeStatus", "summary": "用户-停用角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/resetPwd": {"put": {"operationId": "UserController_resetPwd", "summary": "用户-重置密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPwdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/{id}": {"delete": {"operationId": "UserController_remove", "summary": "用户-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/user/export": {"post": {"operationId": "UserController_export", "summary": "导出用户信息数据为xlsx", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListUserDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["admin端/用户管理"], "security": [{"bearer": []}]}}, "/api/system/products": {"post": {"operationId": "ProductsController_create", "summary": "创建商品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"201": {"description": "创建成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "409": {"description": "商品已存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}, "get": {"operationId": "ProductsController_findAll", "summary": "获取商品列表", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "keyword", "required": false, "in": "query", "description": "关键字搜索", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "商品状态 0表示正常, 1表示下架", "schema": {"enum": ["0", "1"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"default": "sortOrder", "enum": ["price", "flowAmount", "salesCount", "createTime", "sortOrder"], "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序方式", "schema": {"default": "ASC", "enum": ["ASC", "DESC"], "type": "string"}}], "responses": {"200": {"description": "获取成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/management": {"get": {"operationId": "ProductsController_findAllForManagement", "summary": "获取产品管理列表（包含扩展字段）", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "productCode", "required": false, "in": "query", "description": "产品编号", "schema": {"type": "string"}}, {"name": "productName", "required": false, "in": "query", "description": "产品名称", "schema": {"type": "string"}}, {"name": "region", "required": false, "in": "query", "description": "地区名称", "schema": {"type": "string"}}, {"name": "country", "required": false, "in": "query", "description": "国家名称", "schema": {"type": "string"}}, {"name": "city", "required": false, "in": "query", "description": "城市名称", "schema": {"type": "string"}}, {"name": "regionId", "required": false, "in": "query", "description": "地区ID", "schema": {"type": "number"}}, {"name": "countryId", "required": false, "in": "query", "description": "国家ID", "schema": {"type": "number"}}, {"name": "cityId", "required": false, "in": "query", "description": "城市ID", "schema": {"type": "number"}}, {"name": "productType", "required": false, "in": "query", "description": "产品类型", "schema": {"type": "string"}}, {"name": "providerId", "required": false, "in": "query", "description": "供应商ID", "schema": {"type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "状态：0上架 1下架", "schema": {"type": "string"}}, {"name": "inventoryStatus", "required": false, "in": "query", "description": "库存状态", "schema": {"type": "string"}}, {"name": "syncStrategy", "required": false, "in": "query", "description": "同步策略", "schema": {"enum": ["FULL_SYNC", "STOCK_AND_COST_ONLY", "MANUAL_OVERRIDE", "DISABLED"], "type": "string"}}], "responses": {"200": {"description": "获取成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/{id}": {"get": {"operationId": "ProductsController_findOne", "summary": "获取商品详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "商品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "商品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}, "put": {"operationId": "ProductsController_update", "summary": "更新商品", "parameters": [{"name": "id", "required": true, "in": "path", "description": "商品ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "商品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}, "delete": {"operationId": "ProductsController_remove", "summary": "删除商品", "parameters": [{"name": "id", "required": true, "in": "path", "description": "商品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "商品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/{id}/status": {"put": {"operationId": "ProductsController_updateStatus", "summary": "修改商品状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "商品ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductStatusDto"}}}}, "responses": {"200": {"description": "修改成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "商品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/sync": {"post": {"operationId": "ProductsController_syncProducts", "summary": "同步产品价格和库存", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncProductsDto"}}}}, "responses": {"200": {"description": "同步成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/sync/status": {"get": {"operationId": "ProductsController_getSyncStatus", "summary": "获取同步状态", "parameters": [], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/sync-clear": {"post": {"operationId": "ProductsController_clearSyncStatus", "summary": "清除同步状态（处理僵尸任务）", "parameters": [], "responses": {"200": {"description": "清除成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/sync-stop": {"post": {"operationId": "ProductsController_stopSyncTask", "summary": "停止同步任务", "parameters": [], "responses": {"200": {"description": "停止成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/manual-price-update": {"post": {"operationId": "ProductsController_manualUpdatePrice", "summary": "手动更新产品价格", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManualUpdatePriceDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "产品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/{id}/price-history": {"get": {"operationId": "ProductsController_getPriceHistory", "summary": "获取产品价格历史", "parameters": [{"name": "id", "required": true, "in": "path", "description": "产品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "产品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/batch-status": {"post": {"operationId": "ProductsController_batchUpdateStatus", "summary": "批量上下架产品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchStatusDto"}}}}, "responses": {"200": {"description": "操作成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/batch-sync": {"post": {"operationId": "ProductsController_batchSync", "summary": "批量同步产品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSyncDto"}}}}, "responses": {"200": {"description": "同步任务已提交"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/batch-update-prices": {"post": {"operationId": "ProductsController_batchUpdatePrices", "summary": "批量调价", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdatePricesDto"}}}}, "responses": {"200": {"description": "批量调价成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdatePricesResultDto"}}}}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/batch-sync-strategy": {"post": {"operationId": "ProductsController_batchUpdateSyncStrategy", "summary": "批量更新产品同步策略", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdateSyncStrategyDto"}}}}, "responses": {"200": {"description": "批量更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/batch-update-prices/preview": {"post": {"operationId": "ProductsController_batchUpdatePricesPreview", "summary": "批量调价预览", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdatePricesPreviewDto"}}}}, "responses": {"200": {"description": "预览成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdatePricesPreviewResultDto"}}}}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/ip-segment/allocate": {"post": {"operationId": "ProductsController_allocateIpSegment", "summary": "分配IP段给产品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocateIpSegmentDto"}}}}, "responses": {"201": {"description": "分配成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "产品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/ip-segment/{productId}": {"get": {"operationId": "ProductsController_getProductIpSegments", "summary": "获取产品的IP段信息", "parameters": [{"name": "productId", "required": true, "in": "path", "description": "产品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "产品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/inventory/update": {"post": {"operationId": "ProductsController_updateInventoryStatus", "summary": "批量更新库存状态", "parameters": [], "responses": {"200": {"description": "更新成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/tasks/recalculate-prices": {"post": {"operationId": "ProductsController_recalculateAllPrices", "summary": "批量重新计算产品价格", "parameters": [], "responses": {"200": {"description": "价格重算任务已提交"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/inventory/sync": {"post": {"operationId": "ProductsController_syncInventory", "summary": "手动同步库存数据", "parameters": [], "responses": {"200": {"description": "库存同步任务已启动"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/{id}/actions/revert-to-auto-pricing": {"post": {"operationId": "ProductsController_revertToAutoPricing", "summary": "恢复产品为自动定价模式", "parameters": [{"name": "id", "required": true, "in": "path", "description": "产品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "恢复成功"}, "400": {"description": "操作无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "产品不存在"}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/system/products/test/product-code": {"get": {"operationId": "ProductsController_testProductCode", "summary": "测试产品编号生成（开发测试用）", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["admin端/商品管理"], "security": [{"bearer": []}]}}, "/api/products/ip-segments/allocate": {"post": {"operationId": "IpSegmentManagementController_allocateIpSegments", "summary": "分配IP段给产品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocateIpSegmentsDto"}}}}, "responses": {"200": {"description": "分配成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpAllocationResultDto"}}}}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/deallocate": {"post": {"operationId": "IpSegmentManagementController_deallocateIpSegments", "summary": "回收IP段", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeallocateIpSegmentsDto"}}}}, "responses": {"200": {"description": "回收成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpDeallocationResultDto"}}}}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/list": {"get": {"operationId": "IpSegmentManagementController_getIpSegmentsList", "summary": "获取IP段列表", "parameters": [{"name": "productId", "required": false, "in": "query", "description": "产品ID", "schema": {"type": "number"}}, {"name": "supplierId", "required": false, "in": "query", "description": "供应商ID", "schema": {"type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "状态", "schema": {"enum": ["active", "allocated", "reserved", "full", "maintenance", "inactive"], "type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/stats": {"get": {"operationId": "IpSegmentManagementController_getIpSegmentStats", "summary": "获取IP段统计信息", "parameters": [{"name": "productId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "统计信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpSegmentStatsDto"}}}}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/product/{productId}": {"get": {"operationId": "IpSegmentManagementController_getProductIpSegments", "summary": "获取指定产品的IP段列表", "parameters": [{"name": "productId", "required": true, "in": "path", "description": "产品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/{segmentId}/status": {"put": {"operationId": "IpSegmentManagementController_updateIpSegmentStatus", "summary": "更新IP段状态", "parameters": [{"name": "segmentId", "required": true, "in": "path", "description": "IP段ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIpSegmentStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/rebalance": {"post": {"operationId": "IpSegmentManagementController_rebalanceIpSegments", "summary": "重新平衡IP段分配", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RebalanceIpSegmentsDto"}}}}, "responses": {"200": {"description": "重新平衡成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RebalanceResultDto"}}}}}, "tags": ["IP段管理"]}}, "/api/products/ip-segments/dashboard": {"get": {"operationId": "IpSegmentManagementController_getIpManagementDashboard", "summary": "获取IP管理仪表板数据", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["IP段管理"]}}, "/api/shop/OpenApi/GetNodeInventory": {"get": {"operationId": "InventoryController_getNodeInventory", "summary": "获取静态住宅代理的可用 IP 列表 (节点库存)", "parameters": [{"name": "city_name", "required": false, "in": "query", "description": "城市名称", "schema": {"type": "string"}}, {"name": "continents_id", "required": false, "in": "query", "description": "大洲ID", "schema": {"type": "number"}}, {"name": "country_code", "required": false, "in": "query", "description": "国家代码", "schema": {"type": "string"}}, {"name": "proxies_type", "required": true, "in": "query", "description": "代理类型 (例如: Premium (ISP) proxies, Shared (ISP) proxies)", "schema": {"example": "Premium (ISP) proxies", "type": "string"}}, {"name": "proxies_format", "required": true, "in": "query", "description": "代理格式 (例如 1 表示 HTTP, 2 表示 SOCKS5)", "schema": {"example": 1, "type": "number"}}, {"name": "purpose_web", "required": true, "in": "query", "description": "IP 的用途/目标", "schema": {"example": "general", "type": "string"}}], "responses": {"200": {"description": "成功获取库存列表。", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenApiGetNodeInventoryDataDto"}}}}, "400": {"description": "请求参数错误。"}, "500": {"description": "服务器内部错误。"}}, "tags": ["OpenApi - Inventory"]}}, "/api/shop/OpenApi/GetCurrentVersionPrice": {"post": {"operationId": "InventoryController_calculatePrice", "summary": "计算代理价格", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculatePriceQueryDto"}}}}, "responses": {"200": {"description": "成功获取价格信息。", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculatePriceResponseDataDto"}}}}, "400": {"description": "请求参数错误。"}, "500": {"description": "服务器内部错误。"}}, "tags": ["OpenApi - Inventory"]}}, "/api/shop/OpenApi/exchange-rate": {"get": {"operationId": "InventoryController_getExchangeRate", "summary": "获取USD到CNY汇率", "parameters": [], "responses": {"200": {"description": "成功获取汇率"}}, "tags": ["OpenApi - Inventory"]}}, "/api/system/sync-history": {"get": {"operationId": "SyncHistoryController_getSyncHistoryList", "summary": "获取同步历史列表", "parameters": [{"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {}}, {"name": "status", "required": false, "in": "query", "description": "状态", "schema": {}}, {"name": "providerId", "required": false, "in": "query", "description": "供应商ID", "schema": {}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {}}], "responses": {"200": {"description": ""}}, "tags": ["同步历史管理"]}}, "/api/system/sync-history/{syncId}": {"get": {"operationId": "SyncHistoryController_getSyncHistoryDetail", "summary": "获取同步历史详情", "parameters": [{"name": "syncId", "required": true, "in": "path", "description": "同步ID", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["同步历史管理"]}}, "/api/system/sync-history/{syncId}/details": {"get": {"operationId": "SyncHistoryController_getSyncDetails", "summary": "获取同步产品详情", "parameters": [{"name": "syncId", "required": true, "in": "path", "description": "同步ID", "schema": {"type": "number"}}, {"name": "action", "required": false, "in": "query", "description": "操作类型", "schema": {}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {}}], "responses": {"200": {"description": ""}}, "tags": ["同步历史管理"]}}, "/api/system/sync-history/recent/products": {"get": {"operationId": "SyncHistoryController_getRecentSyncedProducts", "summary": "获取最近同步的产品", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "数量限制", "schema": {}}, {"name": "syncId", "required": false, "in": "query", "description": "同步ID，不传则获取所有", "schema": {}}], "responses": {"200": {"description": ""}}, "tags": ["同步历史管理"]}}, "/api/shop/products": {"get": {"operationId": "ProductsController_getProducts", "summary": "获取标准化产品列表 (支持搜索)", "description": "根据查询参数获取产品列表。如果提供了 `keyword`，则执行搜索。不包含敏感的成本价格信息。", "parameters": [{"name": "productType", "required": false, "in": "query", "description": "产品类型", "schema": {"type": "string"}}, {"name": "proxyType", "required": false, "in": "query", "description": "代理类型 (用于查询 config_proxy_type 虚拟列)", "schema": {"example": "Premium (ISP) proxies", "type": "string"}}, {"name": "cityId", "required": false, "in": "query", "description": "标准化城市ID (前端兼容字段，实际查询可能使用 standardLocationCityId)", "schema": {"type": "number"}}, {"name": "region", "required": false, "in": "query", "description": "地区 (用于查询 standardRegionName 或 region)", "schema": {"type": "string"}}, {"name": "country", "required": false, "in": "query", "description": "国家 (用于查询 standardCountryName 或 country)", "schema": {"type": "string"}}, {"name": "city", "required": false, "in": "query", "description": "城市 (用于查询 standardCityName 或 city)", "schema": {"type": "string"}}, {"name": "priceMin", "required": false, "in": "query", "description": "最低价格", "schema": {"type": "number"}}, {"name": "priceMax", "required": false, "in": "query", "description": "最高价格", "schema": {"type": "number"}}, {"name": "currency", "required": false, "in": "query", "description": "货币", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态 (例如: \"0\" 表示正常)", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"enum": ["price", "name", "location", "createTime"], "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序顺序", "schema": {"default": "desc", "enum": ["asc", "desc"], "type": "string"}}, {"name": "keyword", "required": false, "in": "query", "description": "搜索关键词 (用于 searchProducts)", "schema": {"type": "string"}}], "responses": {"default": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PublicShopProductDto"}}}}}}, "tags": ["Shop / Standardized Products"]}}, "/api/shop/products/stats": {"get": {"operationId": "ProductsController_getProductStats", "summary": "获取产品统计信息", "parameters": [], "responses": {"default": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductStatsDto"}}}}}, "tags": ["Shop / Standardized Products"]}}, "/api/shop/products/sync-status": {"get": {"operationId": "ProductsController_getSyncStatus", "summary": "获取地理位置同步状态", "description": "获取需要同步地理位置的商品数量", "parameters": [], "responses": {"default": {"description": "获取成功"}}, "tags": ["Shop / Standardized Products"]}}, "/api/shop/products/resolve": {"get": {"operationId": "ProductsController_resolveProduct", "summary": "根据供应商信息解析标准化产品", "parameters": [{"name": "providerCode", "required": true, "in": "query", "description": "供应商代码", "schema": {"type": "string"}}, {"name": "supplierCityId", "required": true, "in": "query", "description": "供应商处城市ID", "schema": {"type": "string"}}, {"name": "proxiesType", "required": true, "in": "query", "description": "代理类型 (例如来自 IPNux 的 proxies_type)", "schema": {"type": "string"}}, {"name": "proxiesFormat", "required": false, "in": "query", "description": "代理格式 (可选)", "schema": {"type": "number"}}], "responses": {"default": {"description": "解析成功，返回产品解析结果。", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResolveResultDto"}}}}}, "tags": ["Shop / Standardized Products"]}}, "/api/shop/products/{id}": {"get": {"operationId": "ProductsController_getProductById", "summary": "获取单个标准化产品详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "产品数据库主键ID", "schema": {"type": "number"}}], "responses": {"404": {"description": "产品未找到 (如果控制器选择抛出异常而不是返回null)"}, "default": {"description": "获取成功。如果产品不存在或不可用，响应体为null。不包含敏感的成本价格信息。", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicShopProductDto"}}}}}, "tags": ["Shop / Standardized Products"]}}, "/api/shop/products/sync-locations": {"post": {"operationId": "ProductsController_syncProductLocations", "summary": "同步商品地理位置信息", "description": "将商品配置中的地理位置信息同步到标准化字段中", "parameters": [], "responses": {"default": {"description": "同步成功"}}, "tags": ["Shop / Standardized Products"]}}, "/api/system/orders": {"get": {"operationId": "OrdersController_findAll", "summary": "获取订单列表", "parameters": [], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/订单管理"], "security": [{"bearer": []}]}}, "/api/system/orders/{id}": {"get": {"operationId": "OrdersController_findOne", "summary": "获取订单详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "订单不存在"}}, "tags": ["admin端/订单管理"], "security": [{"bearer": []}]}}, "/api/system/orders/{id}/status": {"put": {"operationId": "OrdersController_updateStatus", "summary": "更新订单状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效或状态转换不合法"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "订单不存在"}}, "tags": ["admin端/订单管理"], "security": [{"bearer": []}]}}, "/api/system/orders/{id}/refund": {"put": {"operationId": "OrdersController_refundOrder", "summary": "订单退款", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundOrderDto"}}}}, "responses": {"200": {"description": "退款成功"}, "400": {"description": "请求参数无效或订单状态不允许退款"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "订单不存在"}}, "tags": ["admin端/订单管理"], "security": [{"bearer": []}]}}, "/api/system/orders/{id}/cancel": {"put": {"operationId": "OrdersController_cancelOrder", "summary": "取消订单", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "取消成功"}, "400": {"description": "请求参数无效或订单状态不允许取消"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "订单不存在"}}, "tags": ["admin端/订单管理"], "security": [{"bearer": []}]}}, "/api/system/customers": {"get": {"operationId": "CustomersController_findAll", "summary": "获取客户列表", "parameters": [], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}, "post": {"operationId": "CustomersController_create", "summary": "新增客户", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerDto"}}}}, "responses": {"201": {"description": "创建成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "409": {"description": "邮箱或手机号已被注册"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/system/customers/{id}": {"get": {"operationId": "CustomersController_findOne", "summary": "获取客户详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}, "put": {"operationId": "CustomersController_update", "summary": "更新客户信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}, "409": {"description": "邮箱或手机号已被注册"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/system/customers/{id}/status": {"put": {"operationId": "CustomersController_updateStatus", "summary": "更新客户状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerStatusDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/system/customers/{id}/orders": {"get": {"operationId": "CustomersController_getCustomerOrders", "summary": "获取客户订单", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/system/customers/{id}/timeline": {"get": {"operationId": "CustomersController_getCustomerTimeline", "summary": "获取客户操作时间线", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}, {"name": "eventType", "required": false, "in": "query", "description": "事件类型", "schema": {"enum": ["ADMIN_ACTION", "BALANCE_CHANGE", "ORDER_EVENT", "LOGIN_EVENT"], "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "pageNum", "required": true, "in": "query", "description": "页码", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerTimelineResponseDto"}}}}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/system/customers/{id}/timeline/stats": {"get": {"operationId": "CustomersController_getCustomerTimelineStats", "summary": "获取客户时间线统计信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["admin端/客户管理"], "security": [{"bearer": []}]}}, "/api/shop/wallet": {"get": {"operationId": "WalletController_getUserWallet", "summary": "获取用户余额信息", "parameters": [], "responses": {"200": {"description": "成功获取用户余额信息"}}, "tags": ["web端/余额管理"], "security": [{"bearer": []}]}}, "/api/shop/wallet/transactions": {"get": {"operationId": "WalletController_getTransactions", "summary": "获取用户交易记录", "parameters": [], "responses": {"200": {"description": "成功获取用户交易记录"}}, "tags": ["web端/余额管理"], "security": [{"bearer": []}]}}, "/api/shop/wallet/recharge": {"post": {"operationId": "WalletController_recharge", "summary": "用户充值", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeDto"}}}}, "responses": {"201": {"description": "成功充值"}}, "tags": ["web端/余额管理"], "security": [{"bearer": []}]}}, "/api/shop/wallet/pay": {"post": {"operationId": "WalletController_payWithBalance", "summary": "使用余额支付订单", "parameters": [], "responses": {"200": {"description": "支付成功"}, "400": {"description": "余额不足或余额状态异常"}}, "tags": ["web端/余额管理"], "security": [{"bearer": []}]}}, "/api/shop/wallet/balance-check/{amount}": {"get": {"operationId": "WalletController_checkBalance", "summary": "检查余额是否足够", "parameters": [{"name": "amount", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "余额检查结果"}}, "tags": ["web端/余额管理"], "security": [{"bearer": []}]}}, "/api/shop/orders": {"post": {"operationId": "OrdersController_create", "summary": "创建订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}}}, "responses": {"201": {"description": "创建成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}, "get": {"operationId": "OrdersController_findAll", "summary": "获取订单列表", "parameters": [{"name": "orderStatus", "required": false, "in": "query", "description": "订单状态（0待支付 1已支付 2已取消 3已退款）", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderPublicDto"}}}}}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/static-ip": {"post": {"operationId": "OrdersController_createStaticIpOrder", "summary": "创建静态IP订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStaticIpOrderDto"}}}}, "responses": {"201": {"description": "创建成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/renew": {"post": {"operationId": "OrdersController_createRenewOrder", "summary": "创建续费订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenewStaticIpOrderDto"}}}}, "responses": {"201": {"description": "续费订单创建成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/recharge": {"post": {"operationId": "OrdersController_createRechargeOrder", "summary": "创建充值订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRechargeOrderDto"}}}}, "responses": {"201": {"description": "充值订单创建成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/renewable-instances": {"get": {"operationId": "OrdersController_getRenewableInstances", "summary": "获取可续费的IP实例列表", "parameters": [], "responses": {"200": {"description": "获取成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}": {"get": {"operationId": "OrdersController_findOne", "summary": "获取订单详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPublicDto"}}}}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/cancel": {"put": {"operationId": "OrdersController_cancel", "summary": "取消订单", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "取消成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/pay": {"post": {"operationId": "OrdersController_pay", "summary": "支付订单", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "支付成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/payment-status": {"get": {"operationId": "OrdersController_getPaymentStatus", "summary": "查询订单支付状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/activation-status": {"get": {"operationId": "OrdersController_getActivationStatus", "summary": "查询订单开通状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/retry-activation": {"post": {"operationId": "OrdersController_retryActivation", "summary": "重试订单开通", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "重试成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/orders/{id}/payment-url": {"get": {"operationId": "OrdersController_getPaymentUrl", "summary": "获取订单支付链接", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}}, "tags": ["web端/订单管理"], "security": [{"bearer": []}]}}, "/api/shop/notifications": {"get": {"operationId": "NotificationsController_getNotifications", "summary": "获取用户通知列表", "parameters": [{"name": "isRead", "required": true, "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "成功获取用户通知列表"}}, "tags": ["web端/用户通知"], "security": [{"bearer": []}]}}, "/api/shop/notifications/{id}": {"get": {"operationId": "NotificationsController_getNotification", "summary": "获取用户通知详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取用户通知详情"}}, "tags": ["web端/用户通知"], "security": [{"bearer": []}]}}, "/api/shop/notifications/{id}/read": {"patch": {"operationId": "NotificationsController_markAsRead", "summary": "标记通知为已读", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功标记通知为已读"}}, "tags": ["web端/用户通知"], "security": [{"bearer": []}]}}, "/api/shop/notifications/read-all": {"patch": {"operationId": "NotificationsController_markAllAsRead", "summary": "标记所有通知为已读", "parameters": [], "responses": {"200": {"description": "成功标记所有通知为已读"}}, "tags": ["web端/用户通知"], "security": [{"bearer": []}]}}, "/api/shop/notifications/unread-count": {"get": {"operationId": "NotificationsController_getUnreadCount", "summary": "获取未读通知数量", "parameters": [], "responses": {"200": {"description": "成功获取未读通知数量"}}, "tags": ["web端/用户通知"], "security": [{"bearer": []}]}}, "/api/shop/settings": {"get": {"operationId": "SettingsController_getAllSettings", "summary": "获取用户所有设置", "parameters": [], "responses": {"200": {"description": "成功获取用户设置"}}, "tags": ["web端/用户设置"], "security": [{"bearer": []}]}, "post": {"operationId": "SettingsController_updateSettings", "summary": "创建或更新用户设置", "parameters": [], "responses": {"201": {"description": "成功创建或更新用户设置"}}, "tags": ["web端/用户设置"], "security": [{"bearer": []}]}}, "/api/shop/settings/{type}": {"get": {"operationId": "SettingsController_getSettingsByType", "summary": "获取指定类型的用户设置", "parameters": [{"name": "type", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取指定类型的用户设置"}}, "tags": ["web端/用户设置"], "security": [{"bearer": []}]}}, "/api/shop/settings/{type}/{key}": {"delete": {"operationId": "SettingsController_deleteSetting", "summary": "删除用户设置", "parameters": [{"name": "type", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "key", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功删除用户设置"}}, "tags": ["web端/用户设置"], "security": [{"bearer": []}]}}, "/api/mail-test/health": {"get": {"operationId": "MailTestController_healthCheck", "summary": "邮件服务健康检查", "parameters": [], "responses": {"200": {"description": "健康检查结果"}}, "tags": ["邮件服务测试"]}}, "/api/mail-test/send-test": {"post": {"operationId": "MailTestController_sendTestEmail", "summary": "发送测试邮件", "parameters": [{"name": "to", "required": true, "in": "query", "description": "收件人邮箱", "schema": {"example": "<EMAIL>", "type": "string"}}], "responses": {"200": {"description": "测试邮件发送结果"}}, "tags": ["邮件服务测试"]}}, "/api/mail-test/send-verification-code": {"post": {"operationId": "MailTestController_sendVerificationCodeTest", "summary": "发送验证码邮件测试", "parameters": [], "responses": {"200": {"description": "验证码邮件发送结果"}}, "tags": ["邮件服务测试"]}}, "/api/mail-test/debug-verification-code": {"get": {"operationId": "MailTestController_debugVerificationCode", "summary": "调试验证码（查看 Redis 中的验证码）", "parameters": [{"name": "email", "required": true, "in": "query", "description": "邮箱地址", "schema": {"example": "<EMAIL>", "type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "验证码类型", "schema": {"enum": ["register", "login"], "type": "string"}}], "responses": {"200": {"description": "验证码调试信息"}}, "tags": ["邮件服务测试"]}}, "/api/shop/proxy/static/list": {"get": {"operationId": "StaticProxyController_listUserStaticProxies", "summary": "获取用户的所有静态代理列表", "parameters": [], "responses": {"200": {"description": "成功获取静态代理列表"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/list/detailed": {"get": {"operationId": "StaticProxyController_listUserStaticProxiesDetailed", "summary": "获取用户的详细静态代理列表（从供应商同步）", "parameters": [], "responses": {"200": {"description": "成功获取详细静态代理列表"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/{instanceId}": {"get": {"operationId": "StaticProxyController_getStaticProxyDetails", "summary": "获取指定静态代理的详细信息", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取静态代理详情"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/batch/credentials": {"post": {"operationId": "StaticProxyController_batchUpdateStaticCredentials", "summary": "批量更新静态代理认证信息", "parameters": [], "responses": {"200": {"description": "成功批量更新认证信息"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/{instanceId}/replace": {"post": {"operationId": "StaticProxyController_replaceStaticProxy", "summary": "更换静态代理IP", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更换IP"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/{instanceId}/credentials": {"post": {"operationId": "StaticProxyController_updateStaticCredentials", "summary": "更新静态代理认证信息", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新认证信息"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/static/{instanceId}/whitelist": {"post": {"operationId": "StaticProxyController_updateStaticWhitelist", "summary": "更新静态代理白名单", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新白名单"}}, "tags": ["Static Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/dynamic/channels": {"get": {"operationId": "DynamicProxyController_listUserChannels", "summary": "获取用户的所有动态代理Channel", "parameters": [], "responses": {"200": {"description": "成功获取Channel列表"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/dynamic/channels/{instanceId}": {"get": {"operationId": "DynamicProxyController_getChannelDetails", "summary": "获取指定Channel的详细信息", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "Channel实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取Channel详情"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}, "put": {"operationId": "DynamicProxyController_updateChannel", "summary": "更新Channel设置", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "Channel实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新Channel"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/dynamic/channels/{instanceId}/traffic": {"get": {"operationId": "DynamicProxyController_getChannelTraffic", "summary": "获取Channel的流量使用情况", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "Channel实例ID", "schema": {"type": "number"}}, {"name": "dateType", "required": false, "in": "query", "description": "日期类型: 0今天, 1昨天, 2最近7天, 3最近30天, 4自定义", "schema": {"type": "number"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始日期 (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束日期 (YYYY-MM-DD)", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取流量数据"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/dynamic/channels/{instanceId}/endpoints": {"post": {"operationId": "DynamicProxyController_generateEndpoints", "summary": "为Channel生成Endpoints", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "Channel实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功生成Endpoints"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}, "get": {"operationId": "DynamicProxyController_listChannelEndpoints", "summary": "列出Channel的所有Endpoints", "parameters": [{"name": "instanceId", "required": true, "in": "path", "description": "Channel实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取Endpoints列表"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}}, "/api/shop/proxy/dynamic/endpoints/{endpointId}": {"delete": {"operationId": "DynamicProxyController_deleteEndpoint", "summary": "删除Endpoint", "parameters": [{"name": "endpointId", "required": true, "in": "path", "description": "Endpoint ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功删除Endpoint"}}, "tags": ["Dynamic Proxy Management"], "security": [{"bearer": []}]}}, "/api/wallet/customers/{id}/wallet": {"get": {"operationId": "WalletController_getCustomerWallet", "summary": "获取客户余额信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}, "put": {"operationId": "WalletController_updateCustomerWallet", "summary": "更新客户余额", "parameters": [{"name": "id", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWalletDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "客户不存在"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/transactions": {"get": {"operationId": "WalletController_getTransactions", "summary": "获取交易记录", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "customerId", "required": false, "in": "query", "description": "客户ID", "schema": {"type": "number"}}, {"name": "customerEmail", "required": false, "in": "query", "description": "客户邮箱", "schema": {"type": "string"}}, {"name": "operatorUserName", "required": false, "in": "query", "description": "操作员用户名", "schema": {"type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "交易类型", "schema": {"enum": ["1", "2", "3", "4", "5"], "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "交易状态", "schema": {"enum": ["0", "1", "2", "3", "4"], "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序字段", "schema": {"enum": ["createTime", "amount"], "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "排序方式", "schema": {"enum": ["ASC", "DESC"], "type": "string"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/customer/{customerId}": {"get": {"operationId": "WalletController_getCustomerWalletInfo", "summary": "查询客户余额信息", "parameters": [{"name": "customerId", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}, "404": {"description": "余额不存在"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/recharge-list": {"get": {"operationId": "WalletController_getRechargeList", "summary": "查询充值记录列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "customerId", "required": false, "in": "query", "description": "客户ID", "schema": {"type": "number"}}, {"name": "operatorUserId", "required": false, "in": "query", "description": "操作员ID", "schema": {"type": "number"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/statistics": {"get": {"operationId": "WalletController_getStatistics", "summary": "获取余额统计数据", "parameters": [{"name": "type", "required": true, "in": "query", "description": "统计类型", "schema": {"default": "daily", "enum": ["daily", "monthly"], "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/customer/{customerId}/adjust": {"post": {"operationId": "WalletController_adjustBalance", "summary": "调整客户余额", "parameters": [{"name": "customerId", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdjustBalanceDto"}}}}, "responses": {"200": {"description": "调整成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/customer/{customerId}/freeze": {"post": {"operationId": "WalletController_freezeUnfreezeBalance", "summary": "冻结/解冻客户余额", "parameters": [{"name": "customerId", "required": true, "in": "path", "description": "客户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FreezeBalanceDto"}}}}, "responses": {"200": {"description": "操作成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/wallet/recharge/order": {"post": {"operationId": "WalletController_createRechargeOrder", "summary": "创建充值订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRechargeOrderDto"}}}}, "responses": {"201": {"description": "充值订单创建成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["系统-余额管理"], "security": [{"bearer": []}]}}, "/api/payment/create": {"post": {"operationId": "PaymentController_createPayment", "summary": "创建支付订单", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDto"}}}}, "responses": {"201": {"description": "创建成功"}}, "tags": ["支付管理"]}}, "/api/payment/order/status": {"get": {"operationId": "PaymentController_getOrderStatus", "summary": "查询订单支付状态", "parameters": [{"name": "orderId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["支付管理"]}}, "/api/payment/zpay/notify": {"get": {"operationId": "PaymentController_handleZpayNotifyGet", "summary": "ZPAY原生支付异步通知(GET)", "parameters": [], "responses": {"200": {"description": "处理成功"}}, "tags": ["支付管理"]}, "post": {"operationId": "PaymentController_handleZpayNotifyPost", "summary": "ZPAY原生支付异步通知(POST备用)", "parameters": [], "responses": {"200": {"description": "处理成功"}}, "tags": ["支付管理"]}}, "/api/payment/notify": {"post": {"operationId": "PaymentController_handleNotifyPost", "summary": "支付异步通知(POST)", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentNotifyDto"}}}}, "responses": {"200": {"description": "处理成功，返回success字符串"}}, "tags": ["支付管理"]}, "get": {"operationId": "PaymentController_handleNotifyGet", "summary": "支付异步通知(GET)", "parameters": [{"name": "pid", "required": true, "in": "query", "description": "商户ID", "schema": {"type": "string"}}, {"name": "trade_no", "required": true, "in": "query", "description": "交易号", "schema": {"type": "string"}}, {"name": "out_trade_no", "required": true, "in": "query", "description": "商户订单号", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "交易类型", "schema": {"type": "string"}}, {"name": "name", "required": true, "in": "query", "description": "商品名称", "schema": {"type": "string"}}, {"name": "money", "required": true, "in": "query", "description": "交易金额", "schema": {"type": "string"}}, {"name": "trade_status", "required": true, "in": "query", "description": "交易状态", "schema": {"type": "string"}}, {"name": "sign", "required": true, "in": "query", "description": "签名", "schema": {"type": "string"}}, {"name": "sign_type", "required": true, "in": "query", "description": "签名类型", "schema": {"type": "string"}}, {"name": "param", "required": false, "in": "query", "description": "业务扩展参数", "schema": {"type": "string"}}], "responses": {"200": {"description": "处理成功，返回success字符串"}}, "tags": ["支付管理"]}}, "/api/payment/debug/order/{orderId}": {"get": {"operationId": "PaymentController_debugCheckOrder", "summary": "调试：检查订单是否存在", "parameters": [{"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "返回订单信息"}}, "tags": ["支付管理"]}}, "/api/payment/return": {"get": {"operationId": "PaymentController_handleReturn", "summary": "支付同步跳转", "parameters": [{"name": "pid", "required": true, "in": "query", "description": "商户ID", "schema": {"type": "string"}}, {"name": "trade_no", "required": true, "in": "query", "description": "交易号", "schema": {"type": "string"}}, {"name": "out_trade_no", "required": true, "in": "query", "description": "商户订单号", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "交易类型", "schema": {"type": "string"}}, {"name": "name", "required": true, "in": "query", "description": "商品名称", "schema": {"type": "string"}}, {"name": "money", "required": true, "in": "query", "description": "交易金额", "schema": {"type": "string"}}, {"name": "trade_status", "required": true, "in": "query", "description": "交易状态", "schema": {"type": "string"}}, {"name": "sign", "required": true, "in": "query", "description": "签名", "schema": {"type": "string"}}, {"name": "sign_type", "required": true, "in": "query", "description": "签名类型", "schema": {"type": "string"}}], "responses": {"302": {"description": "重定向到前端"}}, "tags": ["支付管理"]}}, "/api/shop/transactions/operations": {"post": {"operationId": "TransactionOperationController_createOperation", "summary": "执行财务操作", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOperationDto"}}}}, "responses": {"201": {"description": "操作成功"}}, "tags": ["财务操作"], "security": [{"bearer": []}]}, "get": {"operationId": "TransactionOperationController_getOperations", "summary": "查询用户财务操作历史", "parameters": [{"name": "page", "required": true, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 20, "type": "number"}}, {"name": "transactionType", "required": false, "in": "query", "description": "交易类型筛选", "schema": {"type": "array", "items": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}}}, {"name": "accountType", "required": false, "in": "query", "description": "账户类型筛选", "schema": {"type": "array", "items": {"type": "string", "enum": ["WALLET", "WALLET_RECHARGE", "WALLET_BONUS", "ALIPAY", "WECHAT_PAY", "POINTS", "INTERNAL_SETTLEMENT"]}}}, {"name": "direction", "required": false, "in": "query", "description": "流向筛选", "schema": {"type": "array", "items": {"type": "string", "enum": ["C", "D"]}}}, {"name": "dateStart", "required": false, "in": "query", "description": "开始日期", "schema": {"format": "date-time", "type": "string"}}, {"name": "dateEnd", "required": false, "in": "query", "description": "结束日期", "schema": {"format": "date-time", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["财务操作"], "security": [{"bearer": []}]}}, "/api/shop/transactions/operations/{operationId}": {"get": {"operationId": "TransactionOperationController_getOperationDetails", "summary": "查询特定操作的详细流水", "parameters": [{"name": "operationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["财务操作"], "security": [{"bearer": []}]}}, "/api/system/faq": {"post": {"operationId": "FaqController_create", "summary": "创建FAQ", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFaqDto"}}}}, "responses": {"201": {"description": "创建成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "409": {"description": "FAQ问题已存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}, "get": {"operationId": "FaqController_findAll", "summary": "获取FAQ列表", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "keyword", "required": false, "in": "query", "description": "关键字搜索", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "分类", "schema": {"type": "string"}}, {"name": "isPublished", "required": false, "in": "query", "description": "是否发布", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}}, "/api/system/faq/{id}": {"get": {"operationId": "FaqController_findOne", "summary": "获取FAQ详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "FAQ ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "FAQ不存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}, "put": {"operationId": "FaqController_update", "summary": "更新FAQ", "parameters": [{"name": "id", "required": true, "in": "path", "description": "FAQ ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFaqDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "FAQ不存在"}, "409": {"description": "FAQ问题已存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}, "delete": {"operationId": "FaqController_remove", "summary": "删除FAQ", "parameters": [{"name": "id", "required": true, "in": "path", "description": "FAQ ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "FAQ不存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}}, "/api/system/feedback": {"get": {"operationId": "FeedbackController_findAll", "summary": "获取反馈列表", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "customerId", "required": false, "in": "query", "description": "客户ID", "schema": {"type": "number"}}, {"name": "type", "required": false, "in": "query", "description": "反馈类型", "schema": {"enum": ["suggestion", "complaint", "question", "other"], "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "反馈状态", "schema": {"enum": ["0", "1", "2"], "type": "string"}}, {"name": "keyword", "required": false, "in": "query", "description": "关键字搜索", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}}, "/api/system/feedback/{id}": {"get": {"operationId": "FeedbackController_findOne", "summary": "获取反馈详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "反馈ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "反馈不存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}}, "/api/system/feedback/{id}/reply": {"put": {"operationId": "FeedbackController_replyFeedback", "summary": "回复反馈", "parameters": [{"name": "id", "required": true, "in": "path", "description": "反馈ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReplyFeedbackDto"}}}}, "responses": {"200": {"description": "回复成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "反馈不存在"}}, "tags": ["admin端/帮助与支持管理"], "security": [{"bearer": []}]}}, "/api/inventory-sync/manual": {"post": {"operationId": "InventorySyncController_manualSync", "summary": "手动触发库存同步", "parameters": [], "responses": {"200": {"description": "同步任务已启动"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "tags": ["admin端/库存同步管理"]}}, "/api/inventory-sync/status": {"get": {"operationId": "InventorySyncController_getSyncStatus", "summary": "获取库存同步状态", "parameters": [], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/库存同步管理"]}}, "/api/inventory-sync/ipnux/sync": {"post": {"operationId": "InventorySyncController_syncIpnuxInventory", "summary": "手动同步IPNux库存", "parameters": [], "responses": {"200": {"description": "IPNux库存同步已启动"}}, "tags": ["admin端/库存同步管理"]}}, "/api/system/payment/list": {"get": {"operationId": "PaymentController_findAll", "summary": "获取支付记录列表", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderNumber", "required": false, "in": "query", "description": "订单号", "schema": {"type": "string"}}, {"name": "transactionId", "required": false, "in": "query", "description": "交易号", "schema": {"type": "string"}}, {"name": "paymentMethod", "required": false, "in": "query", "description": "支付方式", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/stats": {"get": {"operationId": "PaymentController_getStats", "summary": "获取支付统计数据", "parameters": [], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/config": {"get": {"operationId": "PaymentController_getConfig", "summary": "获取支付配置", "parameters": [], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/qrcode/{transactionId}": {"get": {"operationId": "PaymentController_getPaymentQRCode", "summary": "获取或刷新支付二维码", "parameters": [{"name": "transactionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/qrcode/{transactionId}/regenerate": {"post": {"operationId": "PaymentController_regeneratePaymentQRCode", "summary": "重新生成支付二维码", "parameters": [{"name": "transactionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "生成成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/sync/{orderId}": {"post": {"operationId": "PaymentController_syncPaymentStatus", "summary": "同步支付状态", "parameters": [{"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "同步成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/payment/{id}": {"get": {"operationId": "PaymentController_findOne", "summary": "获取支付记录详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功"}}, "tags": ["admin端/支付管理"]}}, "/api/system/providers": {"get": {"operationId": "ProviderController_getProviders", "summary": "获取供应商列表", "parameters": [{"name": "keyword", "required": false, "in": "query", "description": "关键词搜索 (名称或代码)", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态 (0启用 1禁用)", "schema": {"enum": ["0", "1"], "type": "string"}}, {"name": "providerType", "required": false, "in": "query", "description": "供应商类型", "schema": {"enum": ["API", "SELF_HOSTED", "MANUAL_IMPORT", "SCRIPT_BASED"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "每页条数", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "成功获取供应商列表"}}, "tags": ["系统管理 - 供应商管理"]}, "post": {"operationId": "ProviderController_createProvider", "summary": "创建供应商", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProviderDto"}}}}, "responses": {"201": {"description": "成功创建供应商"}, "400": {"description": "无效的输入数据"}}, "tags": ["系统管理 - 供应商管理"]}}, "/api/system/providers/all": {"get": {"operationId": "ProviderController_getAllProviders", "summary": "获取所有启用的供应商（不分页）", "parameters": [], "responses": {"200": {"description": "成功获取所有供应商"}}, "tags": ["系统管理 - 供应商管理"]}}, "/api/system/providers/{id}": {"get": {"operationId": "ProviderController_getProvider", "summary": "获取供应商详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "供应商ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取供应商详情"}}, "tags": ["系统管理 - 供应商管理"]}, "put": {"operationId": "ProviderController_updateProvider", "summary": "更新供应商", "parameters": [{"name": "id", "required": true, "in": "path", "description": "供应商ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProviderDto"}}}}, "responses": {"200": {"description": "成功更新供应商"}, "400": {"description": "无效的输入数据"}, "404": {"description": "供应商未找到"}}, "tags": ["系统管理 - 供应商管理"]}, "delete": {"operationId": "ProviderController_deleteProvider", "summary": "删除供应商", "parameters": [{"name": "id", "required": true, "in": "path", "description": "供应商ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功删除供应商"}, "404": {"description": "供应商未找到"}}, "tags": ["系统管理 - 供应商管理"]}}, "/api/system/providers/{id}/status": {"put": {"operationId": "ProviderController_updateProviderStatus", "summary": "更新供应商状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "供应商ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "enum": ["0", "1"]}}}}}}, "responses": {"200": {"description": "成功更新供应商状态"}, "400": {"description": "无效的状态值"}, "404": {"description": "供应商未找到"}}, "tags": ["系统管理 - 供应商管理"]}}, "/api/system/providers/{id}/test-connection": {"post": {"operationId": "ProviderController_testProviderConnection", "summary": "测试供应商API连接", "parameters": [{"name": "id", "required": true, "in": "path", "description": "供应商ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "连接测试请求已发送或已完成"}, "404": {"description": "供应商未找到"}, "500": {"description": "连接测试失败"}}, "tags": ["系统管理 - 供应商管理"]}}, "/api/system/proxy/dynamic": {"get": {"operationId": "DynamicProxyController_getDynamicProxies", "summary": "获取动态代理Channel列表", "parameters": [{"name": "customerId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "channelName", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取动态代理列表"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/{id}": {"get": {"operationId": "DynamicProxyController_getDynamicProxy", "summary": "获取动态代理Channel详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取动态代理详情"}}, "tags": ["系统管理 - 动态代理管理"]}, "put": {"operationId": "DynamicProxyController_updateDynamicProxy", "summary": "更新动态代理Channel设置", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新Channel设置"}}, "tags": ["系统管理 - 动态代理管理"]}, "delete": {"operationId": "DynamicProxyController_deleteDynamicProxy", "summary": "删除动态代理Channel", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功删除动态代理"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/{id}/traffic": {"get": {"operationId": "DynamicProxyController_getChannelTraffic", "summary": "获取动态代理Channel流量使用情况", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}, {"name": "dateType", "required": false, "in": "query", "description": "日期类型: 0今天, 1昨天, 2最近7天, 3最近30天, 4自定义", "schema": {"type": "number"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始日期 (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束日期 (YYYY-MM-DD)", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取流量数据"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/{id}/endpoints": {"post": {"operationId": "DynamicProxyController_generateEndpoints", "summary": "为动态代理Channel生成Endpoints", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功生成Endpoints"}}, "tags": ["系统管理 - 动态代理管理"]}, "get": {"operationId": "DynamicProxyController_getChannelEndpoints", "summary": "获取动态代理Channel的Endpoints列表", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取Endpoints列表"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/{id}/list-endpoints": {"get": {"operationId": "DynamicProxyController_listEndpoints", "summary": "获取动态代理Channel的Endpoints列表 (别名)", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取Endpoints列表"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/endpoints/{endpointId}": {"delete": {"operationId": "DynamicProxyController_deleteEndpoint", "summary": "删除Endpoint", "parameters": [{"name": "endpointId", "required": true, "in": "path", "description": "Endpoint ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功删除Endpoint"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/dynamic/{id}/refresh-status": {"post": {"operationId": "DynamicProxyController_refreshDynamicProxyStatus", "summary": "刷新动态代理Channel状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功刷新状态"}}, "tags": ["系统管理 - 动态代理管理"]}}, "/api/system/proxy/static": {"get": {"operationId": "StaticProxyController_getStaticProxies", "summary": "获取静态代理实例列表", "parameters": [{"name": "customerId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "ip<PERSON><PERSON><PERSON>", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取静态代理列表"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/static/{id}": {"get": {"operationId": "StaticProxyController_getStaticProxy", "summary": "获取静态代理实例详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取静态代理详情"}}, "tags": ["系统管理 - 静态代理管理"]}, "delete": {"operationId": "StaticProxyController_deleteStaticProxy", "summary": "删除静态代理实例", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功删除静态代理"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/static/{id}/replace": {"post": {"operationId": "StaticProxyController_replaceStaticProxy", "summary": "更换静态代理IP", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更换IP"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/static/{id}/credentials": {"post": {"operationId": "StaticProxyController_updateStaticCredentials", "summary": "更新静态代理认证信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新认证信息"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/static/{id}/whitelist": {"post": {"operationId": "StaticProxyController_updateStaticWhitelist", "summary": "更新静态代理白名单", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功更新白名单"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/static/{id}/refresh-status": {"post": {"operationId": "StaticProxyController_refreshStaticProxyStatus", "summary": "刷新静态代理状态", "parameters": [{"name": "id", "required": true, "in": "path", "description": "代理实例ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功刷新状态"}}, "tags": ["系统管理 - 静态代理管理"]}}, "/api/system/proxy/instances": {"get": {"operationId": "ProxyController_getProxyInstances", "summary": "获取代理实例列表 (静态/动态)", "parameters": [{"name": "ipType", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "customerId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "ip<PERSON><PERSON><PERSON>", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "channelName", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "pageNum", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["系统管理-代理管理"]}}, "/api/system/proxy/sync-provider-ips": {"post": {"operationId": "ProxyController_syncProviderIPs", "summary": "同步供应商已购IP", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["系统管理-代理管理"]}}, "/api/system/proxy/sync-status": {"get": {"operationId": "ProxyController_getSyncStatus", "summary": "获取同步状态", "parameters": [{"name": "taskId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统管理-代理管理"]}}, "/api/system/proxy/unassigned-ips": {"get": {"operationId": "ProxyController_getUnassignedIPs", "summary": "获取未分配的IP列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["系统管理-代理管理"]}}, "/api/system/proxy/assign-ips": {"post": {"operationId": "ProxyController_assignIPsToCustomer", "summary": "分配IP给客户", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["系统管理-代理管理"]}}, "/api/monitor/job/list": {"get": {"operationId": "JobController_list", "summary": "获取定时任务列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job/{jobId}": {"get": {"operationId": "JobController_getInfo", "summary": "获取定时任务详细信息", "parameters": [{"name": "jobId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job": {"post": {"operationId": "JobController_add", "summary": "创建定时任务", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务管理"]}, "put": {"operationId": "JobController_update", "summary": "修改定时任务", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job/changeStatus": {"put": {"operationId": "JobController_changeStatus", "summary": "修改任务状态", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job/{jobIds}": {"delete": {"operationId": "JobController_remove", "summary": "删除定时任务", "parameters": [{"name": "jobIds", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job/run": {"put": {"operationId": "JobController_run", "summary": "立即执行一次", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/job/export": {"post": {"operationId": "JobController_export", "summary": "导出定时任务为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListJobDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务管理"]}}, "/api/monitor/jobLog/list": {"get": {"operationId": "JobLogController_list", "summary": "获取定时任务日志列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "job<PERSON>ame", "required": true, "in": "query", "description": "任务名称", "schema": {"type": "string"}}, {"name": "jobGroup", "required": true, "in": "query", "description": "任务组名", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "状态（0正常 1暂停）", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/api/monitor/jobLog/clean": {"delete": {"operationId": "JobLogController_clean", "summary": "清空定时任务日志", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/api/monitor/jobLog/export": {"post": {"operationId": "JobLogController_export", "summary": "导出调度日志为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListJobLogDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/api/shop/auth/register": {"post": {"operationId": "AuthController_register", "summary": "客户注册", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": "注册成功"}, "400": {"description": "请求参数无效"}, "409": {"description": "用户名或邮箱已存在"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/login": {"post": {"operationId": "AuthController_login", "summary": "客户登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "登录成功"}, "401": {"description": "认证失败"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/logout": {"post": {"operationId": "AuthController_logout", "summary": "客户登出", "parameters": [], "responses": {"200": {"description": "登出成功"}}, "tags": ["web端/客户认证"], "security": [{"bearer": []}]}}, "/api/shop/auth/refresh-token": {"post": {"operationId": "AuthController_refreshToken", "summary": "刷新令牌", "parameters": [], "responses": {"200": {"description": "刷新成功"}, "401": {"description": "无效的刷新令牌"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/profile": {"get": {"operationId": "AuthController_getProfile", "summary": "获取客户信息", "parameters": [], "responses": {"200": {"description": "获取成功"}, "401": {"description": "未授权"}}, "tags": ["web端/客户认证"], "security": [{"bearer": []}]}, "put": {"operationId": "AuthController_updateProfile", "summary": "更新客户信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "请求参数无效"}, "401": {"description": "未授权"}}, "tags": ["web端/客户认证"], "security": [{"bearer": []}]}}, "/api/shop/auth/password": {"put": {"operationId": "AuthController_changePassword", "summary": "修改密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "修改成功"}, "400": {"description": "请求参数无效或旧密码不正确"}, "401": {"description": "未授权"}}, "tags": ["web端/客户认证"], "security": [{"bearer": []}]}}, "/api/shop/auth/reset-password-request": {"post": {"operationId": "AuthController_resetPasswordRequest", "summary": "请求重置密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequestDto"}}}}, "responses": {"200": {"description": "请求成功"}, "400": {"description": "请求参数无效"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/reset-password": {"post": {"operationId": "AuthController_resetPassword", "summary": "重置密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "重置成功"}, "400": {"description": "请求参数无效或令牌无效"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/send-registration-code": {"post": {"operationId": "AuthController_sendRegistrationCode", "summary": "发送注册验证码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCodeDto"}}}}, "responses": {"200": {"description": "验证码发送成功"}, "400": {"description": "请求参数错误或邮箱已被注册"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/register-with-code": {"post": {"operationId": "AuthController_registerWithCode", "summary": "使用验证码注册", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterWithCodeDto"}}}}, "responses": {"201": {"description": "注册成功"}, "400": {"description": "请求参数错误或邮箱已被注册"}, "401": {"description": "验证码无效"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/send-login-code": {"post": {"operationId": "AuthController_sendLoginCode", "summary": "发送登录验证码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCodeDto"}}}}, "responses": {"200": {"description": "验证码发送成功"}, "400": {"description": "请求参数错误或邮箱未注册"}}, "tags": ["web端/客户认证"]}}, "/api/shop/auth/login-with-code": {"post": {"operationId": "AuthController_loginWithCode", "summary": "使用验证码登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginWithCodeDto"}}}}, "responses": {"200": {"description": "登录成功"}, "401": {"description": "验证码无效或邮箱未注册"}}, "tags": ["web端/客户认证"]}}, "/api/shop/faq": {"get": {"operationId": "FaqController_getFaqs", "summary": "获取FAQ列表", "parameters": [{"name": "category", "required": false, "in": "query", "description": "分类", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": "成功获取FAQ列表"}}, "tags": ["web端/常见问题"]}}, "/api/shop/faq/{id}": {"get": {"operationId": "FaqController_getFaq", "summary": "获取FAQ详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取FAQ详情"}}, "tags": ["web端/常见问题"]}}, "/api/shop/faq/categories": {"get": {"operationId": "FaqController_getCategories", "summary": "获取FAQ分类列表", "parameters": [], "responses": {"200": {"description": "成功获取FAQ分类列表"}}, "tags": ["web端/常见问题"]}}, "/api/shop/feedback": {"post": {"operationId": "FeedbackController_createFeedback", "summary": "提交反馈", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFeedbackDto"}}}}, "responses": {"201": {"description": "成功提交反馈"}}, "tags": ["web端/用户反馈"], "security": [{"bearer": []}]}, "get": {"operationId": "FeedbackController_getFeedbacks", "summary": "获取反馈列表", "parameters": [{"name": "status", "required": false, "in": "query", "description": "状态（0未处理 1处理中 2已处理）", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": "成功获取反馈列表"}}, "tags": ["web端/用户反馈"], "security": [{"bearer": []}]}}, "/api/shop/feedback/{id}": {"get": {"operationId": "FeedbackController_getFeedback", "summary": "获取反馈详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "成功获取反馈详情"}}, "tags": ["web端/用户反馈"], "security": [{"bearer": []}]}}, "/api/shop/dict/data/type/{dictType}": {"get": {"operationId": "ShopDictController_findOneDataType", "summary": "字典数据-根据类型获取【走缓存】", "description": "获取指定字典类型的所有数据项，用于前端下拉选择等场景", "parameters": [{"name": "dictType", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["商城/字典管理"]}}, "/api/shop/payment-methods": {"get": {"operationId": "PaymentMethodsController_getPaymentMethods", "summary": "获取可用支付方式列表", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentMethodDto"}}, "message": {"type": "string", "example": "Success"}, "logId": {"type": "string", "format": "uuid"}}}}}}}, "tags": ["支付方式管理"]}}, "/api/monitor/server": {"get": {"operationId": "ServerController_getInfo", "summary": "在线用户-列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["系统监控-服务监控"]}}, "/api/monitor/cache": {"get": {"operationId": "CacheController_getInfo", "summary": "缓存监控信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/getNames": {"get": {"operationId": "CacheController_getNames", "summary": "缓存列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/getKeys/{id}": {"get": {"operationId": "CacheController_get<PERSON>eys", "summary": "键名列表", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/getValue/{cacheName}/{cacheKey}": {"get": {"operationId": "CacheController_getValue", "summary": "缓存内容", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/clearCacheName/{cacheName}": {"delete": {"operationId": "CacheController_clearCacheName", "summary": "清理缓存名称", "parameters": [{"name": "cacheName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/clearCacheKey/{cacheKey}": {"delete": {"operationId": "CacheController_clearCache<PERSON>ey", "summary": "清理缓存键名", "parameters": [{"name": "cache<PERSON>ey", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/cache/clearCacheAll": {"delete": {"operationId": "CacheController_clearCacheAll", "summary": "清理全部", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/api/monitor/logininfor/list": {"get": {"operationId": "LoginlogController_findAll", "summary": "登录日志-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "ipaddr", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListLoginlogDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/api/monitor/logininfor/clean": {"delete": {"operationId": "LoginlogController_removeAll", "summary": "登录日志-清除全部日志", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/api/monitor/logininfor/{id}": {"delete": {"operationId": "LoginlogController_remove", "summary": "登录日志-删除日志", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/api/monitor/logininfor/export": {"post": {"operationId": "LoginlogController_export", "summary": "导出登录日志为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListLoginlogDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["登录日志"]}}, "/api/monitor/online/list": {"get": {"operationId": "OnlineController_findAll", "summary": "在线用户-列表", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OnlineListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["系统监控-在线用户"]}}, "/api/monitor/online/{token}": {"delete": {"operationId": "OnlineController_delete", "summary": "在线用户-强退", "parameters": [{"name": "token", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统监控-在线用户"]}}, "/api/monitor/operlog": {"post": {"operationId": "OperlogController_create", "summary": "创建操作日志", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOperlogDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["操作日志"]}}, "/api/monitor/operlog/clean": {"delete": {"operationId": "OperlogController_removeAll", "summary": "登录日志-清除全部日志", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["操作日志"]}}, "/api/monitor/operlog/list": {"get": {"operationId": "OperlogController_findAll", "summary": "获取操作日志列表", "parameters": [{"name": "businessModule", "required": false, "in": "query", "description": "业务模块", "schema": {"example": "customer", "enum": ["customer", "order", "product", "provider", "proxy", "system", "user", "role", "menu", "dept", "post", "dict", "config", "notice", "auth", "wallet", "payment", "inventory", "location", "other"], "type": "string"}}, {"name": "businessType", "required": false, "in": "query", "description": "业务类型", "schema": {"example": 1, "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "number"}}, {"name": "businessId", "required": false, "in": "query", "description": "业务ID（如客户ID、订单ID等）", "schema": {"example": "123", "type": "string"}}, {"name": "operName", "required": false, "in": "query", "description": "操作人员", "schema": {"example": "admin", "type": "string"}}, {"name": "title", "required": false, "in": "query", "description": "操作标题（模糊搜索）", "schema": {"example": "新增客户", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "操作状态 (0=正常, 1=失败)", "schema": {"example": "0", "type": "string"}}, {"name": "operIp", "required": false, "in": "query", "description": "请求IP地址", "schema": {"example": "127.0.0.1", "type": "string"}}, {"name": "operLocation", "required": false, "in": "query", "description": "操作地点", "schema": {"example": "内网IP", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"example": "2024-01-01 00:00:00", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"example": "2024-12-31 23:59:59", "type": "string"}}, {"name": "pageNum", "required": true, "in": "query", "description": "页码", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "orderBy", "required": false, "in": "query", "description": "排序字段", "schema": {"example": "operTime", "type": "string"}}, {"name": "orderDirection", "required": false, "in": "query", "description": "排序方向 (ASC|DESC)", "schema": {"example": "DESC", "type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["操作日志"]}}, "/api/monitor/operlog/{id}": {"get": {"operationId": "OperlogController_findOne", "summary": "获取单个操作日志详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["操作日志"]}, "patch": {"operationId": "OperlogController_update", "summary": "更新操作日志", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOperlogDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["操作日志"]}, "delete": {"operationId": "OperlogController_remove", "summary": "删除单个操作日志", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["操作日志"]}}}, "info": {"title": "Nest-Admin", "description": "Nest-Admin 接口文档", "version": "2.0.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"token": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "password": {"type": "string", "description": "密码", "example": "Password123!"}}, "required": ["email", "password"]}, "RegisterDto": {"type": "object", "properties": {"password": {"type": "string", "description": "密码", "example": "Password123!"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}}, "required": ["password", "email"]}, "FileUploadDto": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}, "ChunkMergeFileDto": {"type": "object", "properties": {"uploadId": {"type": "string"}, "fileName": {"type": "string"}}, "required": ["uploadId", "fileName"]}, "CreateConfigDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "DateParamsDTO": {"type": "object", "properties": {}}, "ListConfigDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateConfigDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "CreateDeptDto": {"type": "object", "properties": {"parentId": {"type": "number"}, "deptName": {"type": "string"}, "orderNum": {"type": "number"}, "leader": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "status": {"type": "string"}}, "required": ["parentId", "deptName", "orderNum"]}, "UpdateDeptDto": {"type": "object", "properties": {"parentId": {"type": "number"}, "deptName": {"type": "string"}, "orderNum": {"type": "number"}, "leader": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "status": {"type": "string"}, "deptId": {"type": "number"}}, "required": ["parentId", "deptName", "orderNum"]}, "CreateDictTypeDto": {"type": "object", "properties": {"dictName": {"type": "string"}, "dictType": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["dictName", "dictType", "remark"]}, "UpdateDictTypeDto": {"type": "object", "properties": {"dictName": {"type": "string"}, "dictType": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["dictName", "dictType", "remark"]}, "CreateDictDataDto": {"type": "object", "properties": {}}, "UpdateDictDataDto": {"type": "object", "properties": {}}, "ListDictType": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "CreateLocationRegionDto": {"type": "object", "properties": {"regionCode": {"type": "string", "description": "区域代码"}, "regionName": {"type": "string", "description": "区域名称"}, "regionNameEn": {"type": "string", "description": "区域英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}, "required": ["regionCode", "regionName"]}, "UpdateLocationRegionDto": {"type": "object", "properties": {"regionCode": {"type": "string", "description": "区域代码"}, "regionName": {"type": "string", "description": "区域名称"}, "regionNameEn": {"type": "string", "description": "区域英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}}, "CreateLocationCountryDto": {"type": "object", "properties": {"regionId": {"type": "number", "description": "所属区域ID"}, "countryCode": {"type": "string", "description": "国家代码（ISO 3166-1）"}, "countryName": {"type": "string", "description": "国家名称"}, "countryNameEn": {"type": "string", "description": "国家英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}, "required": ["regionId", "countryCode", "countryName"]}, "UpdateLocationCountryDto": {"type": "object", "properties": {"regionId": {"type": "number", "description": "所属区域ID"}, "countryCode": {"type": "string", "description": "国家代码（ISO 3166-1）"}, "countryName": {"type": "string", "description": "国家名称"}, "countryNameEn": {"type": "string", "description": "国家英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}}, "CreateLocationCityDto": {"type": "object", "properties": {"countryId": {"type": "number", "description": "所属国家ID"}, "cityCode": {"type": "string", "description": "城市代码"}, "cityName": {"type": "string", "description": "城市名称"}, "cityNameEn": {"type": "string", "description": "城市英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "isHot": {"type": "number", "description": "是否热门城市"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}, "required": ["countryId", "cityName"]}, "UpdateLocationCityDto": {"type": "object", "properties": {"countryId": {"type": "number", "description": "所属国家ID"}, "cityCode": {"type": "string", "description": "城市代码"}, "cityName": {"type": "string", "description": "城市名称"}, "cityNameEn": {"type": "string", "description": "城市英文名"}, "sortOrder": {"type": "number", "description": "排序"}, "isHot": {"type": "number", "description": "是否热门城市"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}}}, "CreateSupplierLocationMappingDto": {"type": "object", "properties": {"standardCityId": {"type": "number", "description": "标准城市ID"}, "providerId": {"type": "number", "description": "供应商ID"}, "supplierNativeCityId": {"type": "string", "description": "供应商系统中的城市ID"}, "supplierNativeCityName": {"type": "string", "description": "供应商系统中的城市名称"}, "mappingStatus": {"type": "string", "description": "映射状态（0正常 1待审核 2已废弃）", "default": "0"}, "confidenceLevel": {"type": "number", "description": "映射置信度 (0-100)", "default": 100}, "notes": {"type": "string", "description": "映射备注"}}, "required": ["standardCityId", "providerId", "supplierNativeCityId"]}, "BatchCreateSupplierLocationMappingDto": {"type": "object", "properties": {"mappings": {"description": "批量映射数据", "type": "array", "items": {"$ref": "#/components/schemas/CreateSupplierLocationMappingDto"}}}, "required": ["mappings"]}, "UpdateSupplierLocationMappingDto": {"type": "object", "properties": {"standardCityId": {"type": "number", "description": "标准城市ID"}, "providerId": {"type": "number", "description": "供应商ID"}, "supplierNativeCityId": {"type": "string", "description": "供应商系统中的城市ID"}, "supplierNativeCityName": {"type": "string", "description": "供应商系统中的城市名称"}, "mappingStatus": {"type": "string", "description": "映射状态（0正常 1待审核 2已废弃）", "default": "0"}, "confidenceLevel": {"type": "number", "description": "映射置信度 (0-100)", "default": 100}, "notes": {"type": "string", "description": "映射备注"}}}, "CreateMenuDto": {"type": "object", "properties": {"menuName": {"type": "string"}, "orderNum": {"type": "number"}, "parentId": {"type": "number"}, "path": {"type": "string"}, "query": {"type": "string"}, "component": {"type": "string"}, "icon": {"type": "string"}, "menuType": {"type": "string"}, "isCache": {"type": "string"}, "isFrame": {"type": "string"}, "status": {"type": "string"}, "visible": {"type": "string"}, "perms": {"type": "string"}}, "required": ["menuName", "parentId", "isFrame"]}, "UpdateMenuDto": {"type": "object", "properties": {"menuName": {"type": "string"}, "orderNum": {"type": "number"}, "parentId": {"type": "number"}, "path": {"type": "string"}, "query": {"type": "string"}, "component": {"type": "string"}, "icon": {"type": "string"}, "menuType": {"type": "string"}, "isCache": {"type": "string"}, "isFrame": {"type": "string"}, "status": {"type": "string"}, "visible": {"type": "string"}, "perms": {"type": "string"}, "menuId": {"type": "number"}}, "required": ["menuName", "parentId", "isFrame", "menuId"]}, "CreateNoticeDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "ListNoticeDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateNoticeDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "CreatePostDto": {"type": "object", "properties": {"remark": {"type": "string"}, "postSort": {"type": "number"}}, "required": ["postSort"]}, "ListPostDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdatePostDto": {"type": "object", "properties": {"remark": {"type": "string"}, "postSort": {"type": "number"}, "postId": {"type": "number"}}, "required": ["postSort", "postId"]}, "CreateRoleDto": {"type": "object", "properties": {"roleName": {"type": "string"}, "roleKey": {"type": "string"}, "roleSort": {"type": "number"}, "status": {"type": "string"}, "dataScope": {"type": "string"}, "remark": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort"]}, "ListRoleDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateRoleDto": {"type": "object", "properties": {"roleName": {"type": "string"}, "roleKey": {"type": "string"}, "roleSort": {"type": "number"}, "status": {"type": "string"}, "dataScope": {"type": "string"}, "remark": {"type": "string"}, "roleId": {"type": "number"}}, "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort", "roleId"]}, "ChangeStatusDto": {"type": "object", "properties": {"userId": {"type": "number"}, "status": {"type": "string"}}, "required": ["userId", "status"]}, "AllocatedListDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "userName": {"type": "string"}, "phonenumber": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "AuthUserCancelDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userId": {"type": "number"}}, "required": ["roleId", "userId"]}, "AuthUserCancelAllDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userIds": {"type": "string"}}, "required": ["roleId", "userIds"]}, "AuthUserSelectAllDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userIds": {"type": "string"}}, "required": ["roleId", "userIds"]}, "TableName": {"type": "object", "properties": {"tableNames": {"type": "string"}}, "required": ["tableNames"]}, "GenTableUpdate": {"type": "object", "properties": {"tableId": {"type": "number"}}, "required": ["tableId"]}, "UpdateProfileDto": {"type": "object", "properties": {"email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "手机号", "example": "13800138000"}, "nickname": {"type": "string", "description": "昵称", "example": "小明"}, "avatar": {"type": "string", "description": "头像", "example": "https://example.com/avatar.jpg"}}}, "UpdatePwdDto": {"type": "object", "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}}, "required": ["oldPassword", "newPassword"]}, "CreateUserDto": {"type": "object", "properties": {"deptId": {"type": "number"}, "email": {"type": "string"}, "nickName": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "phonenumber": {"type": "string"}, "postIds": {"type": "array", "items": {"type": "string"}}, "roleIds": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}, "sex": {"type": "string"}, "remark": {"type": "string"}, "postSort": {"type": "number"}}, "required": ["nick<PERSON><PERSON>", "userName", "password", "postSort"]}, "UpdateUserDto": {"type": "object", "properties": {"deptId": {"type": "number"}, "email": {"type": "string"}, "nickName": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "phonenumber": {"type": "string"}, "postIds": {"type": "array", "items": {"type": "string"}}, "roleIds": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}, "sex": {"type": "string"}, "remark": {"type": "string"}, "postSort": {"type": "number"}, "userId": {"type": "number"}}, "required": ["userId"]}, "ResetPwdDto": {"type": "object", "properties": {"userId": {"type": "number"}, "password": {"type": "string"}}, "required": ["userId", "password"]}, "ListUserDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "deptId": {"type": "string"}, "nickName": {"type": "string"}, "email": {"type": "string"}, "userName": {"type": "string"}, "phonenumber": {"type": "string"}, "status": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "SyncStrategy": {"type": "string", "description": "同步策略", "enum": ["FULL_SYNC", "STOCK_AND_COST_ONLY", "MANUAL_OVERRIDE", "DISABLED"]}, "CreateProductDto": {"type": "object", "properties": {"productName": {"type": "string", "description": "产品名称", "example": "Premium Proxy 10GB"}, "productDesc": {"type": "string", "description": "产品描述", "example": "High quality proxy service with 10GB data"}, "productType": {"type": "string", "description": "产品类型", "example": "1"}, "providerId": {"type": "number", "description": "供应商ID", "example": 1}, "price": {"type": "number", "description": "价格", "example": 99.99}, "discountPrice": {"type": "number", "description": "折扣价格", "example": 79.99}, "currency": {"type": "string", "description": "销售价格货币", "example": "CNY"}, "costPrice": {"type": "number", "description": "成本价", "example": 50}, "costPriceCurrency": {"type": "string", "description": "成本价货币", "example": "CNY"}, "syncStrategy": {"example": "FULL_SYNC", "$ref": "#/components/schemas/SyncStrategy"}, "flowAmount": {"type": "number", "description": "流量大小 (MB)", "example": 10240}, "validityPeriod": {"type": "number", "description": "有效期 (天)", "example": 30}, "imageUrl": {"type": "string", "description": "产品图片URL", "example": "https://example.com/image.jpg"}, "salesCount": {"type": "number", "description": "销售数量", "example": 0}, "sortOrder": {"type": "number", "description": "排序", "example": 0}, "status": {"type": "string", "description": "产品状态 0表示正常, 1表示下架", "example": "0", "enum": ["0", "1"]}, "remark": {"type": "string", "description": "备注", "example": "特别优惠套餐"}, "productProxyCategory": {"type": "string", "description": "产品代理分类", "example": "STATIC"}, "syncStatus": {"type": "string", "description": "同步状态", "example": "manual", "enum": ["manual", "auto", "syncing", "error"]}, "cityId": {"type": "number", "description": "城市ID", "example": 1}, "countryId": {"type": "number", "description": "国家ID", "example": 1}, "regionId": {"type": "number", "description": "地区ID", "example": 1}, "cityName": {"type": "string", "description": "城市名称", "example": "北京"}, "countryName": {"type": "string", "description": "国家名称", "example": "中国"}, "countryCode": {"type": "string", "description": "国家代码", "example": "CN"}, "regionName": {"type": "string", "description": "地区名称", "example": "亚洲"}, "inventoryStatus": {"type": "string", "description": "库存状态", "example": "available", "enum": ["available", "low", "out_of_stock"]}, "availableCount": {"type": "number", "description": "可用数量", "example": 100}, "minQuantity": {"type": "number", "description": "最小购买数量", "example": 1}, "maxQuantity": {"type": "number", "description": "最大购买数量", "example": 10}}, "required": ["productName", "productDesc", "productType", "providerId", "price", "flowAmount", "validityPeriod"]}, "UpdateProductDto": {"type": "object", "properties": {}}, "UpdateProductStatusDto": {"type": "object", "properties": {"status": {"type": "string", "description": "商品状态，0表示正常，1表示下架", "example": "0", "enum": ["0", "1"]}}, "required": ["status"]}, "SyncProductsDto": {"type": "object", "properties": {"providerId": {"type": "number", "description": "供应商ID（可选，不传则同步所有供应商）"}, "proxyType": {"type": "string", "description": "代理类型（可选）", "example": "Premium (ISP) proxies"}, "proxiesFormat": {"type": "number", "description": "代理格式（可选，1=HTTP, 2=SOCKS5）", "example": 2, "default": 2}, "purposeWeb": {"type": "string", "description": "IP用途（可选）", "example": "Tiktok", "default": "Tiktok"}, "syncType": {"type": "string", "description": "Type of sync (e.g., all, price, inventory)", "default": "all"}, "overrideStrategy": {"type": "string", "description": "强制同步策略覆盖（可选，会覆盖产品的默认同步策略）", "enum": ["FULL_SYNC", "STOCK_AND_COST_ONLY", "MANUAL_OVERRIDE", "DISABLED"], "example": "STOCK_AND_COST_ONLY"}}}, "ManualUpdatePriceDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "price": {"type": "number", "description": "新价格（人民币）"}, "discountPrice": {"type": "number", "description": "折扣价格（人民币，可选）"}, "reason": {"type": "string", "description": "更新原因", "example": "促销活动"}}, "required": ["id", "price", "reason"]}, "BatchStatusDto": {"type": "object", "properties": {"ids": {"description": "产品ID列表", "type": "array", "items": {"type": "number"}}, "status": {"type": "string", "description": "状态：0上架 1下架", "enum": ["0", "1"]}}, "required": ["ids", "status"]}, "BatchSyncDto": {"type": "object", "properties": {"productIds": {"description": "产品ID列表", "type": "array", "items": {"type": "number"}}, "syncType": {"type": "string", "description": "同步类型", "enum": ["inventory", "price", "all"]}}, "required": ["productIds"]}, "BatchUpdatePricesDto": {"type": "object", "properties": {"productIds": {"description": "产品ID列表", "example": [1, 2, 3], "type": "array", "items": {"type": "number"}}, "priceTarget": {"type": "string", "description": "调价目标字段", "enum": ["price", "discountPrice", "both"], "example": "price"}, "adjustmentType": {"type": "string", "description": "调价方式", "enum": ["fixed", "percentage", "amount", "cost_based"], "example": "percentage"}, "adjustmentValue": {"type": "number", "description": "调整值（根据adjustmentType的意义不同：固定值为具体价格，百分比为百分数如10表示10%，金额为增减数值，毛利率为百分数如30表示30%）", "example": 10}, "reason": {"type": "string", "description": "调价原因", "example": "市场价格调整"}}, "required": ["productIds", "priceTarget", "adjustmentType", "adjustmentValue"]}, "BatchUpdatePricesResultDto": {"type": "object", "properties": {"successCount": {"type": "number", "description": "成功更新的产品数量"}, "failureCount": {"type": "number", "description": "失败的产品数量"}, "totalCount": {"type": "number", "description": "总处理产品数量"}, "successDetails": {"description": "成功更新的产品详情", "type": "array", "items": {"type": "object"}}, "failureDetails": {"description": "失败的产品详情", "type": "array", "items": {"type": "object"}}, "isFullSuccess": {"type": "boolean", "description": "操作是否完全成功"}, "summary": {"type": "string", "description": "操作摘要信息"}}, "required": ["successCount", "failureCount", "totalCount", "successDetails", "failureDetails", "isFullSuccess", "summary"]}, "BatchUpdateSyncStrategyDto": {"type": "object", "properties": {"productIds": {"description": "产品ID列表", "type": "array", "items": {"type": "number"}}, "syncStrategy": {"type": "string", "description": "同步策略", "enum": ["FULL_SYNC", "STOCK_AND_COST_ONLY", "MANUAL_OVERRIDE", "DISABLED"]}}, "required": ["productIds", "syncStrategy"]}, "BatchUpdatePricesPreviewDto": {"type": "object", "properties": {"productIds": {"description": "产品ID列表", "example": [1, 2, 3], "type": "array", "items": {"type": "number"}}, "priceTarget": {"type": "string", "description": "调价目标字段", "enum": ["price", "discountPrice", "both"], "example": "price"}, "adjustmentType": {"type": "string", "description": "调价方式", "enum": ["fixed", "percentage", "amount", "cost_based"], "example": "percentage"}, "adjustmentValue": {"type": "number", "description": "调整值（根据adjustmentType的意义不同：固定值为具体价格，百分比为百分数如10表示10%，金额为增减数值，毛利率为百分数如30表示30%）", "example": 10}}, "required": ["productIds", "priceTarget", "adjustmentType", "adjustmentValue"]}, "BatchUpdatePricesPreviewResultDto": {"type": "object", "properties": {"previewDetails": {"description": "预览的产品价格变更详情", "type": "array", "items": {"type": "object"}}, "affectedCount": {"type": "number", "description": "受影响的产品总数"}, "summary": {"type": "string", "description": "预览摘要信息"}}, "required": ["previewDetails", "affectedCount", "summary"]}, "AllocateIpSegmentDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "providerId": {"type": "number", "description": "供应商ID"}, "cityName": {"type": "string", "description": "城市名称"}, "proxiesType": {"type": "string", "description": "代理类型"}, "requiredCount": {"type": "number", "description": "需要的IP数量"}, "cidr": {"type": "string", "description": "IP段CIDR"}}, "required": ["id", "providerId", "cityName", "proxiesType", "requiredCount"]}, "LocationFiltersDto": {"type": "object", "properties": {"countryCode": {"type": "string", "description": "国家代码"}, "cityName": {"type": "string", "description": "城市名称"}, "continentsId": {"type": "string", "description": "大洲ID"}}}, "AllocateIpSegmentsDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "supplierId": {"type": "number", "description": "供应商ID"}, "requestedIps": {"type": "number", "description": "请求分配的IP数量"}, "locationFilters": {"description": "位置过滤条件", "allOf": [{"$ref": "#/components/schemas/LocationFiltersDto"}]}}, "required": ["id", "supplierId", "requestedIps"]}, "IpAllocationResultDto": {"type": "object", "properties": {"allocatedSegments": {"description": "分配的IP段列表", "type": "array", "items": {"type": "string"}}, "totalAllocated": {"type": "number", "description": "总共分配的IP数量"}, "remainingRequest": {"type": "number", "description": "剩余未分配的IP数量"}}, "required": ["allocatedSegments", "totalAllocated", "remainingRequest"]}, "DeallocateIpSegmentsDto": {"type": "object", "properties": {"segmentIds": {"description": "IP段ID列表", "type": "array", "items": {"type": "string"}}}, "required": ["segmentIds"]}, "IpDeallocationResultDto": {"type": "object", "properties": {"deallocated": {"type": "number", "description": "回收的IP段数量"}, "returned": {"type": "number", "description": "返还的IP总数"}}, "required": ["deallocated", "returned"]}, "IpSegmentStatsDto": {"type": "object", "properties": {"totalSegments": {"type": "number", "description": "IP段总数"}, "activeSegments": {"type": "number", "description": "活跃IP段数量"}, "totalIps": {"type": "number", "description": "IP总数"}, "allocatedIps": {"type": "number", "description": "已分配IP数量"}, "availableIps": {"type": "number", "description": "可用IP数量"}, "utilizationRate": {"type": "number", "description": "利用率（百分比）"}}, "required": ["totalSegments", "activeSegments", "totalIps", "allocatedIps", "availableIps", "utilizationRate"]}, "UpdateIpSegmentStatusDto": {"type": "object", "properties": {"status": {"type": "string", "description": "新状态", "enum": ["active", "allocated", "reserved", "full", "maintenance", "inactive"]}}, "required": ["status"]}, "RebalanceIpSegmentsDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}}, "required": ["id"]}, "RebalanceResultDto": {"type": "object", "properties": {"rebalanced": {"type": "number", "description": "重新平衡的IP段数量"}, "optimized": {"type": "number", "description": "优化的IP段数量"}}, "required": ["rebalanced", "optimized"]}, "IPAvailabilityCountryItemDto": {"type": "object", "properties": {"city_id": {"type": "string", "description": "城市ID"}, "city_name": {"type": "string", "description": "城市名称"}, "continents_id": {"type": "string", "description": "大洲ID"}, "continents_name": {"type": "string", "description": "大洲名称"}, "country_code": {"type": "string", "description": "国家代码"}, "number": {"type": "number", "description": "库存数量"}, "is_hot": {"type": "boolean", "description": "是否热门"}}}, "OpenApiGetNodeInventoryDataDto": {"type": "object", "properties": {"country_list": {"description": "国家/地区列表及库存", "type": "array", "items": {"$ref": "#/components/schemas/IPAvailabilityCountryItemDto"}}, "proxies_format": {"type": "number", "description": "代理格式"}, "proxies_type": {"type": "string", "description": "代理类型", "example": "Premium (ISP) proxies"}, "purpose_web": {"type": "string", "description": "IP用途"}}}, "CalculatePriceQueryDto": {"type": "object", "properties": {"proxies_type": {"type": "string", "description": "代理类型", "example": "Shared (ISP) proxies"}, "proxies_format": {"type": "number", "description": "代理格式", "example": 2}, "time_period": {"type": "number", "description": "订购天数", "example": 30}, "currency": {"type": "string", "description": "货币", "example": "USD"}}, "required": ["proxies_type", "proxies_format", "time_period", "currency"]}, "PriceDiscountTierDto": {"type": "object", "properties": {"from": {"type": "number", "description": "起始数量"}, "to": {"type": "number", "description": "结束数量"}, "discount_percentage": {"type": "number", "description": "折扣百分比"}, "per_proxy_price": {"type": "number", "description": "每个代理的价格"}, "unit_price_mode": {"type": "boolean", "description": "单价模式"}, "country_name": {"type": "string", "description": "国家名称"}}, "required": ["from", "to", "discount_percentage", "per_proxy_price", "unit_price_mode", "country_name"]}, "CalculatePriceResponseDataDto": {"type": "object", "properties": {"proxies_type": {"type": "string", "description": "代理类型"}, "proxies_format": {"type": "number", "description": "代理格式"}, "time_period": {"type": "number", "description": "时间周期"}, "currency": {"type": "string", "description": "货币"}, "proxies_count_discount_tiers": {"description": "价格折扣层级", "type": "array", "items": {"$ref": "#/components/schemas/PriceDiscountTierDto"}}}, "required": ["proxies_type", "proxies_format", "time_period", "currency", "proxies_count_discount_tiers"]}, "PublicShopProductDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "productName": {"type": "string", "description": "产品名称"}, "productDesc": {"type": "string", "description": "产品描述"}, "productType": {"type": "string", "description": "产品类型"}, "price": {"type": "string", "description": "产品价格"}, "currency": {"type": "string", "description": "销售价格货币"}, "discountPrice": {"type": "string", "description": "折扣价格", "nullable": true}, "validityPeriod": {"type": "number", "description": "有效期(天)"}, "imageUrl": {"type": "string", "description": "产品图片URL"}, "salesCount": {"type": "number", "description": "销售数量"}, "sortOrder": {"type": "number", "description": "排序"}, "cityId": {"type": "number", "description": "标准化城市ID"}, "cityName": {"type": "string", "description": "城市名称"}, "countryName": {"type": "string", "description": "国家名称"}, "countryCode": {"type": "string", "description": "国家代码"}, "regionName": {"type": "string", "description": "地区名称"}, "inventoryStatus": {"type": "string", "description": "库存状态"}, "availableCount": {"type": "number", "description": "可用库存数量"}, "minQuantity": {"type": "number", "description": "最小购买数量"}, "maxQuantity": {"type": "number", "description": "最大购买数量"}, "status": {"type": "string", "description": "状态（0正常 1下架）"}, "createTime": {"type": "string", "description": "创建时间 (ISO 格式字符串)"}, "updateTime": {"type": "string", "description": "更新时间 (ISO 格式字符串)"}, "remark": {"type": "string", "description": "备注"}}, "required": ["id", "productName", "productDesc", "productType", "price", "validityPeriod", "imageUrl", "salesCount", "sortOrder", "status", "createTime", "updateTime", "remark"]}, "PriceRangeDto": {"type": "object", "properties": {"min": {"type": "number", "description": "最低价格"}, "max": {"type": "number", "description": "最高价格"}}, "required": ["min", "max"]}, "ProductStatsDto": {"type": "object", "properties": {"totalProducts": {"type": "number", "description": "产品总数"}, "totalLocations": {"type": "number", "description": "地区总数 (基于去重的城市)"}, "averagePrice": {"type": "number", "description": "平均价格"}, "currency": {"type": "string", "description": "货币单位"}, "priceRange": {"description": "价格范围", "allOf": [{"$ref": "#/components/schemas/PriceRangeDto"}]}}, "required": ["totalProducts", "totalLocations", "averagePrice", "currency", "priceRange"]}, "ResolvedProductDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "productName": {"type": "string", "description": "产品名称"}, "productDesc": {"type": "string", "description": "产品描述"}, "productType": {"type": "string", "description": "产品类型"}, "price": {"type": "string", "description": "产品价格"}, "currency": {"type": "string", "description": "销售价格货币"}, "discountPrice": {"type": "string", "description": "折扣价格"}, "costPrice": {"type": "number", "description": "成本价"}, "costPriceCurrency": {"type": "string", "description": "成本价币种"}, "isPriceManual": {"type": "boolean", "description": "是否手动设置价格"}, "validityPeriod": {"type": "number", "description": "有效期(天)"}, "imageUrl": {"type": "string", "description": "产品图片URL"}, "salesCount": {"type": "number", "description": "销售数量"}, "sortOrder": {"type": "number", "description": "排序"}, "standardLocationCityId": {"type": "number", "description": "标准化城市ID"}, "standardCityName": {"type": "string", "description": "标准化城市名称"}, "standardCountryName": {"type": "string", "description": "标准化国家名称"}, "standardCountryCode": {"type": "string", "description": "标准化国家代码"}, "standardRegionName": {"type": "string", "description": "标准化地区名称"}, "city": {"type": "string", "description": "兼容字段：城市"}, "country": {"type": "string", "description": "兼容字段：国家"}, "region": {"type": "string", "description": "兼容字段：区域"}, "status": {"type": "string", "description": "状态，0-上架, 1-下架"}, "delFlag": {"type": "string", "description": "删除标志，0-正常, 1-删除"}, "createTime": {"type": "string", "description": "创建时间 (ISO Date string)"}, "updateTime": {"type": "string", "description": "更新时间 (ISO Date string)"}, "remark": {"type": "string", "description": "备注"}, "createBy": {"type": "string", "description": "创建人"}, "updateBy": {"type": "string", "description": "更新人"}}, "required": ["id", "productName", "productType", "price", "currency", "status", "delFlag", "createTime", "updateTime"]}, "ProductResolveResultDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "product": {"description": "解析到的产品信息", "allOf": [{"$ref": "#/components/schemas/ResolvedProductDto"}]}, "error": {"type": "string", "description": "错误信息，如果操作失败"}}, "required": ["success"]}, "UpdateOrderStatusDto": {"type": "object", "properties": {}}, "RefundOrderDto": {"type": "object", "properties": {}}, "CreateCustomerDto": {"type": "object", "properties": {"email": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>"}, "nickname": {"type": "string", "description": "昵称", "example": "用户昵称"}, "password": {"type": "string", "description": "密码", "example": "password123"}, "mobile": {"type": "string", "description": "手机号", "example": "13800138000"}, "status": {"type": "string", "description": "状态 0:正常 1:停用", "example": "0"}, "remark": {"type": "string", "description": "备注", "example": "客户备注信息"}}, "required": ["email", "nickname", "password", "status"]}, "UpdateCustomerDto": {"type": "object", "properties": {}}, "UpdateCustomerStatusDto": {"type": "object", "properties": {}}, "TimelineEventDto": {"type": "object", "properties": {"id": {"type": "string", "description": "事件ID"}, "eventType": {"type": "string", "description": "事件类型", "enum": ["ADMIN_ACTION", "BALANCE_CHANGE", "ORDER_EVENT", "LOGIN_EVENT"]}, "title": {"type": "string", "description": "事件标题"}, "description": {"type": "string", "description": "事件描述"}, "author": {"type": "string", "description": "操作人员/来源"}, "timestamp": {"format": "date-time", "type": "string", "description": "事件时间"}, "relatedId": {"type": "string", "description": "关联的业务ID（可选）"}, "metadata": {"type": "object", "description": "额外元数据"}, "originalData": {"type": "object", "description": "原始数据（用于详情展示）"}}, "required": ["id", "eventType", "title", "description", "author", "timestamp", "relatedId", "metadata", "originalData"]}, "CustomerTimelineResponseDto": {"type": "object", "properties": {"list": {"description": "事件列表", "type": "array", "items": {"$ref": "#/components/schemas/TimelineEventDto"}}, "total": {"type": "number", "description": "总数量"}, "pageNum": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页数量"}, "pages": {"type": "number", "description": "总页数"}}, "required": ["list", "total", "pageNum", "pageSize", "pages"]}, "RechargeDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "充值金额", "example": 100}, "paymentMethod": {"type": "string", "description": "支付方式", "example": "alipay"}, "externalReference": {"type": "string", "description": "外部参考号"}, "remark": {"type": "string", "description": "备注"}}, "required": ["amount", "paymentMethod"]}, "CreateOrderDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID"}, "quantity": {"type": "number", "description": "购买数量"}, "paymentMethod": {"type": "string", "description": "支付方式 (1支付宝, 2微信, 4余额支付)", "enum": ["1", "2", "4"], "example": "1"}, "address": {"type": "string", "description": "收货地址"}, "contactName": {"type": "string", "description": "联系人"}, "contactMobile": {"type": "string", "description": "联系电话"}, "remark": {"type": "string", "description": "备注"}}, "required": ["id", "quantity", "paymentMethod"]}, "CreateStaticIpOrderDto": {"type": "object", "properties": {"id": {"type": "string", "description": "产品数据库主键ID"}, "quantity": {"type": "number", "description": "购买数量"}, "duration": {"type": "number", "description": "有效期（天）"}, "paymentMethod": {"type": "string", "description": "支付方式 (1支付宝, 2微信, 4余额支付)", "enum": ["1", "2", "4", "balance"], "example": "balance"}, "remark": {"type": "string", "description": "备注"}}, "required": ["id", "quantity", "duration", "paymentMethod"]}, "RenewStaticIpOrderDto": {"type": "object", "properties": {"instanceIds": {"description": "要续费的IP实例ID列表", "example": ["9", "10", "11"], "type": "array", "items": {"type": "string"}}}, "required": ["instanceIds"]}, "CreateRechargeOrderDto": {"type": "object", "properties": {"customerId": {"type": "number", "description": "客户ID"}, "amount": {"type": "number", "description": "充值金额"}, "bonusAmount": {"type": "number", "description": "赠送金额"}, "paymentMethod": {"type": "string", "description": "支付方式", "enum": ["alipay", "wechat_pay"]}, "orderType": {"type": "string", "description": "订单类型", "enum": ["RECHARGE"]}, "remark": {"type": "string", "description": "备注"}, "operatorType": {"type": "string", "description": "操作员类型", "enum": ["ADMIN", "CUSTOMER"]}}, "required": ["customerId", "amount", "paymentMethod", "orderType", "operatorType"]}, "ProviderPublicDto": {"type": "object", "properties": {"providerId": {"type": "number", "description": "供应商ID"}, "providerCode": {"type": "string", "description": "供应商代码"}, "providerName": {"type": "string", "description": "供应商名称"}, "providerDesc": {"type": "string", "description": "供应商描述", "nullable": true}, "providerType": {"type": "string", "description": "供应商类型"}}, "required": ["providerId", "providerCode", "providerName", "providerDesc", "providerType"]}, "ProductPublicDto": {"type": "object", "properties": {"id": {"type": "number", "description": "产品ID (主键ID)"}, "productCode": {"type": "string", "description": "产品编号/SKU", "nullable": true}, "productName": {"type": "string", "description": "产品名称"}, "productDesc": {"type": "string", "description": "产品描述", "nullable": true}, "productType": {"type": "string", "description": "产品类型"}, "price": {"type": "number", "description": "产品价格"}, "discountPrice": {"type": "number", "description": "折扣价格", "nullable": true}, "currency": {"type": "string", "description": "销售价格货币"}, "costPrice": {"type": "number", "description": "成本价（人民币）", "nullable": true}, "costPriceCurrency": {"type": "string", "description": "成本价币种"}, "flowAmount": {"type": "number", "description": "流量大小(MB)"}, "validityPeriod": {"type": "number", "description": "有效期(天)"}, "syncStatus": {"type": "string", "description": "同步状态"}, "lastSyncTime": {"format": "date-time", "type": "string", "description": "最后同步时间", "nullable": true}, "minQuantity": {"type": "number", "description": "最小购买数量"}, "maxQuantity": {"type": "number", "description": "最大购买数量", "nullable": true}, "inventoryStatus": {"type": "string", "description": "库存状态"}, "availableCount": {"type": "number", "description": "可用数量", "nullable": true}, "cityId": {"type": "number", "description": "标准化城市ID", "nullable": true}, "region": {"type": "string", "description": "地区名称", "nullable": true}, "country": {"type": "string", "description": "国家名称", "nullable": true}, "city": {"type": "string", "description": "城市名称", "nullable": true}, "countryCode": {"type": "string", "description": "国家代码", "nullable": true}, "syncStrategy": {"type": "string", "description": "同步策略"}, "isPriceManual": {"type": "boolean", "description": "是否手动价格"}, "imageUrl": {"type": "string", "description": "产品图片URL", "nullable": true}, "salesCount": {"type": "number", "description": "销售数量"}, "sortOrder": {"type": "number", "description": "排序"}, "externalProductId": {"type": "string", "description": "供应商处的产品标识/SKU", "nullable": true}, "productProxyCategory": {"type": "string", "description": "产品代理分类"}, "provider": {"description": "供应商信息", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ProviderPublicDto"}]}, "supplierName": {"type": "string", "description": "供应商名称", "nullable": true}}, "required": ["id", "productCode", "productName", "productDesc", "productType", "price", "discountPrice", "currency", "costPrice", "costPriceCurrency", "flowAmount", "validityPeriod", "syncStatus", "lastSyncTime", "minQuantity", "maxQuantity", "inventoryStatus", "availableCount", "cityId", "region", "country", "city", "countryCode", "syncStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imageUrl", "salesCount", "sortOrder", "externalProductId", "productProxyCategory", "provider", "supplierName"]}, "OrderPublicDto": {"type": "object", "properties": {"orderId": {"type": "string", "description": "订单ID"}, "customerId": {"type": "number", "description": "客户ID"}, "productId": {"type": "number", "description": "产品ID (充值订单时为NULL)", "nullable": true}, "quantity": {"type": "number", "description": "购买数量"}, "totalAmount": {"type": "number", "description": "订单总金额"}, "paidAmount": {"type": "number", "description": "实付金额"}, "orderStatus": {"type": "string", "description": "订单状态 (0:待支付, 1:已支付, 2:已取消, 3:已退款)", "enum": ["0", "1", "2", "3"]}, "paymentMethod": {"type": "string", "description": "支付方式 (1:支付宝, 2:微信, 4:余额支付)", "enum": ["1", "2", "4"], "nullable": true}, "paymentTime": {"format": "date-time", "type": "string", "description": "支付时间", "nullable": true}, "transactionId": {"type": "string", "description": "支付交易号", "nullable": true}, "paymentUrl": {"type": "string", "description": "支付URL/二维码链接", "nullable": true}, "address": {"type": "string", "description": "收货地址", "nullable": true}, "contactName": {"type": "string", "description": "联系人", "nullable": true}, "contactMobile": {"type": "string", "description": "联系电话", "nullable": true}, "orderType": {"type": "string", "description": "订单类型: new-新购, renewal-续费, RECHARGE-充值", "enum": ["new", "renewal", "RECHARGE"]}, "renewalInstanceIds": {"description": "续费的IP实例ID列表", "nullable": true, "type": "array", "items": {"type": "string"}}, "fulfillmentStatus": {"type": "string", "description": "产品开通状态", "default": "pending"}, "fulfillmentError": {"type": "string", "description": "产品开通失败原因", "nullable": true}, "fulfillmentTime": {"format": "date-time", "type": "string", "description": "产品开通完成时间", "nullable": true}, "fulfillmentRetryCount": {"type": "number", "description": "产品开通重试次数", "default": 0}, "product": {"description": "产品信息", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ProductPublicDto"}]}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productFlowAmount": {"type": "number", "description": "产品流量大小", "nullable": true}, "statusDescription": {"type": "string", "description": "订单状态描述"}, "paymentMethodName": {"type": "string", "description": "支付方式名称"}, "currency": {"type": "string", "description": "订单货币"}}, "required": ["orderId", "customerId", "productId", "quantity", "totalAmount", "paidAmount", "orderStatus", "paymentMethod", "paymentTime", "transactionId", "paymentUrl", "address", "contactName", "contactMobile", "orderType", "renewalInstanceIds", "fulfillmentStatus", "fulfillmentError", "fulfillmentTime", "fulfillmentRetryCount", "product", "productName", "productFlowAmount", "statusDescription", "paymentMethodName", "currency"]}, "UpdateWalletDto": {"type": "object", "properties": {"adjustmentType": {"type": "string", "description": "调整类型", "enum": ["add", "subtract", "set"]}, "amount": {"type": "number", "description": "金额"}, "reason": {"type": "string", "description": "原因"}}, "required": ["adjustmentType", "amount"]}, "AdjustBalanceDto": {"type": "object", "properties": {"adjustmentType": {"type": "string", "description": "调整类型", "enum": ["add", "subtract"]}, "balanceType": {"type": "string", "description": "余额类型", "enum": ["recharge", "bonus"]}, "amount": {"type": "number", "description": "调整金额"}, "reason": {"type": "string", "description": "调整原因"}}, "required": ["adjustmentType", "balanceType", "amount", "reason"]}, "FreezeBalanceDto": {"type": "object", "properties": {"action": {"type": "string", "description": "操作类型", "enum": ["freeze", "unfreeze"]}, "amount": {"type": "number", "description": "操作金额"}, "reason": {"type": "string", "description": "操作原因"}, "description": {"type": "string", "description": "详细说明"}}, "required": ["action", "amount", "reason", "description"]}, "CreatePaymentDto": {"type": "object", "properties": {"orderId": {"type": "string", "description": "订单ID"}, "paymentMethod": {"type": "string", "description": "支付方式 (1:支付宝, 2:微信, 4: 余额支付)", "enum": ["1", "2", "4"], "example": "1"}}, "required": ["orderId", "paymentMethod"]}, "PaymentNotifyDto": {"type": "object", "properties": {"pid": {"type": "string", "description": "商户ID"}, "trade_no": {"type": "string", "description": "交易号"}, "out_trade_no": {"type": "string", "description": "商户订单号"}, "type": {"type": "string", "description": "交易类型"}, "name": {"type": "string", "description": "商品名称"}, "money": {"type": "string", "description": "交易金额"}, "trade_status": {"type": "string", "description": "交易状态"}, "sign": {"type": "string", "description": "签名"}, "sign_type": {"type": "string", "description": "签名类型"}, "param": {"type": "string", "description": "业务扩展参数"}}, "required": ["pid", "trade_no", "out_trade_no", "type", "name", "money", "trade_status", "sign", "sign_type"]}, "TransactionDetailDto": {"type": "object", "properties": {"accountType": {"type": "string", "description": "账户类型", "enum": ["WALLET", "WALLET_RECHARGE", "WALLET_BONUS", "ALIPAY", "WECHAT_PAY", "POINTS", "INTERNAL_SETTLEMENT"]}, "accountId": {"type": "string", "description": "账户标识"}, "amount": {"type": "number", "description": "金额", "example": 100}, "direction": {"type": "string", "description": "资金流向", "enum": ["C", "D"]}}, "required": ["accountType", "amount", "direction"]}, "CreateOperationDto": {"type": "object", "properties": {"customerId": {"type": "number", "description": "客户ID"}, "operationType": {"type": "string", "description": "操作类型", "enum": ["1", "2", "3", "4", "5"]}, "orderId": {"type": "string", "description": "关联订单ID"}, "description": {"type": "string", "description": "操作描述"}, "transactions": {"description": "交易明细", "type": "array", "items": {"$ref": "#/components/schemas/TransactionDetailDto"}}, "operatorUserId": {"type": "number", "description": "操作员ID"}, "idempotencyKey": {"type": "string", "description": "幂等性键（防重复请求）"}}, "required": ["customerId", "operationType", "description", "transactions"]}, "CreateFaqDto": {"type": "object", "properties": {"question": {"type": "string", "description": "问题", "example": "如何查看我的流量使用情况？"}, "answer": {"type": "string", "description": "回答", "example": "您可以在个人中心的\"流量管理\"页面查看您的流量使用详情。"}, "category": {"type": "string", "description": "分类", "example": "账户相关"}, "sortOrder": {"type": "number", "description": "排序", "example": 0}, "isPublished": {"type": "boolean", "description": "是否发布", "example": true}}, "required": ["question", "answer"]}, "UpdateFaqDto": {"type": "object", "properties": {"isPublished": {"type": "boolean", "description": "是否发布", "example": true}, "status": {"type": "string", "description": "状态", "example": "0"}}, "required": ["isPublished", "status"]}, "ReplyFeedbackDto": {"type": "object", "properties": {"content": {"type": "string", "description": "回复内容", "example": "感谢您的反馈，我们已经解决了这个问题..."}, "status": {"type": "string", "description": "更新状态", "enum": ["0", "1", "2"]}, "reply": {"type": "string", "description": "回复内容", "example": "感谢您的反馈，我们已经解决了这个问题..."}, "replyBy": {"type": "string", "description": "回复者", "example": "管理员"}}, "required": ["content", "reply", "replyBy"]}, "CreateProviderDto": {"type": "object", "properties": {"providerCode": {"type": "string", "description": "供应商代码 (必须唯一)", "example": "ipnux_premium"}, "providerName": {"type": "string", "description": "供应商名称", "example": "IPNux 高级版"}, "providerDesc": {"type": "string", "description": "供应商描述", "example": "提供高质量住宅代理"}, "providerType": {"type": "string", "description": "供应商类型", "enum": ["API", "SELF_HOSTED", "MANUAL_IMPORT", "SCRIPT_BASED"], "example": "API"}, "configDetails": {"type": "object", "description": "供应商配置详情 (JSON对象)", "additionalProperties": true, "example": {"apiKey": "your_api_key", "region": "us"}}, "status": {"type": "string", "description": "状态 (0启用 1禁用)", "example": "0", "default": "0"}, "remark": {"type": "string", "description": "备注"}}, "required": ["providerCode", "providerName", "providerType"]}, "UpdateProviderDto": {"type": "object", "properties": {"providerCode": {"type": "string", "description": "供应商代码 (必须唯一)", "example": "ipnux_premium"}, "providerName": {"type": "string", "description": "供应商名称", "example": "IPNux 高级版"}, "providerDesc": {"type": "string", "description": "供应商描述", "example": "提供高质量住宅代理"}, "providerType": {"type": "string", "description": "供应商类型", "enum": ["API", "SELF_HOSTED", "MANUAL_IMPORT", "SCRIPT_BASED"], "example": "API"}, "configDetails": {"type": "object", "description": "供应商配置详情 (JSON对象)", "additionalProperties": true, "example": {"apiKey": "your_api_key", "region": "us"}}, "status": {"type": "string", "description": "状态 (0启用 1禁用)", "example": "0", "default": "0"}, "remark": {"type": "string", "description": "备注"}}}, "CreateJobDto": {"type": "object", "properties": {"jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "invokeTarget": {"type": "string", "description": "调用目标字符串"}, "cronExpression": {"type": "string", "description": "cron执行表达式"}, "misfirePolicy": {"type": "string", "description": "计划执行错误策略（1立即执行 2执行一次 3放弃执行）"}, "concurrent": {"type": "string", "description": "是否并发执行（0允许 1禁止）"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}, "remark": {"type": "string", "description": "备注信息"}}, "required": ["job<PERSON>ame", "jobGroup", "invoke<PERSON><PERSON><PERSON>", "cronExpression", "misfirePolicy", "concurrent", "status"]}, "ListJobDto": {"type": "object", "properties": {"jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}}, "required": ["job<PERSON>ame", "jobGroup", "status"]}, "ListJobLogDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}}, "required": ["pageNum", "pageSize", "job<PERSON>ame", "jobGroup", "status"]}, "ChangePasswordDto": {"type": "object", "properties": {"oldPassword": {"type": "string", "description": "旧密码", "example": "OldPassword123!"}, "newPassword": {"type": "string", "description": "新密码", "example": "NewPassword123!"}}, "required": ["oldPassword", "newPassword"]}, "ResetPasswordRequestDto": {"type": "object", "properties": {"email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}}, "required": ["email"]}, "ResetPasswordDto": {"type": "object", "properties": {"token": {"type": "string", "description": "重置令牌", "example": "abcdef123456"}, "newPassword": {"type": "string", "description": "新密码", "example": "NewPassword123!"}}, "required": ["token", "newPassword"]}, "SendCodeDto": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}}, "required": ["email"]}, "RegisterWithCodeDto": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}, "password": {"type": "string", "description": "用户密码", "example": "Password123!"}, "code": {"type": "string", "description": "验证码", "example": "123456"}}, "required": ["email", "password", "code"]}, "LoginWithCodeDto": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}, "code": {"type": "string", "description": "验证码", "example": "123456"}}, "required": ["email", "code"]}, "CreateFeedbackDto": {"type": "object", "properties": {"feedbackType": {"type": "string", "description": "反馈类型"}, "content": {"type": "string", "description": "反馈内容"}, "contactInfo": {"type": "string", "description": "联系方式"}}, "required": ["feedbackType", "content"]}, "PaymentMethodDto": {"type": "object", "properties": {"id": {"type": "string", "description": "支付方式ID"}, "name": {"type": "string", "description": "支付方式名称"}, "description": {"type": "string", "description": "支付方式描述"}, "icon": {"type": "string", "description": "支付方式图标"}, "isEnabled": {"type": "boolean", "description": "是否启用", "default": true}, "sortOrder": {"type": "number", "description": "排序顺序"}, "config": {"type": "object", "description": "支付方式配置"}}, "required": ["id", "name"]}, "ListLoginlogDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "ipaddr": {"type": "string"}, "userName": {"type": "string"}, "status": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "OnlineListDto": {"type": "object", "properties": {"pageNum": {"type": "number"}, "pageSize": {"type": "number"}, "ipaddr": {"type": "string"}, "userName": {"type": "string"}}}, "CreateOperlogDto": {"type": "object", "properties": {}}, "UpdateOperlogDto": {"type": "object", "properties": {}}}}}