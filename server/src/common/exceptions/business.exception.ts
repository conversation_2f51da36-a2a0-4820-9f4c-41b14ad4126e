import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCode, getErrorMessage } from '../constant/error-code.constant';

/**
 * 业务异常类
 * 用于处理应用程序中的业务逻辑错误，将业务错误码映射到HTTP状态码
 * 便于统一的错误处理和响应格式
 */

export class BusinessException extends HttpException {
  /**
   * 错误码
   * 保存业务错误代码，用于后续可能的错误处理和日志
   */
  private readonly errorCode: ErrorCode;

  /**
   * 构造函数
   * @param errorCode 业务错误码
   * @param message 自定义错误消息，如果提供则覆盖默认消息
   * @param data 附加数据，可用于提供更多错误上下文
   */
  constructor(errorCode: ErrorCode, message?: string, data?: any) {
    // 将业务错误码映射到HTTP状态码
    let httpStatus: HttpStatus;

    switch (errorCode) {
      case ErrorCode.BAD_REQUEST:
      case ErrorCode.VALIDATION_ERROR:
        httpStatus = HttpStatus.BAD_REQUEST;
        break;
      case ErrorCode.UNAUTHORIZED:
      case ErrorCode.TOKEN_EXPIRED:
      case ErrorCode.TOKEN_INVALID:
      case ErrorCode.INVALID_CREDENTIALS:
        httpStatus = HttpStatus.UNAUTHORIZED;
        break;
      case ErrorCode.FORBIDDEN:
      case ErrorCode.INSUFFICIENT_PERMISSIONS:
        httpStatus = HttpStatus.FORBIDDEN;
        break;
      case ErrorCode.NOT_FOUND:
      case ErrorCode.USER_NOT_FOUND:
        httpStatus = HttpStatus.NOT_FOUND;
        break;
      case ErrorCode.CONFLICT:
      case ErrorCode.USER_ALREADY_EXISTS:
      case ErrorCode.EMAIL_ALREADY_REGISTERED:
        httpStatus = HttpStatus.CONFLICT;
        break;
      default:
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    // 使用统一的响应格式构建异常信息
    // 构建响应对象，包含错误码、消息和可选的数据
    const response = {
      code: errorCode,
      message: getErrorMessage(errorCode, message),
      data: data || null,
    };

    super(response, httpStatus);

    this.errorCode = errorCode;
  }

  /**
   * 获取错误码
   * @returns 当前异常的业务错误码
   */
  getErrorCode(): ErrorCode {
    return this.errorCode;
  }
}
