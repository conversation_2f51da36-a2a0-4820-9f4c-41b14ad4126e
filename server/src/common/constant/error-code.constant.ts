export enum ErrorCode {
  // General errors
  SUCCESS = 0,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = -1,

  // Business logic errors
  VALIDATION_ERROR = 10001,
  USER_NOT_FOUND = 10002,
  USER_ALREADY_EXISTS = 10003,
  INVALID_CREDENTIALS = 10004,
  ACCOUNT_DISABLED = 10005,
  TOKEN_EXPIRED = 10006,
  TOKEN_INVALID = 10007,
  EMAIL_ALREADY_REGISTERED = 10008,
  INSUFFICIENT_PERMISSIONS = 10009,
  INVALID_OPERATION = 10010,

  // 验证码相关错误
  EMAIL_SEND_FAILED = 10012,
  CAPTCHA_MISSING = 10013, // 验证码缺失
  CAPTCHA_EXPIRED = 10014, // 验证码已过期
  CAPTCHA_INCORRECT = 10015, // 验证码错误
  CAPTCHA_GENERATION_ERROR = 10016, // 生成验证码错误

  // 配置相关错误
  CONFIG_BUILTIN_DELETE_ERROR = 20001, // 内置参数不能删除

  // 部门相关错误
  PARENT_DEPT_NOT_FOUND = 30001, // 父级部门不存在

  // 用户相关错误
  USER_STATUS_DISABLED = 40001, // 用户已被禁用
  USER_STATUS_DEACTIVATED = 40002, // 用户已被停用
  SYSTEM_USER_PASSWORD_RESET_FORBIDDEN = 40003, // 系统用户不能重置密码
  SYSTEM_ROLE_DISABLE_FORBIDDEN = 40004, // 系统角色不可停用
  PASSWORD_SAME_AS_OLD = 40005, // 新密码不能与旧密码相同
  OLD_PASSWORD_INCORRECT = 40006, // 旧密码错误

  // 文件相关错误
  FILE_SIZE_EXCEEDED = 50001, // 文件大小超过限制
  FILE_NOT_FOUND = 50002, // 文件不存在

  // 通用系统错误
  SYSTEM_ERROR = 90001, // 系统错误
  INVALID_PARAMS = 90002, // 参数错误

  // Wallet related errors
  WALLET_NOT_FOUND = 60001, // 余额不存在
  WALLET_FROZEN = 60002, // 余额已冻结
  INSUFFICIENT_BALANCE = 60003, // 余额不足
  INSUFFICIENT_RECHARGE_BALANCE = 60004, // 充值余额不足
  INSUFFICIENT_BONUS_BALANCE = 60005, // 赠送余额不足
  BALANCE_BELOW_FROZEN_AMOUNT = 60006, // 余额低于冻结金额
  INSUFFICIENT_AVAILABLE_BALANCE = 60007, // 可用余额不足
  INSUFFICIENT_FROZEN_AMOUNT = 60008, // 冻结金额不足

  // More specific error codes can be added here
}

export const ErrorMessages = {
  [ErrorCode.SUCCESS]: 'Success',
  [ErrorCode.BAD_REQUEST]: 'Bad request',
  [ErrorCode.UNAUTHORIZED]: 'Unauthorized',
  [ErrorCode.FORBIDDEN]: 'Forbidden',
  [ErrorCode.NOT_FOUND]: 'Not found',
  [ErrorCode.CONFLICT]: 'Conflict',
  [ErrorCode.INTERNAL_SERVER_ERROR]: 'Internal server error',

  [ErrorCode.VALIDATION_ERROR]: 'Validation error',
  [ErrorCode.USER_NOT_FOUND]: 'User not found',
  [ErrorCode.USER_ALREADY_EXISTS]: 'User already exists',
  [ErrorCode.INVALID_CREDENTIALS]: '帐号或密码错误',
  [ErrorCode.ACCOUNT_DISABLED]: 'Account is disabled',
  [ErrorCode.TOKEN_EXPIRED]: 'Token has expired',
  [ErrorCode.TOKEN_INVALID]: 'Invalid token',
  [ErrorCode.EMAIL_ALREADY_REGISTERED]: 'Email is already registered',
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions',
  [ErrorCode.INVALID_OPERATION]: 'Invalid operation',
  [ErrorCode.EMAIL_SEND_FAILED]: 'Failed to send email',

  // 验证码错误的中文消息
  [ErrorCode.CAPTCHA_MISSING]: '请输入验证码',
  [ErrorCode.CAPTCHA_EXPIRED]: '验证码已过期',
  [ErrorCode.CAPTCHA_INCORRECT]: '验证码错误',
  [ErrorCode.CAPTCHA_GENERATION_ERROR]: '生成验证码错误，请重试',

  // 配置相关错误
  [ErrorCode.CONFIG_BUILTIN_DELETE_ERROR]: '内置参数不能删除',

  // 部门相关错误
  [ErrorCode.PARENT_DEPT_NOT_FOUND]: '父级部门不存在',

  // 用户相关错误
  [ErrorCode.USER_STATUS_DISABLED]: '您已被禁用，如需正常使用请联系管理员',
  [ErrorCode.USER_STATUS_DEACTIVATED]: '您已被停用，如需正常使用请联系管理员',
  [ErrorCode.SYSTEM_USER_PASSWORD_RESET_FORBIDDEN]: '系统用户不能重置密码',
  [ErrorCode.SYSTEM_ROLE_DISABLE_FORBIDDEN]: '系统角色不可停用',
  [ErrorCode.PASSWORD_SAME_AS_OLD]: '新密码不能与旧密码相同',
  [ErrorCode.OLD_PASSWORD_INCORRECT]: '修改密码失败，旧密码错误',

  // 文件相关错误
  [ErrorCode.FILE_SIZE_EXCEEDED]: '文件大小不能超过限制',
  [ErrorCode.FILE_NOT_FOUND]: '文件不存在',

  // 通用系统错误
  [ErrorCode.SYSTEM_ERROR]: '系统错误',
  [ErrorCode.INVALID_PARAMS]: '参数错误',

  // 余额相关错误
  [ErrorCode.WALLET_NOT_FOUND]: '余额不存在',
  [ErrorCode.WALLET_FROZEN]: '余额已冻结',
  [ErrorCode.INSUFFICIENT_BALANCE]: '余额不足',
  [ErrorCode.INSUFFICIENT_RECHARGE_BALANCE]: '充值余额不足',
  [ErrorCode.INSUFFICIENT_BONUS_BALANCE]: '赠送余额不足',
  [ErrorCode.BALANCE_BELOW_FROZEN_AMOUNT]: '操作将导致总余额低于冻结金额，无法执行',
  [ErrorCode.INSUFFICIENT_AVAILABLE_BALANCE]: '可用余额不足，无法冻结该金额',
  [ErrorCode.INSUFFICIENT_FROZEN_AMOUNT]: '冻结金额不足，无法解冻该金额',
};

// Helper to get the message for a given error code
export const getErrorMessage = (code: ErrorCode, customMessage?: string): string => {
  return customMessage || ErrorMessages[code] || 'Unknown error';
};
