import { Injectable, Logger } from '@nestjs/common';
import { InventoryService } from '../../../shop/inventory/inventory.service';
import { ConfigService } from '../../config/config.service';
import { ShopProviderIpInventory } from '../../../shop/provider/entities/provider-ip-inventory.entity';

export interface PricingCalculationResult {
  supplierCostPriceUSD: number;
  supplierCostPriceCNY: number;
  finalSellingPrice: number;
  finalSellingCurrency: string;
  exchangeRate: number;
}

/**
 * 产品定价服务
 * 负责：
 * - 供应商成本价获取和计算
 * - 汇率获取和转换
 * - 销售价格计算
 * - 定价策略应用
 */
@Injectable()
export class ProductPricingService {
  private readonly logger = new Logger(ProductPricingService.name);

  constructor(
    private readonly inventoryService: InventoryService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 计算产品价格
   */
  async calculateProductPricing(inventoryItem: ShopProviderIpInventory): Promise<PricingCalculationResult> {
    // 获取供应商成本价（USD）
    const supplierCostPriceUSD = await this.getSupplierCostPrice(inventoryItem);

    // 获取汇率并转换成本价为CNY
    const exchangeRate = await this.getExchangeRate();
    const supplierCostPriceCNY = Number((supplierCostPriceUSD * exchangeRate).toFixed(2));

    // 获取加价配置并计算销售价
    const markupPercentage = await this.getConfigValue('pricing.default_markup_percentage', 2.0);
    const finalSellingPrice = Number((supplierCostPriceCNY * (1 + markupPercentage)).toFixed(2));
    const finalSellingCurrency = 'CNY';

    return {
      supplierCostPriceUSD,
      supplierCostPriceCNY,
      finalSellingPrice,
      finalSellingCurrency,
      exchangeRate,
    };
  }

  /**
   * 获取供应商成本价（USD）
   */
  private async getSupplierCostPrice(inventoryItem: ShopProviderIpInventory): Promise<number> {
    try {
      const priceData = await this.inventoryService.calculatePrice({
        proxies_type: inventoryItem.proxies_type,
        proxies_format: inventoryItem.proxies_format || 2,
        time_period: 30,
        currency: 'USD',
      });

      if (priceData?.proxies_count_discount_tiers) {
        const countryTier = priceData.proxies_count_discount_tiers.find(
          (tier) =>
            tier.country_name?.toLowerCase() === inventoryItem.country_code?.toLowerCase() ||
            tier.country_name === inventoryItem.city_name,
        );

        if (countryTier) {
          return countryTier.per_proxy_price || 0;
        } else {
          const defaultTier = priceData.proxies_count_discount_tiers[0];
          if (defaultTier) {
            return defaultTier.per_proxy_price || 0;
          }
        }
      }
    } catch (error) {
      this.logger.warn(`获取产品成本价失败，使用库存单价作为备用: ${error.message}`);
      return inventoryItem.unit_price || 0;
    }

    return 0;
  }

  /**
   * 获取汇率
   */
  private async getExchangeRate(): Promise<number> {
    const useManualRate = (await this.getConfigValue('pricing.use_manual_exchange_rate', 'false')) === 'true';
    
    if (useManualRate) {
      return await this.getConfigValue('pricing.manual_usd_to_cny_exchange_rate', 7.1);
    } else {
      return await this.inventoryService.getUsdToCnyRate();
    }
  }

  /**
   * 重新计算产品价格（基于现有成本价）
   */
  async recalculateSellingPrice(costPriceCNY: number): Promise<number> {
    const markupPercentage = await this.getConfigValue('pricing.default_markup_percentage', 2.0);
    return Number((costPriceCNY * (1 + markupPercentage)).toFixed(2));
  }

  /**
   * 转换USD成本价为CNY
   */
  async convertUsdToCny(usdPrice: number): Promise<number> {
    const exchangeRate = await this.getExchangeRate();
    return Number((usdPrice * exchangeRate).toFixed(2));
  }

  /**
   * 获取配置值（带默认值）
   */
  private async getConfigValue(key: string, defaultValue: any): Promise<any> {
    try {
      const value = await this.configService.getConfigValue(key);
      if (value === null || value === undefined) {
        return defaultValue;
      }
      if (typeof defaultValue === 'number') {
        return Number(value);
      }
      return value;
    } catch (error) {
      this.logger.warn(`获取配置 ${key} 失败，使用默认值: ${defaultValue}`);
      return defaultValue;
    }
  }
}