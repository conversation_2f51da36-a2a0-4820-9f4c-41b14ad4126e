import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product, SyncStrategy } from '../../../shop/products/entities/product.entity';
import { Provider } from '../../provider/entities/provider.entity';
import { ProductCodeService } from './product-code.service';
import { ProxyTypeService } from '../../../shop/products/services/proxy-type.service';
import { LocationCityService } from '../../location/services/location-city.service';
import { SyncHistoryService } from '../../sync-history/sync-history.service';
import { SyncStrategyService } from './sync-strategy.service';
import { SupplierProductData } from '../interfaces/sync-strategy.interface';

export interface ProductCreationData {
  productCode: string;
  productName: string;
  productDesc: string;
  productType: string;
  price: number;
  currency: string;
  costPrice: number;
  costPriceCurrency: string;
  isPriceManual: boolean;
  flowAmount: number;
  validityPeriod: number;
  providerId: number;
  externalProductId: string;
  productProxyCategory: string;
  availableCount: number;
  inventoryStatus: string;
  standardCityName: string;
  standardCountryName: string | null;
  standardCountryCode: string | null;
  standardRegionName: string | null;
  cityId: number | null;
  productConfigDetails: Record<string, any>;
  status: string;
  delFlag: string;
}

/**
 * 产品生命周期服务
 * 负责：
 * - 产品的创建和更新
 * - 产品数据的验证和格式化
 * - 产品生命周期的管理
 */
@Injectable()
export class ProductLifecycleService {
  private readonly logger = new Logger(ProductLifecycleService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly productCodeService: ProductCodeService,
    private readonly proxyTypeService: ProxyTypeService,
    private readonly locationCityService: LocationCityService,
    private readonly syncHistoryService: SyncHistoryService,
    private readonly syncStrategyService: SyncStrategyService,
  ) {}

  /**
   * 创建或更新产品
   */
  async createOrUpdateProduct(
    provider: Provider,
    productData: ProductCreationData,
    syncId?: number,
    overrideStrategy?: SyncStrategy,
  ): Promise<{ product: Product; action: 'created' | 'updated' }> {
    // 查找现有产品
    let product = await this.productRepository.findOne({
      where: {
        providerId: provider.providerId,
        externalProductId: productData.externalProductId,
        delFlag: '0',
      },
    });

    const oldPrice = product?.price || null;
    const oldStock = product?.availableCount || null;
    let action: 'created' | 'updated' = 'created';

    if (product) {
      // 更新现有产品
      action = 'updated';
      await this.updateExistingProduct(product, productData, overrideStrategy);
    } else {
      // 创建新产品
      product = await this.createNewProduct(productData);
    }

    await this.productRepository.save(product);

    // 记录产品同步详情
    if (syncId) {
      await this.syncHistoryService.addProductDetail({
        syncId,
        id: product.id,
        action,
        oldPrice,
        newPrice: productData.price,
        oldStock,
        newStock: productData.availableCount,
        errorMessage: null,
      });
    }

    return { product, action };
  }

  /**
   * 创建新产品
   */
  private async createNewProduct(productData: ProductCreationData): Promise<Product> {
    const product = this.productRepository.create({
      ...productData,
      syncStrategy: SyncStrategy.STOCK_AND_COST_ONLY,
    });

    // 地理位置一致性保障
    if (product.cityId) {
      const hierarchy = await this.locationCityService.findLocationHierarchyById(product.cityId);
      if (hierarchy) {
        product.countryId = hierarchy.countryId;
        product.regionId = hierarchy.regionId;
      }
    }

    return product;
  }

  /**
   * 更新现有产品
   */
  private async updateExistingProduct(
    product: Product,
    productData: ProductCreationData,
    overrideStrategy?: SyncStrategy,
  ): Promise<void> {
    // 获取产品的同步策略实现
    const effectiveStrategy = overrideStrategy || product.syncStrategy;
    const strategy = this.syncStrategyService.getStrategy(effectiveStrategy);

    // 转换为供应商产品数据格式
    const supplierData: SupplierProductData = {
      productName: productData.productName,
      productDesc: productData.productDesc,
      price: productData.price,
      discountPrice: undefined,
      currency: productData.currency,
      costPrice: productData.costPrice,
      costPriceCurrency: productData.costPriceCurrency,
      availableCount: productData.availableCount,
      inventoryStatus: productData.inventoryStatus,
      cityId: productData.cityId,
      cityName: productData.standardCityName,
      countryName: productData.standardCountryName,
      countryCode: productData.standardCountryCode,
      regionName: productData.standardRegionName,
      productConfigDetails: productData.productConfigDetails,
      externalProductId: productData.externalProductId,
      lastSyncTime: new Date(),
    };

    // 使用策略处理同步
    const syncResult = await strategy.sync(supplierData, product);

    if (!syncResult.success) {
      this.logger.warn(`产品 ${product.id} 同步失败: ${syncResult.error || syncResult.message}`);
      throw new Error(syncResult.error || syncResult.message || '产品同步失败');
    }

    // 更新同步时间
    product.updateTime = new Date();

    this.logger.debug(`产品 ${product.id} 使用 ${product.syncStrategy} 策略进行同步`);
  }

  /**
   * 生成产品基础数据
   */
  async generateProductData(
    provider: Provider,
    inventoryItem: any,
    locationData: any,
    pricingData: any,
    supplierLocation: any,
  ): Promise<ProductCreationData> {
    // 生成产品代码和名称
    const productCode = await this.productCodeService.generateProductCode(
      provider.providerCode,
      inventoryItem.proxies_type,
      locationData.standardCountryCode || inventoryItem.country_code,
      locationData.standardLocationCityId ? String(locationData.standardLocationCityId) : 
        inventoryItem.city_id && inventoryItem.city_id !== 'undefined' ? String(inventoryItem.city_id) : undefined,
      inventoryItem.proxies_format || 2,
      inventoryItem.purpose_web,
    );

    const productName = await this.generateProductName(
      inventoryItem.proxies_type,
      locationData.standardCountryName,
      locationData.standardCityName,
    );

    // 构建产品唯一标识
    const proxyTypeProcessed = inventoryItem.proxies_type.replace(/\s*\([^)]*\)\s*/g, '').replace(/\s+/g, '');
    const externalProductId = `${provider.providerId}-${inventoryItem.city_id || 'unknown'}-${proxyTypeProcessed}-${inventoryItem.proxies_format || 2}-${inventoryItem.purpose_web}`;

    return {
      productCode,
      productName,
      productDesc: `${locationData.standardCityName || inventoryItem.city_name || '未知'}地区的${inventoryItem.proxies_type}代理IP`,
      productType: '1',
      price: pricingData.finalSellingPrice,
      currency: pricingData.finalSellingCurrency,
      costPrice: pricingData.supplierCostPriceCNY,
      costPriceCurrency: 'CNY',
      isPriceManual: false,
      flowAmount: -1,
      validityPeriod: 30,
      providerId: provider.providerId,
      externalProductId,
      productProxyCategory: await this.proxyTypeService.ensureProxyTypeExists(inventoryItem.proxies_type),
      availableCount: inventoryItem.stock_count,
      inventoryStatus: this.getInventoryStatus(inventoryItem.stock_count),
      standardCityName: locationData.standardCityName || inventoryItem.city_name,
      standardCountryName: locationData.standardCountryName || inventoryItem.country_code,
      standardCountryCode: locationData.standardCountryCode || inventoryItem.country_code,
      standardRegionName: locationData.standardRegionName || inventoryItem.continents_name,
      cityId: locationData.standardLocationCityId,
      productConfigDetails: {
        inventoryId: inventoryItem.inventory_id,
        proxyType: inventoryItem.proxies_type,
        proxiesFormat: inventoryItem.proxies_format,
        purposeWeb: inventoryItem.purpose_web,
        rawSnapshot: inventoryItem.raw_data_snapshot,
        supplierLocation: supplierLocation,
        lastSyncTime: new Date(),
      },
      status: '0',
      delFlag: '0',
    };
  }

  /**
   * 根据库存数量获取库存状态
   */
  private getInventoryStatus(stockCount: number): string {
    if (stockCount === 0) {
      return 'out_of_stock';
    } else if (stockCount < 10) {
      return 'low';
    }
    return 'available';
  }

  /**
   * 生成产品名称
   */
  private async generateProductName(
    proxyType: string,
    countryName: string | null,
    cityName: string | null,
  ): Promise<string> {
    if (cityName && cityName.trim()) {
      return cityName.trim();
    }
    
    if (countryName && countryName.trim()) {
      return countryName.trim();
    }

    return '未知地区';
  }
}