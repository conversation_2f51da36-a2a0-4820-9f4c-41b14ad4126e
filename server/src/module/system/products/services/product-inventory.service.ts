import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../../../shop/products/entities/product.entity';
import { InventoryService } from '../../../shop/inventory/inventory.service';
import { SyncHistoryService } from '../../sync-history/sync-history.service';

export interface InventoryUpdateResult {
  success: boolean;
  oldStock: number | null;
  newStock: number | null;
  inventoryStatus: string;
}

/**
 * 产品库存服务
 * 负责：
 * - 产品库存的同步和更新
 * - 库存状态的计算
 * - 库存变化的记录
 */
@Injectable()
export class ProductInventoryService {
  private readonly logger = new Logger(ProductInventoryService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly inventoryService: InventoryService,
    private readonly syncHistoryService: SyncHistoryService,
  ) {}

  /**
   * 同步产品库存
   */
  async syncProductInventory(product: Product, syncId?: number): Promise<InventoryUpdateResult> {
    const oldStock = product.availableCount;
    const proxyType = product.productConfigDetails?.proxyType || 'Shared (ISP) proxies';
    const cityName = product.productConfigDetails?.supplierLocation?.cityName || product.productConfigDetails?.cityName;
    const purposeWeb = product.productConfigDetails?.purposeWeb || 'Any';

    if (!cityName) {
      this.logger.warn(`产品 ${product.id} 缺少城市名称信息，跳过库存同步`);
      return {
        success: false,
        oldStock,
        newStock: null,
        inventoryStatus: product.inventoryStatus,
      };
    }

    try {
      const inventoryData = await this.inventoryService.getNodeInventory({
        proxies_type: proxyType,
        city_name: cityName,
        proxies_format: 2,
        purpose_web: purposeWeb,
        continents_id: null,
        country_code: null,
      });

      const cityData = inventoryData.country_list?.find((country) => country.city_name === cityName);

      if (cityData && cityData.number !== undefined) {
        const stock = cityData.number;
        const inventoryStatus = this.getInventoryStatus(stock);

        product.inventoryStatus = inventoryStatus;
        product.availableCount = stock;
        product.updateTime = new Date();

        await this.productRepository.save(product);

        // 记录库存同步详情
        if (syncId && oldStock !== stock) {
          await this.syncHistoryService.addProductDetail({
            syncId,
            id: product.id,
            action: 'updated',
            oldPrice: null,
            newPrice: null,
            oldStock,
            newStock: stock,
            errorMessage: null,
          });
        }

        return {
          success: true,
          oldStock,
          newStock: stock,
          inventoryStatus,
        };
      } else {
        this.logger.warn(`未找到产品 ${product.id} 城市 ${cityName} 的库存数据`);
        return {
          success: false,
          oldStock,
          newStock: null,
          inventoryStatus: product.inventoryStatus,
        };
      }
    } catch (error) {
      this.logger.error(`同步产品库存失败: ${product.productName}`, error);

      // 记录库存同步失败详情
      if (syncId) {
        await this.syncHistoryService.addProductDetail({
          syncId,
          id: product.id,
          action: 'failed',
          oldPrice: null,
          newPrice: null,
          oldStock,
          newStock: null,
          errorMessage: error.message,
        });
      }

      return {
        success: false,
        oldStock,
        newStock: null,
        inventoryStatus: product.inventoryStatus,
      };
    }
  }

  /**
   * 根据库存数量获取库存状态
   */
  getInventoryStatus(stockCount: number): string {
    if (stockCount === 0) {
      return 'out_of_stock';
    } else if (stockCount < 10) {
      return 'low';
    }
    return 'available';
  }

  /**
   * 批量更新产品库存为零
   */
  async batchUpdateZeroStock(
    productIds: number[],
    syncId?: number,
  ): Promise<{ updatedCount: number; errors: string[] }> {
    let updatedCount = 0;
    const errors: string[] = [];
    const batchSize = 50;

    for (let i = 0; i < productIds.length; i += batchSize) {
      const batch = productIds.slice(i, i + batchSize);

      try {
        // 获取批次产品的当前库存信息（用于记录历史）
        const products = await this.productRepository.findByIds(batch);
        
        // 批量更新
        await this.productRepository
          .createQueryBuilder()
          .update()
          .set({
            availableCount: 0,
            inventoryStatus: 'out_of_stock',
            updateTime: new Date(),
          })
          .whereInIds(batch)
          .execute();

        updatedCount += batch.length;

        // 记录同步详情
        if (syncId) {
          for (const product of products) {
            try {
              await this.syncHistoryService.addProductDetail({
                syncId,
                id: product.id,
                action: 'updated',
                oldPrice: null,
                newPrice: null,
                oldStock: product.availableCount,
                newStock: 0,
                errorMessage: null,
              });
            } catch (detailError) {
              this.logger.warn(`记录产品 ${product.id} 同步详情失败: ${detailError.message}`);
            }
          }
        }

        this.logger.log(`批量更新了 ${batch.length} 个产品的库存为0`);
      } catch (batchError) {
        this.logger.error(`批量更新产品库存失败: ${batchError.message}`);
        errors.push(`Batch update failed: ${batchError.message}`);
      }
    }

    return { updatedCount, errors };
  }

  /**
   * 设置零库存给未返回的产品
   */
  async setZeroStockForMissingProducts(
    providerId: number,
    proxyType: string,
    returnedProductIds: string[],
    syncId?: number,
    proxiesFormat?: number,
    purposeWeb?: string,
  ): Promise<{ updatedCount: number; errors: string[] }> {
    this.logger.log(`开始处理供应商 ${providerId} 的零库存设置，同步批次参数: ${proxyType}, proxiesFormat: ${proxiesFormat}, purposeWeb: ${purposeWeb}`);

    try {
      // 生成当前同步批次的 externalProductId 前缀模式
      const proxyTypeProcessed = proxyType.replace(/\s*\([^)]*\)\s*/g, '').replace(/\s+/g, '');
      const formatPart = proxiesFormat ? `-${proxiesFormat}` : '-%';
      const purposePart = purposeWeb ? `-${purposeWeb}` : '-%';
      const externalIdPattern = `${providerId}-%${proxyTypeProcessed}${formatPart}${purposePart}`;

      // 查询数据库中该供应商下所有匹配当前同步批次模式的产品
      const existingProducts = await this.productRepository
        .createQueryBuilder('product')
        .where('product.providerId = :providerId', { providerId })
        .andWhere('product.delFlag = :delFlag', { delFlag: '0' })
        .andWhere('product.status = :status', { status: '0' })
        .andWhere('product.externalProductId LIKE :pattern', { pattern: externalIdPattern })
        .getMany();

      this.logger.log(`找到 ${existingProducts.length} 个匹配分类的现有产品`);

      // 筛选出需要设置为零库存的产品
      const productsToZeroStock = existingProducts.filter(
        (product) => !returnedProductIds.includes(product.externalProductId),
      );

      this.logger.log(`需要设置零库存的产品数量: ${productsToZeroStock.length}`);

      if (productsToZeroStock.length > 0) {
        const productIds = productsToZeroStock.map((p) => p.id);
        return await this.batchUpdateZeroStock(productIds, syncId);
      }

      return { updatedCount: 0, errors: [] };
    } catch (error) {
      this.logger.error(`零库存处理失败: ${error.message}`);
      throw error;
    }
  }
}