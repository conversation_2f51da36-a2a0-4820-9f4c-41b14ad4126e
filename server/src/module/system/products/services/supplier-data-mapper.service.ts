import { Injectable, Logger } from '@nestjs/common';
import { SupplierLocationMappingService } from '../../location/supplier-location-mapping.service';
import { LocationCityService } from '../../location/services/location-city.service';
import { LocationParserFactoryService } from './location-parser-factory.service';
import { Provider } from '../../provider/entities/provider.entity';
import { ShopProviderIpInventory } from '../../../shop/provider/entities/provider-ip-inventory.entity';
import { SupplierLocationUtils } from '../types/supplier-location.types';

/**
 * 供应商数据映射服务
 * 负责：
 * - 供应商原始数据到标准格式的映射
 * - 地理位置标准化处理
 * - 供应商特定的数据格式转换
 */
@Injectable()
export class SupplierDataMapperService {
  private readonly logger = new Logger(SupplierDataMapperService.name);

  constructor(
    private readonly supplierLocationMappingService: SupplierLocationMappingService,
    private readonly locationCityService: LocationCityService,
    private readonly locationParserFactory: LocationParserFactoryService,
  ) {}

  /**
   * 映射地理位置信息
   */
  async mapLocationData(
    provider: Provider,
    inventoryItem: ShopProviderIpInventory,
  ): Promise<{
    standardLocationCityId: number | null;
    standardCityName: string;
    standardCountryName: string | null;
    standardCountryCode: string | null;
    standardRegionName: string | null;
  }> {
    const supplierCityId = String(inventoryItem.city_id);
    const supplierCityName = inventoryItem.city_name;
    const supplierCountryCode = inventoryItem.country_code;
    const supplierContinentName = inventoryItem.continents_name;

    let standardLocationCityId: number | null = null;
    let standardCityName: string = supplierCityName || '';
    let standardCountryName: string = null;
    let standardCountryCode: string = supplierCountryCode || null;
    let standardRegionName: string = supplierContinentName || null;

    if (provider.providerId && supplierCityId) {
      const mapping = await this.supplierLocationMappingService.findByProviderAndNativeCityId(
        provider.providerId,
        supplierCityId,
      );

      if (mapping && mapping.standardCityId) {
        standardLocationCityId = mapping.standardCityId;

        try {
          const standardCity = await this.locationCityService.findOne(standardLocationCityId);
          if (standardCity) {
            standardCityName = standardCity.cityName;
            if (standardCity.country) {
              standardCountryName = standardCity.country.countryName;
              standardCountryCode = standardCity.country.countryCode;
              if (standardCity.country.region) {
                standardRegionName = standardCity.country.region.regionName;
              }
            }
          }
        } catch (error) {
          this.logger.warn(`获取标准化城市信息失败: ${error.message}`);
        }
      } else {
        this.logger.warn(`未找到供应商 ${provider.providerId} 城市 ${supplierCityId} 的映射，尝试按城市名称匹配`);

        if (supplierCityName) {
          try {
            const mappingByName = await this.supplierLocationMappingService.findByProviderAndCityName(
              provider.providerId,
              supplierCityName,
            );
            if (mappingByName && mappingByName.standardCityId) {
              standardLocationCityId = mappingByName.standardCityId;
              this.logger.log(`通过城市名称 "${supplierCityName}" 找到映射，标准城市ID: ${standardLocationCityId}`);

              try {
                const standardCity = await this.locationCityService.findOne(standardLocationCityId);
                if (standardCity) {
                  standardCityName = standardCity.cityName;
                  if (standardCity.country) {
                    standardCountryName = standardCity.country.countryName;
                    standardCountryCode = standardCity.country.countryCode;
                    if (standardCity.country.region) {
                      standardRegionName = standardCity.country.region.regionName;
                    }
                  }
                }
              } catch (error) {
                this.logger.warn(`获取标准化城市信息失败: ${error.message}`);
              }
            } else {
              this.logger.warn(`未找到供应商 ${provider.providerId} 城市名称 "${supplierCityName}" 的映射，使用原始数据`);
            }
          } catch (error) {
            this.logger.warn(`按城市名称查找映射失败: ${error.message}`);
          }
        }
      }
    }

    return {
      standardLocationCityId,
      standardCityName,
      standardCountryName,
      standardCountryCode,
      standardRegionName,
    };
  }

  /**
   * 生成供应商地理位置信息
   */
  generateSupplierLocation(inventoryItem: ShopProviderIpInventory, provider: Provider) {
    try {
      const providerType = this.getProviderType(provider);
      const parseResult = this.locationParserFactory.parseLocationSmart(inventoryItem, providerType);

      if (parseResult.status === 'success' && parseResult.location) {
        return SupplierLocationUtils.migrateLegacyToExtended(
          {
            cityId: parseResult.location.cityId,
            cityName: parseResult.location.cityName,
            countryCode: parseResult.location.countryCode,
            continentName: parseResult.location.continentName,
          },
          parseResult.location.providerType,
        );
      } else {
        this.logger.error(`地理位置解析失败，回退到旧方式: ${parseResult.error}`, {
          providerType,
          inventoryId: inventoryItem.inventory_id,
          cityId: inventoryItem.city_id,
          cityName: inventoryItem.city_name,
          countryCode: inventoryItem.country_code,
        });

        return SupplierLocationUtils.migrateLegacyToExtended(
          {
            cityId: String(inventoryItem.city_id || ''),
            cityName: inventoryItem.city_name || '',
            countryCode: inventoryItem.country_code || '',
            continentName: inventoryItem.continents_name || '',
          },
          providerType,
        );
      }
    } catch (error) {
      this.logger.error(`生成供应商地理位置信息失败: ${error.message}`, error);
      return SupplierLocationUtils.createDefault('UNKNOWN');
    }
  }

  /**
   * 获取供应商类型标识
   */
  private getProviderType(provider: Provider): string {
    const providerIdMapping: Record<number, string> = {
      1: 'IPNUX',
    };

    if (providerIdMapping[provider.providerId]) {
      return providerIdMapping[provider.providerId];
    }

    const providerName = provider.providerName?.toLowerCase() || '';
    const providerCode = provider.providerCode?.toLowerCase() || '';

    const ipnuxPatterns = ['ipnux', 'ip-nux', 'ip_nux'];
    if (ipnuxPatterns.some((pattern) => providerName.includes(pattern) || providerCode.includes(pattern))) {
      return 'IPNUX';
    }

    this.logger.warn(
      `未识别的供应商类型: ID=${provider.providerId}, Name=${provider.providerName}, Code=${provider.providerCode}`,
    );

    return 'UNKNOWN';
  }
}