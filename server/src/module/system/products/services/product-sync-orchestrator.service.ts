import { Task } from '@/common/decorators/task.decorator';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { InventoryService } from '../../../shop/inventory/inventory.service';
import { Product, SyncStrategy } from '../../../shop/products/entities/product.entity';
import { ShopProviderIpInventory } from '../../../shop/provider/entities/provider-ip-inventory.entity';
import { Provider } from '../../provider/entities/provider.entity';
import { SyncHistoryService } from '../../sync-history/sync-history.service';
import { SupplierDataMapperService } from './supplier-data-mapper.service';
import { ProductPricingService } from './product-pricing.service';
import { ProductCacheService } from './product-cache.service';
import { SyncTaskStatusService } from './sync-task-status.service';
import { ProductInventoryService } from './product-inventory.service';
import { ProductLifecycleService } from './product-lifecycle.service';

/**
 * 同步操作上下文，用于追踪和审计
 */
export interface SyncContext {
  taskId: string;
  syncId?: number;
  source: string;
}

/**
 * 产品同步编排服务
 * 责任：协调各个专门服务完成产品同步任务
 * 
 * 架构改进：
 * - 遵循单一职责原则，每个服务专注于特定功能
 * - 使用编排模式协调各服务交互
 * - 保持原有API接口兼容性
 */
@Injectable()
export class ProductSyncOrchestratorService {
  private readonly logger = new Logger(ProductSyncOrchestratorService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(Provider)
    private readonly providerRepository: Repository<Provider>,
    private readonly inventoryService: InventoryService,
    private readonly syncHistoryService: SyncHistoryService,
    private readonly supplierDataMapper: SupplierDataMapperService,
    private readonly productPricing: ProductPricingService,
    private readonly productCache: ProductCacheService,
    private readonly syncTaskStatus: SyncTaskStatusService,
    private readonly productInventory: ProductInventoryService,
    private readonly productLifecycle: ProductLifecycleService,
  ) {}

  /**
   * 同步所有供应商的产品和价格 - 全量产品同步
   */
  @Task({
    name: 'task.syncAllProducts',
    description: '同步所有供应商产品和价格',
  })
  async syncAllProviderProducts(
    providerIdToSync?: number,
    syncType?: string,
    proxiesFormat?: number,
    purposeWeb?: string,
    proxyType?: string,
    overrideStrategy?: SyncStrategy,
  ) {
    const taskId = uuidv4();
    const initialStatus = this.syncTaskStatus.createInitialSyncStatus(
      taskId,
      providerIdToSync,
      syncType,
      proxiesFormat,
      purposeWeb,
      proxyType,
      overrideStrategy,
    );

    await this.syncTaskStatus.updateSyncStatus(initialStatus);
    this.logger.log(
      `Product synchronization task ${taskId} started for provider ID: ${providerIdToSync || 'all'}, type: ${syncType || 'all'}${overrideStrategy ? `, override strategy: ${overrideStrategy}` : ''}`,
    );

    // 创建同步历史记录
    const syncHistory = await this.syncHistoryService.createSyncHistory({
      taskId,
      providerId: providerIdToSync,
      syncType: syncType || 'all',
      syncParams: {
        proxies_format: proxiesFormat,
        purposeWeb,
        proxies_type: proxyType,
      },
      createdBy: 'system',
    });

    // 异步执行同步任务
    setImmediate(async () => {
      const currentStatus = { ...initialStatus };
      try {
        const providersToSync = await this.getProvidersToSync(providerIdToSync);

        if (providersToSync.length === 0) {
          await this.handleNoProviders(currentStatus, taskId);
          return;
        }

        currentStatus.currentStep = `Found ${providersToSync.length} active providers. Preparing to sync...`;
        currentStatus.totalProducts = 0;
        await this.syncTaskStatus.updateSyncStatus(currentStatus);

        const syncResults = await this.syncAllProviders(
          providersToSync,
          currentStatus,
          syncHistory.syncId,
          overrideStrategy,
        );

        await this.completeSyncTask(currentStatus, syncResults, providersToSync.length, taskId);
      } catch (error) {
        await this.handleSyncTaskError(currentStatus, error, taskId);
      }
    });

    return { message: 'Sync task successfully initiated', taskId };
  }

  /**
   * 获取需要同步的供应商列表
   */
  private async getProvidersToSync(providerIdToSync?: number): Promise<Provider[]> {
    if (providerIdToSync) {
      const provider = await this.providerRepository.findOne({
        where: { providerId: providerIdToSync, status: '0' },
      });
      if (!provider) {
        throw new Error(`Provider with ID ${providerIdToSync} not found or not active.`);
      }
      return [provider];
    } else {
      return await this.providerRepository.find({ where: { status: '0' } });
    }
  }

  /**
   * 处理没有供应商的情况
   */
  private async handleNoProviders(currentStatus: any, taskId: string): Promise<void> {
    this.logger.log(`Task ${taskId}: No active providers to sync.`);
    currentStatus.currentStep = 'No active providers found.';
    currentStatus.isSyncing = false;
    currentStatus.endTime = new Date().toISOString();
    await this.syncTaskStatus.updateSyncStatus(currentStatus);
  }

  /**
   * 同步所有供应商
   */
  private async syncAllProviders(
    providersToSync: Provider[],
    currentStatus: any,
    syncId: number,
    overrideStrategy?: SyncStrategy,
  ): Promise<{ syncedCount: number; failedCount: number; totalAttempted: number; errors: string[] }> {
    let overallSyncedProducts = 0;
    let overallFailedProducts = 0;
    let overallTotalAttempted = 0;
    const allErrors: string[] = [];

    for (let i = 0; i < providersToSync.length; i++) {
      const provider = providersToSync[i];
      currentStatus.currentStep = `Syncing products for provider: ${provider.providerName} (${i + 1}/${providersToSync.length})`;
      currentStatus.progress = Math.round((i / providersToSync.length) * 80);
      await this.syncTaskStatus.updateSyncStatus(currentStatus);

      const providerSyncResult = await this.syncProviderProducts(
        provider,
        currentStatus,
        async (progressUpdate) => {
          await this.syncTaskStatus.updateSyncStatus(progressUpdate);
        },
        syncId,
        overrideStrategy,
      );

      overallSyncedProducts += providerSyncResult.syncedCount;
      overallFailedProducts += providerSyncResult.failedCount;
      overallTotalAttempted += providerSyncResult.totalAttempted;
      allErrors.push(...providerSyncResult.errors.map((e) => `[${provider.providerName}] ${e}`));

      currentStatus.syncedProducts = overallSyncedProducts;
      currentStatus.failedProducts = overallFailedProducts;
      currentStatus.errors = allErrors;
    }

    currentStatus.totalProducts = overallTotalAttempted;

    // 清除产品缓存
    currentStatus.currentStep = 'Clearing product cache...';
    currentStatus.progress = 90;
    await this.syncTaskStatus.updateSyncStatus(currentStatus);
    await this.productCache.clearAllProductCache();

    return {
      syncedCount: overallSyncedProducts,
      failedCount: overallFailedProducts,
      totalAttempted: overallTotalAttempted,
      errors: allErrors,
    };
  }

  /**
   * 同步单个供应商的产品
   */
  private async syncProviderProducts(
    provider: Provider,
    currentOverallStatus: any,
    updateProgressCallback: (progressUpdate: any) => Promise<void>,
    syncId?: number,
    overrideStrategy?: SyncStrategy,
  ) {
    this.logger.log(`Fetching inventory for provider: ${provider.providerName}`);
    const localStatus = { ...currentOverallStatus, currentStep: `Fetching inventory for ${provider.providerName}` };
    await updateProgressCallback(localStatus);

    // 获取同步参数
    const syncParams = this.getSyncParams(currentOverallStatus, provider);
    this.logger.log(
      `🔧 [产品同步] 参数确认 | 代理类型: ${syncParams.proxyType} | 格式: ${syncParams.proxiesFormat} | 用途: ${syncParams.purposeWeb}`,
    );

    // 获取库存数据
    const inventoryData = await this.inventoryService.getNodeInventory({
      proxies_type: syncParams.proxyType,
      proxies_format: syncParams.proxiesFormat,
      purpose_web: syncParams.purposeWeb,
    });

    const inventoryItems = inventoryData?.country_list;

    if (!inventoryItems || inventoryItems.length === 0) {
      return await this.handleEmptyInventory(provider, syncParams, localStatus, updateProgressCallback, syncId);
    }

    return await this.processInventoryItems(
      provider,
      inventoryItems,
      syncParams,
      localStatus,
      updateProgressCallback,
      syncId,
      overrideStrategy,
    );
  }

  /**
   * 获取同步参数
   */
  private getSyncParams(currentOverallStatus: any, provider: Provider) {
    return {
      proxyType: currentOverallStatus.proxyType || provider.configDetails?.sync_config?.proxy_type || 'Shared (ISP) proxies',
      proxiesFormat: currentOverallStatus.proxiesFormat || provider.configDetails?.sync_config?.proxies_format || 2,
      purposeWeb: currentOverallStatus.purposeWeb || provider.configDetails?.sync_config?.purpose_web || 'TikTok',
    };
  }

  /**
   * 处理空库存的情况
   */
  private async handleEmptyInventory(
    provider: Provider,
    syncParams: any,
    localStatus: any,
    updateProgressCallback: (progressUpdate: any) => Promise<void>,
    syncId?: number,
  ) {
    this.logger.log(`No inventory items found for provider ${provider.providerName} with type ${syncParams.proxyType}.`);
    localStatus.currentStep = `No inventory for ${provider.providerName} (${syncParams.proxyType}).`;
    await updateProgressCallback(localStatus);

    try {
      localStatus.currentStep = `Provider ${provider.providerName}: Setting zero stock for all products in category...`;
      await updateProgressCallback(localStatus);

      const zeroStockResult = await this.productInventory.setZeroStockForMissingProducts(
        provider.providerId,
        syncParams.proxyType,
        [],
        syncId,
        syncParams.proxiesFormat,
        syncParams.purposeWeb,
      );

      this.logger.log(`供应商 ${provider.providerName} 零库存处理完成: ${zeroStockResult.updatedCount} 个产品库存设置为0`);
    } catch (e) {
      this.logger.error(`Failed to set zero stock for all products from ${provider.providerName}: ${e.message}`);
    }

    return {
      syncedCount: 0,
      failedCount: 0,
      totalAttempted: 0,
      errors: [`No inventory for ${provider.providerName} (${syncParams.proxyType})`],
    };
  }

  /**
   * 处理库存项目
   */
  private async processInventoryItems(
    provider: Provider,
    inventoryItems: any[],
    syncParams: any,
    localStatus: any,
    updateProgressCallback: (progressUpdate: any) => Promise<void>,
    syncId?: number,
    overrideStrategy?: SyncStrategy,
  ) {
    this.logger.log(`Found ${inventoryItems.length} inventory items for ${provider.providerName}`);
    localStatus.currentStep = `Processing ${inventoryItems.length} inventory items for ${provider.providerName}`;
    await updateProgressCallback(localStatus);

    let syncedCount = 0;
    let failedCount = 0;
    const errors = [];
    const totalItemsForProvider = inventoryItems.length;
    const returnedProductIds: string[] = [];

    for (let i = 0; i < totalItemsForProvider; i++) {
      const inventoryItem = inventoryItems[i];
      try {
        const tempInventoryAdaptor = this.createInventoryAdaptor(provider, inventoryItem, syncParams);
        await this.createOrUpdateProductFromInventory(provider, tempInventoryAdaptor, localStatus, syncId, overrideStrategy);

        // 记录已处理的产品标识
        const externalProductId = this.generateExternalProductId(provider, tempInventoryAdaptor);
        returnedProductIds.push(String(externalProductId));

        syncedCount++;
      } catch (e) {
        this.logger.error(
          `Failed to sync product for item (City: ${inventoryItem.city_name}, Country: ${inventoryItem.country_code}) from ${provider.providerName}: ${e.message}`,
        );
        errors.push(`Failed for ${inventoryItem.city_name || inventoryItem.country_code}: ${e.message}`);
        failedCount++;
      }
      localStatus.currentStep = `Provider ${provider.providerName}: Processed ${i + 1}/${totalItemsForProvider} items.`;
      await updateProgressCallback(localStatus);
    }

    // 处理零库存
    await this.handleZeroStockProcessing(provider, syncParams, returnedProductIds, localStatus, updateProgressCallback, syncId);

    return { syncedCount, failedCount, totalAttempted: totalItemsForProvider, errors };
  }

  /**
   * 创建库存适配器
   */
  private createInventoryAdaptor(provider: Provider, inventoryItem: any, syncParams: any): ShopProviderIpInventory {
    const proxyTypeCompact = syncParams.proxyType.replace(/\s*\([^)]*\)\s*/g, '').replace(/\s+/g, '');
    
    return {
      providerId: provider.providerId,
      supplierProductId: `${inventoryItem.city_id || inventoryItem.country_code}_${proxyTypeCompact}`,
      productName: `${inventoryItem.city_name || inventoryItem.country_code} ${syncParams.proxyType.replace('Proxies', '').trim()} Proxies`,
      location: inventoryItem.city_name || inventoryItem.country_code,
      stockCount: inventoryItem.number || 0,
      price: 0,
      currency: 'USD',
      productType: syncParams.proxyType,
      rawData: inventoryItem,
      countryCode: inventoryItem.country_code,
      city_id: inventoryItem.city_id?.toString(),
      city_name: inventoryItem.city_name,
      country_code: inventoryItem.country_code,
      continents_id: inventoryItem.continents_id?.toString(),
      continents_name: inventoryItem.continents_name,
      stock_count: inventoryItem.number || 0,
      inventory_id: `${provider.providerId}-${inventoryItem.city_id || inventoryItem.country_code}-${proxyTypeCompact}`,
      proxies_type: syncParams.proxyType,
      proxies_format: syncParams.proxiesFormat,
      purpose_web: syncParams.purposeWeb,
      unit_price: 0,
      raw_data_snapshot: JSON.stringify(inventoryItem),
      status: '0',
    } as unknown as ShopProviderIpInventory;
  }

  /**
   * 基于库存条目创建或更新产品
   */
  private async createOrUpdateProductFromInventory(
    provider: Provider,
    inventoryItem: ShopProviderIpInventory,
    syncStatusRef: any,
    syncId?: number,
    overrideStrategy?: SyncStrategy,
  ) {
    try {
      // 地理位置标准化
      const locationData = await this.supplierDataMapper.mapLocationData(provider, inventoryItem);

      // 价格计算
      const pricingData = await this.productPricing.calculateProductPricing(inventoryItem);

      // 生成供应商地理位置信息
      const supplierLocation = this.supplierDataMapper.generateSupplierLocation(inventoryItem, provider);

      // 生成产品数据
      const productData = await this.productLifecycle.generateProductData(
        provider,
        inventoryItem,
        locationData,
        pricingData,
        supplierLocation,
      );

      // 创建或更新产品
      const { product, action } = await this.productLifecycle.createOrUpdateProduct(
        provider,
        productData,
        syncId,
        overrideStrategy,
      );

      syncStatusRef.syncedProducts++;

      // 清除产品缓存
      await this.productCache.clearProductCache([product.id]);
    } catch (error) {
      syncStatusRef.failedProducts++;
      this.logger.error(`处理产品失败: ${inventoryItem.proxies_type} - ${inventoryItem.city_name}`, error);
      syncStatusRef.errors.push({
        inventoryId: inventoryItem.inventory_id,
        error: error.message,
      });

      // 记录失败的产品同步详情
      if (syncId) {
        try {
          await this.syncHistoryService.addProductDetail({
            syncId,
            id: 0,
            action: 'failed',
            oldPrice: null,
            newPrice: null,
            oldStock: null,
            newStock: null,
            errorMessage: error.message,
          });
        } catch (detailError) {
          this.logger.error('记录产品同步详情失败', detailError);
        }
      }
    }
  }

  /**
   * 生成外部产品ID
   */
  private generateExternalProductId(provider: Provider, inventoryItem: ShopProviderIpInventory): string {
    const proxyTypeProcessed = inventoryItem.proxies_type.replace(/\s*\([^)]*\)\s*/g, '').replace(/\s+/g, '');
    return `${provider.providerId}-${inventoryItem.city_id || 'unknown'}-${proxyTypeProcessed}-${inventoryItem.proxies_format || 2}-${inventoryItem.purpose_web}`;
  }

  /**
   * 处理零库存
   */
  private async handleZeroStockProcessing(
    provider: Provider,
    syncParams: any,
    returnedProductIds: string[],
    localStatus: any,
    updateProgressCallback: (progressUpdate: any) => Promise<void>,
    syncId?: number,
  ) {
    try {
      localStatus.currentStep = `Provider ${provider.providerName}: Setting zero stock for missing products...`;
      await updateProgressCallback(localStatus);

      const zeroStockResult = await this.productInventory.setZeroStockForMissingProducts(
        provider.providerId,
        syncParams.proxyType,
        returnedProductIds,
        syncId,
        syncParams.proxiesFormat,
        syncParams.purposeWeb,
      );

      this.logger.log(`供应商 ${provider.providerName} 零库存处理完成: ${zeroStockResult.updatedCount} 个产品库存设置为0`);
    } catch (e) {
      this.logger.error(`Failed to set zero stock for missing products from ${provider.providerName}: ${e.message}`);
    }
  }

  /**
   * 完成同步任务
   */
  private async completeSyncTask(
    currentStatus: any,
    syncResults: any,
    providersCount: number,
    taskId: string,
  ): Promise<void> {
    await this.syncTaskStatus.completeSyncTask(currentStatus, providersCount);
    this.logger.log(`Product synchronization task ${taskId} finished.`);

    // 更新同步历史记录为完成状态
    await this.syncHistoryService.updateSyncHistory(taskId, {
      endTime: new Date(),
      status: 'completed',
      totalProducts: currentStatus.totalProducts,
      syncedProducts: currentStatus.syncedProducts,
      failedProducts: currentStatus.failedProducts,
      errorDetails: currentStatus.errors.length > 0 ? { errors: currentStatus.errors } : null,
    });
  }

  /**
   * 处理同步任务错误
   */
  private async handleSyncTaskError(currentStatus: any, error: Error, taskId: string): Promise<void> {
    this.logger.error(`Error during product synchronization task ${taskId}:`, error);
    await this.syncTaskStatus.handleSyncTaskFailure(currentStatus, error);

    // 更新同步历史记录为失败状态
    await this.syncHistoryService.updateSyncHistory(taskId, {
      endTime: new Date(),
      status: 'failed',
      totalProducts: currentStatus.totalProducts,
      syncedProducts: currentStatus.syncedProducts,
      failedProducts: currentStatus.failedProducts,
      errorDetails: { errors: currentStatus.errors },
    });
  }

  // ============== 保持兼容性的公共API方法 ==============

  /**
   * 获取产品同步状态
   */
  async getSyncStatus() {
    return await this.syncTaskStatus.getSyncStatus();
  }

  /**
   * 清除同步状态
   */
  async clearSyncStatus() {
    return await this.syncTaskStatus.clearSyncStatus();
  }

  /**
   * 停止同步任务
   */
  async stopSyncTask() {
    return await this.syncTaskStatus.stopSyncTask();
  }

  /**
   * 同步特定产品
   */
  @Task({
    name: 'task.syncSpecificProducts',
    description: '同步特定产品',
  })
  async syncSpecificProducts(providerId: number, productIds: number[], syncType: string = 'all') {
    const taskId = uuidv4();
    const context: SyncContext = {
      taskId,
      source: 'manual_specific_products',
    };

    const products = await this.productRepository.find({
      where: { id: In(productIds) },
    });

    const syncLog = {
      startTime: new Date(),
      endTime: null as Date | null,
      providerId,
      productIds,
      syncType,
      totalProducts: productIds.length,
      syncedProducts: 0,
      failedProducts: 0,
      errors: [],
    };

    for (const product of products) {
      try {
        switch (syncType) {
          case 'inventory':
            await this.productInventory.syncProductInventory(product);
            break;
          case 'price':
            await this.syncProductPrice(product, context);
            break;
          case 'all':
          default:
            await this.syncProductPrice(product, context);
            await this.productInventory.syncProductInventory(product);
            break;
        }

        syncLog.syncedProducts++;
      } catch (error) {
        syncLog.failedProducts++;
        syncLog.errors.push({
          id: product.id,
          error: error.message,
        });
      }
    }

    syncLog.endTime = new Date();
    return syncLog;
  }

  /**
   * 同步产品价格（简化版）
   */
  private async syncProductPrice(product: Product, context: SyncContext): Promise<void> {
    // 安全防护检查
    if (
      product.isPriceManual ||
      product.syncStrategy === SyncStrategy.MANUAL_OVERRIDE ||
      product.syncStrategy === SyncStrategy.DISABLED
    ) {
      this.logger.log(
        `[TaskID: ${context.taskId}] 跳过产品 #${product.id} 价格同步，策略: ${product.syncStrategy}, 手动价格: ${product.isPriceManual}. 来源: ${context.source}`,
      );
      return;
    }

    const oldPrice = product.price;

    // 获取最新价格信息
    const priceData = await this.inventoryService.calculatePrice({
      proxies_type: product.productConfigDetails?.proxyType || 'Shared (ISP) proxies',
      proxies_format: 2,
      time_period: 30,
      currency: 'USD',
    });

    if (priceData?.proxies_count_discount_tiers) {
      const cityName = product.productConfigDetails?.supplierLocation?.cityName || product.productConfigDetails?.cityName;
      const countryCode = product.productConfigDetails?.supplierLocation?.countryCode;

      const priceTier = priceData.proxies_count_discount_tiers.find(
        (tier) => tier.country_name === cityName || tier.country_name?.toLowerCase() === countryCode?.toLowerCase(),
      );

      if (priceTier) {
        const priceUSD = priceTier.per_proxy_price;
        const discountPercentage = priceTier.discount_percentage || 0;

        // 转换为CNY并计算销售价
        const priceCNY = await this.productPricing.convertUsdToCny(priceUSD);
        const finalPrice = await this.productPricing.recalculateSellingPrice(priceCNY);
        const discountPrice = discountPercentage > 0 ? Number((finalPrice * (1 - discountPercentage / 100)).toFixed(2)) : finalPrice;

        // 只有价格真正改变时才进行数据库操作
        if (oldPrice !== finalPrice) {
          product.price = finalPrice;
          product.discountPrice = discountPrice;
          product.costPrice = priceCNY;
          product.costPriceCurrency = 'CNY';
          product.currency = 'CNY';
          product.updateTime = new Date();

          await this.productRepository.save(product);

          if (context.syncId) {
            await this.syncHistoryService.addProductDetail({
              syncId: context.syncId,
              id: product.id,
              action: 'updated',
              oldPrice,
              newPrice: finalPrice,
              oldStock: null,
              newStock: null,
              errorMessage: null,
            });
          }

          this.logger.log(`[TaskID: ${context.taskId}] 产品 #${product.id} 价格已更新: ${oldPrice} -> ${finalPrice}`);
        }
      }
    }
  }

  /**
   * 定时同步任务
   */
  @Task({
    name: 'task.syncAllProducts',
    description: '同步所有供应商产品和价格',
  })
  async scheduledSync() {
    this.logger.log('开始定时产品同步任务');
    await this.syncAllProviderProducts();
  }

  /**
   * 同步单个产品
   */
  async syncSingleProduct(id: number) {
    const product = await this.productRepository.findOne({
      where: { id, delFlag: '0' },
    });

    if (!product) {
      throw new Error(`产品 ${id} 不存在`);
    }

    const context: SyncContext = {
      taskId: uuidv4(),
      source: 'manual_single_product',
    };

    await this.syncProductPrice(product, context);
    await this.productInventory.syncProductInventory(product);

    return {
      id: product.id,
      productName: product.productName,
      price: product.price,
      inventoryStatus: product.inventoryStatus,
    };
  }

  /**
   * 批量同步产品价格
   */
  @Task({
    name: 'task.syncAllProductPrices',
    description: '同步所有产品价格',
  })
  async syncAllProductPrices() {
    const taskId = uuidv4();
    const source = 'scheduled_task:syncAllProductPrices';
    this.logger.log(`开始价格同步任务. TaskID: ${taskId}`);

    let successCount = 0;
    let failCount = 0;

    try {
      const productsToSync = await this.productRepository.find({
        where: {
          delFlag: '0',
          status: '0',
          isPriceManual: false,
          syncStrategy: In([SyncStrategy.FULL_SYNC, SyncStrategy.STOCK_AND_COST_ONLY]),
        },
      });

      this.logger.log(`[TaskID: ${taskId}] 发现 ${productsToSync.length} 个产品需要潜在同步`);

      for (const product of productsToSync) {
        try {
          await this.syncProductPrice(product, { taskId, source });
          successCount++;
        } catch (error) {
          failCount++;
          this.logger.error(`[TaskID: ${taskId}] 同步产品价格失败: ${product.productName}`, error);
        }
      }

      this.logger.log(`[TaskID: ${taskId}] 价格同步完成: 成功 ${successCount}, 失败 ${failCount}`);
      return { successCount, failCount, total: productsToSync.length };
    } catch (error) {
      this.logger.error(`[TaskID: ${taskId}] syncAllProductPrices 任务执行出错:`, error);
      throw error;
    }
  }

  /**
   * 批量同步产品库存
   */
  @Task({
    name: 'task.syncAllProductInventory',
    description: '同步所有产品库存',
  })
  async syncAllProductInventory() {
    this.logger.log('开始同步所有产品库存');

    const products = await this.productRepository.find({
      where: { delFlag: '0', status: '0' },
    });

    let successCount = 0;
    let failCount = 0;

    for (const product of products) {
      try {
        await this.productInventory.syncProductInventory(product);
        successCount++;
      } catch (error) {
        failCount++;
        this.logger.error(`同步产品库存失败: ${product.productName}`, error);
      }
    }

    this.logger.log(`库存同步完成: 成功 ${successCount}, 失败 ${failCount}`);
    return { successCount, failCount, total: products.length };
  }

  /**
   * 批量重新计算所有产品销售价格
   */
  @Task({
    name: 'task.recalculateAllProductSellingPrices',
    description: '重新计算所有自动定价产品的销售价格',
  })
  async recalculateAllProductSellingPrices() {
    this.logger.log('开始批量重新计算产品销售价格');

    const products = await this.productRepository.find({
      where: {
        delFlag: '0',
        status: '0',
        syncStrategy: In([SyncStrategy.FULL_SYNC, SyncStrategy.STOCK_AND_COST_ONLY]),
      },
    });

    const syncLog = {
      startTime: new Date(),
      endTime: null as Date | null,
      totalProducts: products.length,
      processedProducts: 0,
      updatedProducts: 0,
      failedProducts: 0,
      errors: [],
    };

    this.logger.log(`找到 ${products.length} 个需要重新计算价格的产品`);

    // 分批处理
    const batchSize = 100;
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, Math.min(i + batchSize, products.length));
      const updatedBatch = [];

      for (const product of batch) {
        try {
          syncLog.processedProducts++;

          if (!product.costPrice || product.costPrice <= 0) {
            syncLog.errors.push({
              id: product.id,
              productName: product.productName,
              error: '产品没有有效的成本价',
            });
            syncLog.failedProducts++;
            continue;
          }

          // 转换成本价为CNY（如果需要）
          let costPriceCNY = product.costPrice;
          if (product.costPriceCurrency === 'USD') {
            costPriceCNY = await this.productPricing.convertUsdToCny(product.costPrice);
            product.costPrice = Number(costPriceCNY.toFixed(2));
            product.costPriceCurrency = 'CNY';
          }

          // 重新计算销售价
          const newPrice = await this.productPricing.recalculateSellingPrice(costPriceCNY);

          if (product.price !== newPrice || product.currency !== 'CNY') {
            product.price = newPrice;
            product.currency = 'CNY';
            product.updateTime = new Date();
            updatedBatch.push(product);
            syncLog.updatedProducts++;
          }
        } catch (error) {
          syncLog.failedProducts++;
          syncLog.errors.push({
            id: product.id,
            productName: product.productName,
            error: error.message,
          });
        }
      }

      if (updatedBatch.length > 0) {
        await this.productRepository.save(updatedBatch);
        this.logger.log(`批次 ${Math.floor(i / batchSize) + 1}: 更新了 ${updatedBatch.length} 个产品价格`);
      }
    }

    // 清理所有产品缓存
    await this.productCache.clearAllProductCache();

    syncLog.endTime = new Date();
    this.logger.log('产品价格重算完成', syncLog);

    return syncLog;
  }
}