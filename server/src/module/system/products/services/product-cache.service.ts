import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../../common/redis/redis.service';

/**
 * 产品缓存服务
 * 负责：
 * - 产品相关缓存的管理
 * - 缓存键的生成和清理
 * - 缓存策略的实施
 */
@Injectable()
export class ProductCacheService {
  private readonly logger = new Logger(ProductCacheService.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * 清理特定产品的缓存
   */
  async clearProductCache(productIds: number[]): Promise<void> {
    if (productIds && productIds.length > 0) {
      const keys = productIds.map((id) => `product:${id}`);
      await Promise.all(keys.map((key) => this.redisService.del(key)));
      this.logger.log(`清理了 ${keys.length} 个产品缓存`);
    }
  }

  /**
   * 清理所有产品相关缓存
   */
  async clearAllProductCache(): Promise<void> {
    const cacheKeys = await this.redisService.keys('ip_inventory:*');
    const productKeys = await this.redisService.keys('product:*');
    const allKeys = [...cacheKeys, ...productKeys];

    if (allKeys.length > 0) {
      await Promise.all(allKeys.map((key) => this.redisService.del(key)));
      this.logger.log(`清理了 ${allKeys.length} 个缓存键`);
    }
  }

  /**
   * 设置产品缓存
   */
  async setProductCache(productId: number, data: any, ttl: number = 3600): Promise<void> {
    const key = `product:${productId}`;
    await this.redisService.set(key, JSON.stringify(data), ttl);
  }

  /**
   * 获取产品缓存
   */
  async getProductCache(productId: number): Promise<any> {
    const key = `product:${productId}`;
    const data = await this.redisService.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 清理特定类型的缓存
   */
  async clearCacheByPattern(pattern: string): Promise<void> {
    const keys = await this.redisService.keys(pattern);
    if (keys.length > 0) {
      await Promise.all(keys.map((key) => this.redisService.del(key)));
      this.logger.log(`清理了 ${keys.length} 个匹配模式 ${pattern} 的缓存键`);
    }
  }

  /**
   * 批量清理库存相关缓存
   */
  async clearInventoryCache(): Promise<void> {
    await this.clearCacheByPattern('ip_inventory:*');
  }

  /**
   * 清理价格相关缓存
   */
  async clearPriceCache(): Promise<void> {
    await this.clearCacheByPattern('price:*');
  }
}