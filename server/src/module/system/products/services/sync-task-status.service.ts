import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../../common/redis/redis.service';

export interface SyncTaskStatus {
  taskId: string;
  isSyncing: boolean;
  startTime: string;
  endTime: string | null;
  currentStep: string;
  progress: number;
  syncedProducts: number;
  failedProducts: number;
  totalProducts: number;
  errors: any[];
  providerId?: number;
  syncType?: string;
  proxiesFormat?: number;
  purposeWeb?: string;
  proxyType?: string;
  overrideStrategy?: string;
}

/**
 * 同步任务状态服务
 * 负责：
 * - Redis同步状态的管理
 * - 同步进度的跟踪
 * - 同步历史信息的维护
 */
@Injectable()
export class SyncTaskStatusService {
  private readonly logger = new Logger(SyncTaskStatusService.name);
  private readonly SYNC_STATUS_KEY = 'product_sync_status';
  private readonly LAST_SUCCESSFUL_SYNC_INFO_KEY = 'product_last_successful_sync_info';

  constructor(private readonly redisService: RedisService) {}

  /**
   * 更新同步状态
   */
  async updateSyncStatus(status: SyncTaskStatus): Promise<void> {
    await this.redisService.set(this.SYNC_STATUS_KEY, JSON.stringify(status));
  }

  /**
   * 获取当前同步状态
   */
  async getSyncStatus(): Promise<any> {
    const currentSyncState = await this.redisService.get(this.SYNC_STATUS_KEY);
    let currentStatus: any;
    
    if (currentSyncState) {
      currentStatus = currentSyncState;
    } else {
      currentStatus = {
        isSyncing: false,
        currentSyncLog: null,
        errors: [],
        taskId: null,
      };
    }
    
    currentStatus.lastSyncInfo = await this.getLastSuccessfulSyncInfo();
    return currentStatus;
  }

  /**
   * 获取最后成功同步信息
   */
  async getLastSuccessfulSyncInfo(): Promise<any> {
    return await this.redisService.get(this.LAST_SUCCESSFUL_SYNC_INFO_KEY);
  }

  /**
   * 设置最后成功同步信息
   */
  async setLastSuccessfulSyncInfo(info: any): Promise<void> {
    await this.redisService.set(this.LAST_SUCCESSFUL_SYNC_INFO_KEY, JSON.stringify(info));
  }

  /**
   * 清除同步状态
   */
  async clearSyncStatus(): Promise<{ success: boolean; message: string; timestamp: string }> {
    try {
      await this.redisService.del(this.SYNC_STATUS_KEY);
      this.logger.warn('同步状态已被手动清除（可能是僵尸任务处理）');

      return {
        success: true,
        message: '同步状态已清除',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('清除同步状态失败', error);
      throw new Error('清除同步状态失败: ' + error.message);
    }
  }

  /**
   * 停止同步任务
   */
  async stopSyncTask(): Promise<{ success: boolean; message: string; taskId?: string; timestamp: string }> {
    try {
      const currentStatus = await this.redisService.get(this.SYNC_STATUS_KEY);

      if (!currentStatus || !currentStatus.isSyncing) {
        return {
          success: true,
          message: '当前没有正在运行的同步任务',
          timestamp: new Date().toISOString(),
        };
      }

      const stoppedStatus = {
        ...currentStatus,
        isSyncing: false,
        endTime: new Date().toISOString(),
        currentStep: '任务已被手动停止',
        errors: [...(currentStatus.errors || []), '任务被用户手动停止'],
      };

      await this.redisService.set(this.SYNC_STATUS_KEY, stoppedStatus, 3600);
      this.logger.warn('同步任务已被手动停止', { taskId: currentStatus.taskId });

      return {
        success: true,
        message: '同步任务已停止',
        taskId: currentStatus.taskId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('停止同步任务失败', error);
      throw new Error('停止同步任务失败: ' + error.message);
    }
  }

  /**
   * 创建初始同步状态
   */
  createInitialSyncStatus(
    taskId: string,
    providerIdToSync?: number,
    syncType?: string,
    proxiesFormat?: number,
    purposeWeb?: string,
    proxyType?: string,
    overrideStrategy?: string,
  ): SyncTaskStatus {
    return {
      taskId,
      isSyncing: true,
      startTime: new Date().toISOString(),
      endTime: null,
      currentStep: 'Sync task initiated...',
      progress: 0,
      syncedProducts: 0,
      failedProducts: 0,
      totalProducts: 0,
      errors: [],
      providerId: providerIdToSync,
      syncType: syncType,
      proxiesFormat: proxiesFormat,
      purposeWeb: purposeWeb,
      proxyType: proxyType,
      overrideStrategy: overrideStrategy,
    };
  }

  /**
   * 完成同步任务
   */
  async completeSyncTask(
    status: SyncTaskStatus,
    providersCount: number,
  ): Promise<void> {
    status.currentStep = 'Synchronization complete.';
    status.progress = 100;
    status.isSyncing = false;
    status.endTime = new Date().toISOString();

    await this.updateSyncStatus(status);

    if (status.failedProducts === 0 && status.totalProducts > 0) {
      await this.setLastSuccessfulSyncInfo({
        lastSyncTime: status.endTime,
        productName: `Synced ${status.syncedProducts} products across ${providersCount} provider(s).`,
      });
    }
  }

  /**
   * 处理同步任务失败
   */
  async handleSyncTaskFailure(
    status: SyncTaskStatus,
    error: Error,
  ): Promise<void> {
    status.errors.push(`General error: ${error.message}`);
    status.currentStep = 'Synchronization failed.';
    status.isSyncing = false;
    status.progress = 100;
    status.endTime = new Date().toISOString();

    await this.updateSyncStatus(status);
  }
}