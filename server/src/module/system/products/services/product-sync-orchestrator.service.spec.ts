import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductSyncOrchestratorService } from './product-sync-orchestrator.service';
import { Product } from '../../../shop/products/entities/product.entity';
import { Provider } from '../../provider/entities/provider.entity';
import { InventoryService } from '../../../shop/inventory/inventory.service';
import { SyncHistoryService } from '../../sync-history/sync-history.service';
import { SupplierDataMapperService } from './supplier-data-mapper.service';
import { ProductPricingService } from './product-pricing.service';
import { ProductCacheService } from './product-cache.service';
import { SyncTaskStatusService } from './sync-task-status.service';
import { ProductInventoryService } from './product-inventory.service';
import { ProductLifecycleService } from './product-lifecycle.service';

describe('ProductSyncOrchestratorService', () => {
  let service: ProductSyncOrchestratorService;
  let productRepository: Repository<Product>;
  let providerRepository: Repository<Provider>;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockInventoryService = {
    getNodeInventory: jest.fn(),
    calculatePrice: jest.fn(),
    getUsdToCnyRate: jest.fn(),
  };

  const mockSyncHistoryService = {
    createSyncHistory: jest.fn(),
    updateSyncHistory: jest.fn(),
    addProductDetail: jest.fn(),
  };

  const mockSupplierDataMapper = {
    mapLocationData: jest.fn(),
    generateSupplierLocation: jest.fn(),
  };

  const mockProductPricing = {
    calculateProductPricing: jest.fn(),
    convertUsdToCny: jest.fn(),
    recalculateSellingPrice: jest.fn(),
  };

  const mockProductCache = {
    clearProductCache: jest.fn(),
    clearAllProductCache: jest.fn(),
  };

  const mockSyncTaskStatus = {
    createInitialSyncStatus: jest.fn(),
    updateSyncStatus: jest.fn(),
    getSyncStatus: jest.fn(),
    clearSyncStatus: jest.fn(),
    stopSyncTask: jest.fn(),
    completeSyncTask: jest.fn(),
    handleSyncTaskFailure: jest.fn(),
  };

  const mockProductInventory = {
    syncProductInventory: jest.fn(),
    setZeroStockForMissingProducts: jest.fn(),
  };

  const mockProductLifecycle = {
    generateProductData: jest.fn(),
    createOrUpdateProduct: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductSyncOrchestratorService,
        {
          provide: getRepositoryToken(Product),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Provider),
          useValue: mockRepository,
        },
        {
          provide: InventoryService,
          useValue: mockInventoryService,
        },
        {
          provide: SyncHistoryService,
          useValue: mockSyncHistoryService,
        },
        {
          provide: SupplierDataMapperService,
          useValue: mockSupplierDataMapper,
        },
        {
          provide: ProductPricingService,
          useValue: mockProductPricing,
        },
        {
          provide: ProductCacheService,
          useValue: mockProductCache,
        },
        {
          provide: SyncTaskStatusService,
          useValue: mockSyncTaskStatus,
        },
        {
          provide: ProductInventoryService,
          useValue: mockProductInventory,
        },
        {
          provide: ProductLifecycleService,
          useValue: mockProductLifecycle,
        },
      ],
    }).compile();

    service = module.get<ProductSyncOrchestratorService>(ProductSyncOrchestratorService);
    productRepository = module.get<Repository<Product>>(getRepositoryToken(Product));
    providerRepository = module.get<Repository<Provider>>(getRepositoryToken(Provider));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('syncAllProviderProducts', () => {
    it('should initialize sync task and return task info', async () => {
      // 模拟初始状态创建
      const mockInitialStatus = {
        taskId: 'test-task-id',
        isSyncing: true,
        startTime: new Date().toISOString(),
        endTime: null,
        currentStep: 'Sync task initiated...',
        progress: 0,
        syncedProducts: 0,
        failedProducts: 0,
        totalProducts: 0,
        errors: [],
      };

      const mockSyncHistory = {
        syncId: 1,
        taskId: 'test-task-id',
      };

      mockSyncTaskStatus.createInitialSyncStatus.mockReturnValue(mockInitialStatus);
      mockSyncTaskStatus.updateSyncStatus.mockResolvedValue(undefined);
      mockSyncHistoryService.createSyncHistory.mockResolvedValue(mockSyncHistory);

      const result = await service.syncAllProviderProducts();

      expect(result).toEqual({
        message: 'Sync task successfully initiated',
        taskId: expect.any(String),
      });

      expect(mockSyncTaskStatus.createInitialSyncStatus).toHaveBeenCalled();
      expect(mockSyncTaskStatus.updateSyncStatus).toHaveBeenCalledWith(mockInitialStatus);
      expect(mockSyncHistoryService.createSyncHistory).toHaveBeenCalled();
    });

    it('should handle provider filtering correctly', async () => {
      const mockProvider = {
        providerId: 1,
        providerName: 'Test Provider',
        status: '0',
      };

      const mockInitialStatus = {
        taskId: 'test-task-id',
        isSyncing: true,
      };

      const mockSyncHistory = { syncId: 1 };

      mockSyncTaskStatus.createInitialSyncStatus.mockReturnValue(mockInitialStatus);
      mockSyncTaskStatus.updateSyncStatus.mockResolvedValue(undefined);
      mockSyncHistoryService.createSyncHistory.mockResolvedValue(mockSyncHistory);
      mockRepository.findOne.mockResolvedValue(mockProvider);

      const result = await service.syncAllProviderProducts(1);

      expect(result.taskId).toBeDefined();
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { providerId: 1, status: '0' },
      });
    });
  });

  describe('getSyncStatus', () => {
    it('should return sync status from task status service', async () => {
      const mockStatus = {
        isSyncing: false,
        lastSyncInfo: null,
      };

      mockSyncTaskStatus.getSyncStatus.mockResolvedValue(mockStatus);

      const result = await service.getSyncStatus();

      expect(result).toEqual(mockStatus);
      expect(mockSyncTaskStatus.getSyncStatus).toHaveBeenCalled();
    });
  });

  describe('clearSyncStatus', () => {
    it('should clear sync status', async () => {
      const mockResult = {
        success: true,
        message: '同步状态已清除',
        timestamp: expect.any(String),
      };

      mockSyncTaskStatus.clearSyncStatus.mockResolvedValue(mockResult);

      const result = await service.clearSyncStatus();

      expect(result).toEqual(mockResult);
      expect(mockSyncTaskStatus.clearSyncStatus).toHaveBeenCalled();
    });
  });

  describe('stopSyncTask', () => {
    it('should stop sync task', async () => {
      const mockResult = {
        success: true,
        message: '同步任务已停止',
        timestamp: expect.any(String),
      };

      mockSyncTaskStatus.stopSyncTask.mockResolvedValue(mockResult);

      const result = await service.stopSyncTask();

      expect(result).toEqual(mockResult);
      expect(mockSyncTaskStatus.stopSyncTask).toHaveBeenCalled();
    });
  });

  describe('syncSpecificProducts', () => {
    it('should sync specific products successfully', async () => {
      const productIds = [1, 2];
      const mockProducts = [
        { id: 1, productName: 'Product 1' },
        { id: 2, productName: 'Product 2' },
      ];

      mockRepository.find.mockResolvedValue(mockProducts);
      mockProductInventory.syncProductInventory.mockResolvedValue({
        success: true,
        oldStock: 10,
        newStock: 15,
      });

      const result = await service.syncSpecificProducts(1, productIds, 'inventory');

      expect(result.totalProducts).toBe(2);
      expect(result.syncedProducts).toBe(2);
      expect(result.failedProducts).toBe(0);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { id: expect.any(Object) },
      });
    });
  });

  describe('Service Integration', () => {
    it('should properly integrate all specialized services', () => {
      // 验证所有专门服务都已正确注入
      expect(service['supplierDataMapper']).toBe(mockSupplierDataMapper);
      expect(service['productPricing']).toBe(mockProductPricing);
      expect(service['productCache']).toBe(mockProductCache);
      expect(service['syncTaskStatus']).toBe(mockSyncTaskStatus);
      expect(service['productInventory']).toBe(mockProductInventory);
      expect(service['productLifecycle']).toBe(mockProductLifecycle);
    });

    it('should maintain API compatibility with original ProductSyncService', async () => {
      // 验证编排服务保持了原有API的兼容性
      const publicMethods = [
        'syncAllProviderProducts',
        'getSyncStatus',
        'clearSyncStatus',
        'stopSyncTask',
        'syncSpecificProducts',
        'syncSingleProduct',
        'syncAllProductPrices',
        'syncAllProductInventory',
        'recalculateAllProductSellingPrices',
      ];

      publicMethods.forEach(method => {
        expect(typeof service[method]).toBe('function');
      });
    });
  });
});