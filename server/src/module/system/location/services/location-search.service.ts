import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { Repository } from 'typeorm';
import { CitySearchDto, CitySearchResultItem, CountrySearchDto, CountrySearchResultItem, LocationSearchResult, RegionSearchDto, RegionSearchResultItem, UnifiedSearchDto } from '../dto/search.dto';
import { LocationCity } from '../entities/location-city.entity';
import { LocationCountry } from '../entities/location-country.entity';
import { LocationRegion } from '../entities/location-region.entity';

@Injectable()
export class LocationSearchService {
  constructor(
    @InjectRepository(LocationRegion)
    private regionRepository: Repository<LocationRegion>,
    @InjectRepository(LocationCountry)
    private countryRepository: Repository<LocationCountry>,
    @InjectRepository(LocationCity)
    private cityRepository: Repository<LocationCity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * 安全解析整数，防止NaN错误
   */
  private safeParseInt(value: any, defaultValue: number): number {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }

    const parsed = Number(value);
    if (Number.isNaN(parsed) || !Number.isInteger(parsed) || parsed < 1) {
      return defaultValue;
    }

    return parsed;
  }

  /**
   * 统一位置搜索接口
   */
  async unifiedSearch(query: UnifiedSearchDto): Promise<LocationSearchResult> {
    const cacheKey = `location_search_${JSON.stringify(query)}`;

    // 检查缓存 (5分钟缓存)
    let result = await this.cacheManager.get<LocationSearchResult>(cacheKey);
    if (result) {
      return result;
    }

    // 执行搜索
    result = await this.performSearch(query);

    // 缓存结果
    await this.cacheManager.set(cacheKey, result, 300000); // 5分钟

    return result;
  }

  /**
   * 搜索区域
   */
  async searchRegions(query: RegionSearchDto): Promise<LocationSearchResult> {
    // 安全的参数处理，确保数值有效
    const pageNum = this.safeParseInt(query.pageNum, 1);
    const pageSize = this.safeParseInt(query.pageSize, 20);

    // 使用处理后的参数生成缓存键
    const cacheKey = `region_search_${JSON.stringify({
      keyword: query.keyword,
      pageNum,
      pageSize,
    })}`;

    let result = await this.cacheManager.get<LocationSearchResult>(cacheKey);
    if (result) {
      return result;
    }

    const queryBuilder = this.regionRepository.createQueryBuilder('region')
      .where('region.status = :status', { status: '0' })
      .andWhere('region.regionName != :globalRegion', { globalRegion: '全球' });

    if (query.keyword) {
      queryBuilder.andWhere('region.regionName LIKE :keyword', {
        keyword: `%${query.keyword}%`,
      });
    }

    queryBuilder.orderBy('region.sortOrder', 'ASC');
    queryBuilder.addOrderBy('region.id', 'ASC');

    const skip = (pageNum - 1) * pageSize;

    // 确保分页参数是有效的正整数
    if (!Number.isInteger(skip) || skip < 0 || !Number.isInteger(pageSize) || pageSize <= 0) {
      throw new Error(`Invalid pagination parameters: skip=${skip}, pageSize=${pageSize}`);
    }

    queryBuilder.skip(skip).take(pageSize);

    const [data, total] = await queryBuilder.getManyAndCount();

    // 批量查询国家数量
    const regionIds = data.map((region) => region.id);
    let countryCounts: any[] = [];

    if (regionIds.length > 0) {
      countryCounts = await this.countryRepository
        .createQueryBuilder('country')
        .select('country.regionId', 'regionId')
        .addSelect('COUNT(country.id)', 'count')
        .where('country.regionId IN (:...regionIds)', { regionIds })
        .andWhere('country.status = :status', { status: '0' })
        .groupBy('country.regionId')
        .getRawMany();
    }

    // 构建计数映射
    const countMap = new Map();
    countryCounts.forEach((item) => {
      countMap.set(item.regionId, parseInt(item.count));
    });

    // 格式化结果
    const rows: RegionSearchResultItem[] = data.map((region) => ({
      id: region.id,
      regionName: region.regionName,
      countryCount: countMap.get(region.id) || 0,
    }));

    result = {
      rows,
      total,
      pageNum,
      pageSize,
    };

    await this.cacheManager.set(cacheKey, result, 300000);
    return result;
  }

  /**
   * 搜索国家
   */
  async searchCountries(query: CountrySearchDto): Promise<LocationSearchResult> {
    // 安全的参数处理，确保数值有效
    const pageNum = this.safeParseInt(query.pageNum, 1);
    const pageSize = this.safeParseInt(query.pageSize, 20);

    const cacheKey = `country_search_${JSON.stringify({
      ...query,
      pageNum,
      pageSize,
    })}`;

    let result = await this.cacheManager.get<LocationSearchResult>(cacheKey);
    if (result) {
      return result;
    }

    const queryBuilder = this.countryRepository.createQueryBuilder('country').leftJoinAndSelect('country.region', 'region').where('country.status = :status', { status: '0' });

    // 区域筛选 - 使用 safeParseInt 确保 regionId 是有效数字
    const regionId = this.safeParseInt(query.regionId, 0);
    if (regionId > 0) {
      queryBuilder.andWhere('country.regionId = :regionId', { regionId });
    }

    if (query.keyword) {
      queryBuilder.andWhere(
        `(
        country.countryName LIKE :keyword OR
        country.countryNameEn LIKE :keyword OR
        country.countryCode LIKE :keyword
      )`,
        { keyword: `%${query.keyword}%` },
      );
    }

    queryBuilder.orderBy('country.sortOrder', 'ASC');
    queryBuilder.addOrderBy('country.id', 'ASC');

    const skip = (pageNum - 1) * pageSize;

    // 确保分页参数是有效的正整数
    if (!Number.isInteger(skip) || skip < 0 || !Number.isInteger(pageSize) || pageSize <= 0) {
      throw new Error(`Invalid pagination parameters: skip=${skip}, pageSize=${pageSize}`);
    }

    queryBuilder.skip(skip).take(pageSize);

    const [data, total] = await queryBuilder.getManyAndCount();

    // 批量查询城市数量
    const countryIds = data.map((country) => country.id);
    let cityCounts: any[] = [];

    if (countryIds.length > 0) {
      cityCounts = await this.cityRepository
        .createQueryBuilder('city')
        .select('city.countryId', 'countryId')
        .addSelect('COUNT(city.id)', 'count')
        .where('city.countryId IN (:...countryIds)', { countryIds })
        .andWhere('city.status = :status', { status: '0' })
        .groupBy('city.countryId')
        .getRawMany();
    }

    // 构建计数映射
    const countMap = new Map();
    cityCounts.forEach((item) => {
      countMap.set(item.countryId, parseInt(item.count));
    });

    // 格式化结果并过滤掉城市数量为0的国家
    const rows: CountrySearchResultItem[] = data
      .map((country) => ({
        id: country.id,
        countryName: country.countryName,
        countryNameEn: country.countryNameEn,
        countryCode: country.countryCode,
        regionId: country.regionId,
        regionName: country.region?.regionName,
        cityCount: countMap.get(country.id) || 0,
      }))
      .filter((country) => country.cityCount > 0);

    result = {
      rows,
      total: rows.length,
      pageNum,
      pageSize,
    };

    await this.cacheManager.set(cacheKey, result, 300000);
    return result;
  }

  /**
   * 搜索城市
   */
  async searchCities(query: CitySearchDto): Promise<LocationSearchResult> {
    // 安全的参数处理，确保数值有效
    const pageNum = this.safeParseInt(query.pageNum, 1);
    const pageSize = this.safeParseInt(query.pageSize, 50);

    const cacheKey = `city_search_${JSON.stringify({
      ...query,
      pageNum,
      pageSize,
    })}`;

    let result = await this.cacheManager.get<LocationSearchResult>(cacheKey);
    if (result) {
      return result;
    }

    const queryBuilder = this.cityRepository
      .createQueryBuilder('city')
      .leftJoinAndSelect('city.country', 'country')
      .leftJoinAndSelect('country.region', 'region')
      .where('city.status = :status', { status: '0' });

    // 国家筛选 - 使用 safeParseInt 确保 countryId 是有效数字
    const countryId = this.safeParseInt(query.countryId, 0);
    if (countryId > 0) {
      queryBuilder.andWhere('city.countryId = :countryId', { countryId });
    }

    // 区域筛选 - 使用 safeParseInt 确保 regionId 是有效数字
    const regionId = this.safeParseInt(query.regionId, 0);
    if (regionId > 0) {
      queryBuilder.andWhere('country.regionId = :regionId', { regionId });
    }

    // 热门城市筛选
    if (query.hotOnly) {
      queryBuilder.andWhere('city.isHot = :isHot', { isHot: 1 });
    }

    // 关键词搜索 - 多字段模糊匹配
    if (query.keyword) {
      queryBuilder.andWhere(
        `(
        city.cityName LIKE :keyword OR
        city.cityNameEn LIKE :keyword OR
        country.countryName LIKE :keyword OR
        country.countryNameEn LIKE :keyword OR
        region.regionName LIKE :keyword
      )`,
        { keyword: `%${query.keyword}%` },
      );

      // 智能排序：精确匹配 > 前缀匹配 > 包含匹配
      // 修复 CASE 语句，确保正确的别名引用
      queryBuilder.addOrderBy(
        `CASE
          WHEN city.cityName = :exactKeyword THEN 1
          WHEN city.cityName LIKE :prefixKeyword THEN 2
          WHEN city.cityNameEn = :exactKeyword THEN 3
          WHEN city.cityNameEn LIKE :prefixKeyword THEN 4
          WHEN country.countryName LIKE :prefixKeyword THEN 5
          ELSE 6
        END`,
        'ASC'
      );

      // 设置排序参数
      queryBuilder.setParameter('exactKeyword', query.keyword);
      queryBuilder.setParameter('prefixKeyword', `${query.keyword}%`);
    }

    // 热门城市优先
    queryBuilder.addOrderBy('city.isHot', 'DESC');
    queryBuilder.addOrderBy('city.sortOrder', 'ASC');
    queryBuilder.addOrderBy('city.id', 'ASC');

    // 分页
    const skip = (pageNum - 1) * pageSize;

    // 确保分页参数是有效的正整数
    if (!Number.isInteger(skip) || skip < 0 || !Number.isInteger(pageSize) || pageSize <= 0) {
      throw new Error(`Invalid pagination parameters: skip=${skip}, pageSize=${pageSize}`);
    }

    queryBuilder.skip(skip).take(pageSize);

    const [data, total] = await queryBuilder.getManyAndCount();

    // 格式化结果
    const rows: CitySearchResultItem[] = data.map((city) => ({
      id: city.id,
      cityName: city.cityName,
      cityNameEn: city.cityNameEn,
      countryId: city.countryId,
      countryName: city.country?.countryName,
      countryCode: city.country?.countryCode,
      regionId: city.country?.regionId,
      regionName: city.country?.region?.regionName,
      isHot: city.isHot,
      displayName: `${city.cityName} (${city.country?.countryName || ''})`,
    }));

    result = {
      rows,
      total,
      pageNum,
      pageSize,
    };

    await this.cacheManager.set(cacheKey, result, 300000);
    return result;
  }

  /**
   * 执行搜索的内部方法
   */
  private async performSearch(query: UnifiedSearchDto): Promise<LocationSearchResult> {
    const { searchType, keyword, regionId, countryId, hotOnly } = query;

    // 安全处理分页参数
    const pageNum = this.safeParseInt(query.pageNum, 1);
    const pageSize = this.safeParseInt(query.pageSize, 20);

    switch (searchType) {
      case 'region':
        return this.searchRegions({ keyword, pageNum, pageSize });
      case 'country':
        return this.searchCountries({ keyword, regionId, pageNum, pageSize });
      case 'city':
        return this.searchCities({ keyword, countryId, regionId, hotOnly, pageNum, pageSize });
      default:
        return this.searchAll(keyword, regionId, countryId, pageNum, pageSize, hotOnly);
    }
  }

  /**
   * 全局搜索
   */
  private async searchAll(keyword: string, regionId?: number, countryId?: number, pageNum = 1, pageSize = 20, hotOnly = false): Promise<LocationSearchResult> {
    // 安全处理分页参数
    const safePageNum = this.safeParseInt(pageNum, 1);
    const safePageSize = this.safeParseInt(pageSize, 20);

    // 优先搜索城市，然后是国家，最后是区域
    const cityResults = await this.searchCities({
      keyword,
      countryId,
      regionId,
      hotOnly,
      pageNum: safePageNum,
      pageSize: Math.min(safePageSize, 30), // 城市结果限制
    });

    if (cityResults.total > 0) {
      return cityResults;
    }

    // 如果没有城市结果，搜索国家
    const countryResults = await this.searchCountries({
      keyword,
      regionId,
      pageNum: safePageNum,
      pageSize: Math.min(safePageSize, 20),
    });

    if (countryResults.total > 0) {
      return countryResults;
    }

    // 最后搜索区域
    return this.searchRegions({
      keyword,
      pageNum: safePageNum,
      pageSize: Math.min(safePageSize, 10),
    });
  }

  /**
   * 清除搜索缓存
   */
  async clearSearchCache(): Promise<void> {
    // cache-manager v7 中移除了 reset 方法
    // 这里可以根据需要实现具体的缓存键清除逻辑
    // 目前暂时不实现，让缓存自然过期
    console.log('缓存清除功能暂未实现，缓存将自然过期');
  }
}
