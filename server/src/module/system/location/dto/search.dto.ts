import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

export class RegionSearchDto {
  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageNum?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number = 20;
}

export class CountrySearchDto {
  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: '区域ID' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  regionId?: number;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageNum?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number = 20;
}

export class CitySearchDto {
  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: '国家ID' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  countryId?: number;

  @ApiPropertyOptional({ description: '区域ID' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  regionId?: number;

  @ApiPropertyOptional({ description: '是否只返回热门城市' })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  hotOnly?: boolean = false;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageNum?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number = 50;
}

export class UnifiedSearchDto {
  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: '区域ID' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  regionId?: number;

  @ApiPropertyOptional({ description: '国家ID' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  countryId?: number;

  @ApiPropertyOptional({
    description: '搜索类型',
    enum: ['region', 'country', 'city', 'all'],
    default: 'all',
  })
  @IsOptional()
  @IsEnum(['region', 'country', 'city', 'all'])
  searchType?: string = 'all';

  @ApiPropertyOptional({ description: '是否只返回热门' })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  hotOnly?: boolean = false;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageNum?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number = 20;
}

// 搜索结果响应DTO
export class LocationSearchResult {
  @ApiPropertyOptional({ description: '数据列表' })
  rows: any[];

  @ApiPropertyOptional({ description: '总数' })
  total: number;

  @ApiPropertyOptional({ description: '页码' })
  pageNum: number;

  @ApiPropertyOptional({ description: '每页数量' })
  pageSize: number;
}

// 城市搜索结果项
export class CitySearchResultItem {
  @ApiPropertyOptional({ description: '城市ID' })
  id: number;

  @ApiPropertyOptional({ description: '城市名称' })
  cityName: string;

  @ApiPropertyOptional({ description: '城市英文名' })
  cityNameEn?: string;

  @ApiPropertyOptional({ description: '国家ID' })
  countryId: number;

  @ApiPropertyOptional({ description: '国家名称' })
  countryName?: string;

  @ApiPropertyOptional({ description: '国家代码' })
  countryCode?: string;

  @ApiPropertyOptional({ description: '区域ID' })
  regionId?: number;

  @ApiPropertyOptional({ description: '区域名称' })
  regionName?: string;

  @ApiPropertyOptional({ description: '是否热门城市' })
  isHot: number;

  @ApiPropertyOptional({ description: '显示名称' })
  displayName: string;
}

// 国家搜索结果项
export class CountrySearchResultItem {
  @ApiPropertyOptional({ description: '国家ID' })
  id: number;

  @ApiPropertyOptional({ description: '国家名称' })
  countryName: string;

  @ApiPropertyOptional({ description: '国家英文名' })
  countryNameEn?: string;

  @ApiPropertyOptional({ description: '国家代码' })
  countryCode?: string;

  @ApiPropertyOptional({ description: '区域ID' })
  regionId: number;

  @ApiPropertyOptional({ description: '区域名称' })
  regionName?: string;

  @ApiPropertyOptional({ description: '城市数量' })
  cityCount?: number;
}

// 区域搜索结果项
export class RegionSearchResultItem {
  @ApiPropertyOptional({ description: '区域ID' })
  id: number;

  @ApiPropertyOptional({ description: '区域名称' })
  regionName: string;

  @ApiPropertyOptional({ description: '国家数量' })
  countryCount?: number;
}
