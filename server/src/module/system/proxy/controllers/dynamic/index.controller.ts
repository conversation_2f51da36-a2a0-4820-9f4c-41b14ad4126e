import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/module/system/auth/guards/jwt-auth.guard';
import { RequirePermission } from '@/common/decorators/require-premission.decorator';
import { ProxyService } from '@module/system/proxy/proxy.service';

@ApiTags('系统管理 - 动态代理管理')
@Controller('system/proxy/dynamic')
@UseGuards(JwtAuthGuard)
export class DynamicProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  @Get()
  @ApiOperation({ summary: '获取动态代理Channel列表' })
  @RequirePermission('shop:proxy:query')
  @ApiResponse({ status: 200, description: '成功获取动态代理列表' })
  async getDynamicProxies(@Query('customerId') customerId?: number, @Query('channelName') channelName?: string, @Query('status') status?: string, @Query('page') page = 1, @Query('limit') limit = 10) {
    return this.proxyService.getProxyInstances({
      ipType: 'DYNAMIC',
      customerId,
      channelName,
      status,
      page,
      limit,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: '获取动态代理Channel详情' })
  @RequirePermission('shop:proxy:query')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功获取动态代理详情' })
  async getDynamicProxy(@Param('id') id: number) {
    return this.proxyService.getProxyInstanceById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新动态代理Channel设置' })
  @RequirePermission('shop:proxy:edit')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功更新Channel设置' })
  async updateDynamicProxy(
    @Param('id') id: number,
    @Body()
    updateDto: {
      channelName?: string;
      channelPassword?: string;
      limitTrafficGb?: number;
      enable?: boolean;
    },
  ) {
    return this.proxyService.updateDynamicProxy(id, updateDto);
  }

  @Get(':id/traffic')
  @ApiOperation({ summary: '获取动态代理Channel流量使用情况' })
  @RequirePermission('shop:proxy:query')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiQuery({ name: 'dateType', required: false, description: '日期类型: 0今天, 1昨天, 2最近7天, 3最近30天, 4自定义' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束日期 (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: '成功获取流量数据' })
  async getChannelTraffic(@Param('id') id: number, @Query('dateType') dateType?: number, @Query('startTime') startTime?: string, @Query('endTime') endTime?: string) {
    return this.proxyService.getChannelTraffic(id, { dateType: dateType?.toString(), startTime, endTime });
  }
  @Post(':id/endpoints')
  @ApiOperation({ summary: '为动态代理Channel生成Endpoints' })
  @RequirePermission('shop:proxy:edit')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功生成Endpoints' })
  async generateEndpoints(
    @Param('id') id: number,
    @Body()
    generateDto: {
      location: string;
      stickySessionTime: number;
      count: number;
      domain?: string;
      state?: string;
      city?: string;
    },
  ) {
    return this.proxyService.generateEndpoints(id, generateDto);
  }

  @Get(':id/endpoints')
  @ApiOperation({ summary: '获取动态代理Channel的Endpoints列表' })
  @RequirePermission('shop:proxy:query')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功获取Endpoints列表' })
  async getChannelEndpoints(@Param('id') id: number) {
    return this.proxyService.getChannelEndpoints(id);
  }

  @Get(':id/list-endpoints')
  @ApiOperation({ summary: '获取动态代理Channel的Endpoints列表 (别名)' })
  @RequirePermission('shop:proxy:query')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功获取Endpoints列表' })
  async listEndpoints(@Param('id') id: number) {
    return this.proxyService.getChannelEndpoints(id);
  }

  @Delete('endpoints/:endpointId')
  @ApiOperation({ summary: '删除Endpoint' })
  @RequirePermission('shop:proxy:remove')
  @ApiParam({ name: 'endpointId', description: 'Endpoint ID' })
  @ApiResponse({ status: 200, description: '成功删除Endpoint' })
  async deleteEndpoint(@Param('endpointId') endpointId: number) {
    return this.proxyService.deleteEndpoint(endpointId);
  }

  @Post(':id/refresh-status')
  @ApiOperation({ summary: '刷新动态代理Channel状态' })
  @RequirePermission('shop:proxy:edit')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功刷新状态' })
  async refreshDynamicProxyStatus(@Param('id') id: number) {
    return this.proxyService.refreshProxyStatus(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除动态代理Channel' })
  @RequirePermission('shop:proxy:remove')
  @ApiParam({ name: 'id', description: '代理实例ID' })
  @ApiResponse({ status: 200, description: '成功删除动态代理' })
  async deleteDynamicProxy(@Param('id') id: number) {
    return this.proxyService.deleteProxyInstance(id);
  }
}
