import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Kept for potential generic fallbacks, not primary config
import { lastValueFrom } from 'rxjs';
import { Provider } from '../../entities/provider.entity'; // Import Provider entity
import {
  ChannelTrafficResult,
  GenerateEndpointOptions,
  GetChannelTrafficOptions,
  GetInstanceDetailsOptions,
  InstanceDetailsResult,
  ProxyProvider,
  PurchaseInstanceOptions,
  PurchaseInstanceResult,
  RenewInstanceOptions,
  RenewInstanceResult,
  UpdateChannelOptions,
} from '../../interfaces/proxy-provider.interface';
import {
  IpnuxChannelCreateDto,
  IpnuxChannelDetailDto,
  IpnuxChannelTrafficDto,
  IpnuxGenerateEndpointPayload,
  IpnuxNodeInfo,
  IpnuxNodeListPayload,
  IpnuxPurchaseNewOrderDto,
  IpnuxPurchaseNewOrderPayload,
  IpnuxRenewOrderPayload,
} from './ipnux.interfaces';

@Injectable()
export class IpnuxService implements ProxyProvider {
  private readonly logger = new Logger(IpnuxService.name);

  // Configuration to be set by initialize()
  private initialized = false;
  private userId: string;
  private token: string;
  private baseUrl: string;
  private providerCode: string; // Store the provider code from the entity
  private ipnuxDefaultParams: Record<string, any> = {}; // For api_specific_params

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService, // May be used for global fallbacks or other configs
  ) {
    // Constructor should be lightweight. Heavy init logic moved to initialize().
  }

  /**
   * Initializes the IpnuxService with configuration from the database.
   */
  public initialize(configDetails: Record<string, any>, providerEntity?: Provider): void {
    if (!configDetails) {
      this.logger.error('IPNuxService initialization failed: configDetails is missing.');
      throw new InternalServerErrorException('IPNux provider configuration is missing.');
    }

    this.baseUrl = configDetails.api_base_url || this.configService.get<string>('IPNUX_API_BASE_URL_FALLBACK', 'https://api.ipnux.com/V1/OpenApi');

    const credentials = configDetails.credentials;
    if (!credentials || !credentials.user_id || !credentials.token) {
      this.logger.error('IPNuxService initialization failed: Credentials (user_id, token) are missing in configDetails.');
      throw new InternalServerErrorException('IPNux provider credentials (user_id, token) are missing.');
    }
    this.userId = credentials.user_id;
    this.token = credentials.token;

    this.ipnuxDefaultParams = configDetails.api_specific_params || {};

    if (providerEntity) {
      this.providerCode = providerEntity.providerCode;
    } else {
      // Fallback or error if providerEntity is critical and not provided
      this.logger.warn('Provider entity not provided during initialization. Provider code will be default.');
      this.providerCode = 'ipnux_default';
    }

    if (!this.userId || !this.token || !this.baseUrl) {
      this.logger.error('IPNuxService initialization failed: Essential parameters (userId, token, baseUrl) could not be set.');
      throw new InternalServerErrorException('Failed to initialize IPNux provider with essential parameters.');
    }

    this.initialized = true;
    this.logger.log(`IPNuxService for provider code '${this.providerCode}' initialized successfully.`);
  }

  private ensureInitialized(): void {
    if (!this.initialized) {
      this.logger.error('IPNuxService call attempted before initialization.');
      throw new InternalServerErrorException('IPNuxService is not initialized. Please call initialize first.');
    }
  }

  private async postApi<T>(endpoint: string, data: any): Promise<T> {
    this.ensureInitialized();
    try {
      const url = `${this.baseUrl}${endpoint}`;
      // UserId 和 Token 不再合并到 data 中
      // const fullPayload = {
      //   UserId: this.userId,
      //   Token: this.token,
      //   ...data,
      // };

      this.logger.debug(`Sending request to IPNux API for ${this.providerCode}: ${endpoint}`, {
        url,
        // data 是原始的请求体，不包含 UserId 和 Token
        payload: data,
        headers: {
          // + 新增记录 headers 到日志
          UserId: this.userId,
          Token: '[REDACTED]', // Token 仍然不在日志中明文显示
          'Content-Type': 'application/json',
        },
      });

      // 将 UserId 和 Token 添加到请求头
      const headers = {
        UserId: this.userId,
        Token: this.token,
        'Content-Type': 'application/json', // 确保 Content-Type 也被设置
      };

      // 发送 POST 请求，第三个参数是 AxiosRequestConfig，可以用来传递 headers
      const response = await lastValueFrom(
        this.httpService.post<{ Code: number; Message: string; Data: T }>(
          url,
          data, // 请求体现在只是原始的 data
          { headers: headers }, // + 将 headers 作为配置传递
        ),
      );

      // this.logger.debug(`IPNux API response for ${this.providerCode}: ${endpoint}`, {
      //   response: response.data,
      // });

      // IPNux specific error codes: 1000 is success.
      if (response.data.Code !== 1000) {
        this.logger.warn(`IPNux API error for ${this.providerCode} at ${endpoint}: [${response.data.Code}] ${response.data.Message}`, response.data);
        throw new Error(`IPNux API error: [${response.data.Code}] ${response.data.Message}`);
      }

      return response.data.Data;
    } catch (error) {
      this.logger.error(`Error calling IPNux API ${endpoint} for ${this.providerCode}: ${error.message}`, error.stack);
      throw error; // Re-throw to be handled by the calling service method
    }
  }

  public getProviderCode(): string {
    // this.ensureInitialized(); // Not strictly necessary for getProviderCode if set during init
    return this.providerCode || 'ipnux'; // Fallback if providerCode wasn't set
  }

  public async testConnection(): Promise<{ success: boolean; message?: string; details?: any }> {
    this.ensureInitialized();
    try {
      // A simple read operation, e.g., get user traffic for today (should be benign)
      const trafficInfo = await this.postApi<IpnuxChannelTrafficDto>('/OpenApiGetUseTraffic', {
        ChannelId: 0, // All channels for the user
        DateType: 0, // Today
      });
      return {
        success: true,
        message: 'Successfully connected to IPNux API and fetched user traffic info.',
        details: { totalTrafficGbToday: trafficInfo?.UseTrafficGb?.TotalTrafficGb },
      };
    } catch (error) {
      this.logger.error(`IPNux connection test failed for ${this.providerCode}: ${error.message}`, error.stack);
      return { success: false, message: error.message };
    }
  }

  async purchaseInstances(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    this.ensureInitialized();
    if (options.productMode === 'STATIC') {
      return this.purchaseStaticInstances(options);
    } else if (options.productMode === 'DYNAMIC') {
      return this.purchaseDynamicChannel(options);
    }
    throw new BadRequestException('Invalid product mode for purchase.');
  }

  private async purchaseStaticInstances(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    // Merge defaults from initialization with product-specific overrides
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };

    const buyData = [
      {
        city_name: options.targetLocation?.city || effectiveConfig.default_city || 'Los Angeles',
        count: options.quantity,
      },
    ];
    const payload: IpnuxPurchaseNewOrderPayload = {
      proxies_type: options.externalProductId, // This is IPNux's product type
      proxies_format: effectiveConfig.proxies_format || 2,
      purpose_web: effectiveConfig.purpose_web || 'tiktok',
      time_period: options.durationDays,
      currency: effectiveConfig.currency || 'USD',
      udp_status: effectiveConfig.udp_status || false,
      protocols_type: effectiveConfig.protocols_type || 3,
      buyData: buyData,
    };
    const response = await this.postApi<IpnuxPurchaseNewOrderDto>('/OpenApiPurchaseNewOrder', payload);
    if (!response || !response.NodeInfos || response.NodeInfos.length === 0) {
      throw new InternalServerErrorException('IPNux static purchase did not return any NodeInfos.');
    }
    return response.NodeInfos.map((node: IpnuxNodeInfo) => ({
      providerInstanceId: node.proxy_id.toString(),
      providerOrderId: response.OrderNo,
      ipAddress: node.proxy_address.split(':')[0],
      port: parseInt(node.proxy_address.split(':')[1], 10),
      username: node.username,
      password: node.password,
      protocols: this.mapIpnuxProtocolTypeToString(node.protocols_type),
      countryCode: node.country_code,
      cityName: node.city_name,
      activatedAt: new Date(),
      expiresAt: new Date(node.expire_time_data),
      isUnlimited: true,
      metadata: { isp: node.isp, ip_type_name: node.ip_type_name },
    }));
  }

  private async purchaseDynamicChannel(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };
    const payload = {
      ChannelName: options.channelName || `Channel_${options.customerId}_${Date.now()}`,
      ChannelLimitTrafficGb: options.limitTrafficGb || effectiveConfig.default_limit_traffic_gb || 0,
    };
    const response = await this.postApi<IpnuxChannelCreateDto>('/OpenApiCreateChannel', payload);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + options.durationDays); // Expiry managed by our system
    return [
      {
        providerInstanceId: response.ChannelId.toString(),
        channelName: response.ChannelName,
        password: response.ChannelPassword,
        activatedAt: new Date(response.CreateTime),
        expiresAt: expiresAt,
        totalFlowMb: response.ChannelLimitTrafficGb > 0 ? response.ChannelLimitTrafficGb * 1024 : undefined,
        isUnlimited: response.ChannelLimitTrafficGb === 0,
        metadata: { ChannelStatus: response.ChannelStatus, UsedTraffic: response.UsedTraffic },
      },
    ];
  }

  private mapIpnuxProtocolTypeToString(protocolType: number): string {
    if (protocolType === 1) return 'HTTP';
    if (protocolType === 2) return 'SOCKS5';
    if (protocolType === 3) return 'HTTP,SOCKS5';
    return 'UNKNOWN';
  }

  // mapStringToIpnuxProtocolType - kept as is

  async renewInstance(options: RenewInstanceOptions): Promise<RenewInstanceResult> {
    this.ensureInitialized();
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };

    if (options.productMode === 'STATIC') {
      const payload: IpnuxRenewOrderPayload = {
        proxies_ids: [parseInt(options.providerInstanceId, 10)],
        time_period: options.durationDays,
        currency: effectiveConfig.currency || 'USD', // Use configured default
      };
      const response = await this.postApi<any>('/OpenApiNoneRenew', payload); // Assuming API endpoint
      const newExpiresAt = new Date(options.currentExpiresAt);
      newExpiresAt.setDate(newExpiresAt.getDate() + options.durationDays);
      return {
        newExpiresAt: newExpiresAt,
        providerOrderId: response?.OrderNo,
        metadata: { renewalResponse: response },
      };
    } else if (options.productMode === 'DYNAMIC') {
      this.logger.log(`Renewing dynamic channel ${options.providerInstanceId} in local system for ${this.providerCode}.`);
      const newExpiresAt = new Date(options.currentExpiresAt);
      newExpiresAt.setDate(newExpiresAt.getDate() + options.durationDays);
      return { newExpiresAt };
    }
    throw new BadRequestException('Invalid product mode for renewal.');
  }

  async getInstanceDetails(options: GetInstanceDetailsOptions): Promise<InstanceDetailsResult | null> {
    this.ensureInitialized();
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };

    if (options.productMode === 'STATIC') {
      const proxies_type = options.externalProductId; // This is IPNux's product type
      const proxies_format = effectiveConfig.proxies_format || 2;

      if (!proxies_type) {
        this.logger.error(`proxies_type (externalProductId) is required for IPNux GetInstanceDetails (Static) for ${this.providerCode}. ProviderInstanceId: ${options.providerInstanceId}`);
        throw new BadRequestException('proxies_type (externalProductId) is required for IPNux GetInstanceDetails (Static).');
      }
      const listPayload: IpnuxNodeListPayload = {
        proxy_ids: [parseInt(options.providerInstanceId, 10)],
        proxies_type: proxies_type,
        proxies_format: proxies_format,
        page: 1,
        limit: 1,
      };
      const response = await this.postApi<{ NodeInfos: IpnuxNodeInfo[]; Total: number }>('/OpenApiNodeList', listPayload);
      if (response?.NodeInfos?.length > 0) {
        const node = response.NodeInfos[0];
        return {
          /* mapping as before */ providerInstanceId: node.proxy_id.toString(),
          ipAddress: node.proxy_address.split(':')[0],
          port: parseInt(node.proxy_address.split(':')[1], 10),
          username: node.username,
          protocols: this.mapIpnuxProtocolTypeToString(node.protocols_type),
          countryCode: node.country_code,
          cityName: node.city_name,
          expiresAt: new Date(node.expire_time_data),
          status: this.mapIpnuxStaticStatus(node.status),
          isUnlimited: true,
          metadata: { isp: node.isp, ip_type_name: node.ip_type_name, last_heartbeat_time: node.last_heartbeat_time },
        };
      }
    } else if (options.productMode === 'DYNAMIC') {
      const listPayload = { ChannelId: parseInt(options.providerInstanceId, 10) };
      const response = await this.postApi<IpnuxChannelDetailDto[]>('/OpenApiSubUsers', listPayload);
      const channelInfo = response.find((ch) => ch.ChannelId.toString() === options.providerInstanceId);
      if (channelInfo) {
        return {
          /* mapping as before */ providerInstanceId: channelInfo.ChannelId.toString(),
          channelName: channelInfo.ChannelName,
          password: channelInfo.ChannelPassword,
          status: this.mapIpnuxDynamicStatus(channelInfo.ChannelStatus),
          totalFlowMb: channelInfo.LimieTrafficGb > 0 ? channelInfo.LimieTrafficGb * 1024 : undefined,
          usedFlowMb: channelInfo.UseTrafficGb * 1024,
          isUnlimited: channelInfo.LimieTrafficGb === 0,
          metadata: { CreateTime: channelInfo.CreateTime },
        };
      }
    }
    return null;
  }

  private mapIpnuxStaticStatus(status: number | string): string {
    const statusCode = typeof status === 'string' ? parseInt(status, 10) : status;

    switch (statusCode) {
      case 1:
        return 'ACTIVE';
      case 2:
        return 'EXPIRED';
      case 3:
        return 'DISABLED';
      default:
        return 'UNKNOWN';
    }
  }

  private mapIpnuxDynamicStatus(status: number | string): string {
    const statusCode = typeof status === 'string' ? parseInt(status, 10) : status;

    switch (statusCode) {
      case 1:
        return 'ACTIVE';
      case 2:
        return 'DISABLED';
      case 3:
        return 'EXPIRED';
      default:
        return 'UNKNOWN';
    }
  }

  async replaceInstance(providerInstanceId: string, options: { targetLocation?: any; productConfigDetails?: Record<string, any> }): Promise<PurchaseInstanceResult> {
    this.ensureInitialized();
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };
    const proxies_type = options.productConfigDetails?.externalProductId; // externalProductId is proxies_type

    if (!proxies_type) throw new BadRequestException('proxies_type (externalProductId) is required for replaceInstance.');

    const payload = {
      proxies_ids: [parseInt(providerInstanceId, 10)],
      proxies_type: proxies_type,
      proxies_format: effectiveConfig.proxies_format || 2,
      new_city_name: options.targetLocation?.city,
      new_country_code: options.targetLocation?.country_code,
    };
    const responseNode = await this.postApi<IpnuxNodeInfo>('/OpenApiSwitchNode', payload);
    if (!responseNode) throw new InternalServerErrorException('IPNux replace did not return valid NodeInfo.');
    return {
      /* mapping as before */ providerInstanceId: responseNode.proxy_id.toString(),
      ipAddress: responseNode.proxy_address.split(':')[0],
      port: parseInt(responseNode.proxy_address.split(':')[1], 10),
      username: responseNode.username,
      password: responseNode.password,
      protocols: this.mapIpnuxProtocolTypeToString(responseNode.protocols_type),
      countryCode: responseNode.country_code,
      cityName: responseNode.city_name,
      activatedAt: new Date(),
      expiresAt: new Date(responseNode.expire_time_data),
      isUnlimited: true,
      metadata: { isp: responseNode.isp, ip_type_name: responseNode.ip_type_name },
    };
  }

  async updateInstanceCredentials(providerInstanceId: string, credentials: { username?: string; password?: string }, productConfigDetails?: Record<string, any>): Promise<boolean> {
    this.logger.log(`[SYSTEM MODULE] updateInstanceCredentials called for instance ${providerInstanceId}`);
    this.ensureInitialized();
    const proxies_type = productConfigDetails?.externalProductId;
    if (!proxies_type) throw new BadRequestException('proxies_type (externalProductId) is required for updateInstanceCredentials.');
    if (!credentials.username || !credentials.password) throw new BadRequestException('Username and password are required.');

    const payload = {
      proxy_ids: [parseInt(providerInstanceId, 10)],
      proxies_type: proxies_type,
      username: credentials.username,
      password: credentials.password,
    };
    const responseData = await this.postApi<{ Data: boolean }>('/OpenApiEditNodeUserAndPass', payload);
    return responseData.Data; // IPNux returns { Data: true/false }
  }

  async updateInstanceWhitelist(providerInstanceId: string, whitelistedIps: string[], productConfigDetails?: Record<string, any>): Promise<boolean> {
    this.ensureInitialized();
    const proxies_type = productConfigDetails?.externalProductId;
    if (!proxies_type) throw new BadRequestException('proxies_type (externalProductId) is required for updateInstanceWhitelist.');

    const payload = {
      proxy_ids: [parseInt(providerInstanceId, 10)],
      proxies_type: proxies_type,
      ips: whitelistedIps.join(','),
    };
    const responseData = await this.postApi<{ Data: boolean }>('/OpenApiEditNodeIpWhiteList', payload);
    return responseData.Data;
  }

  async updateChannel(options: UpdateChannelOptions): Promise<boolean> {
    this.ensureInitialized();
    // const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };
    // No specific params seem to be used here from productConfigDetails in original, directly use options.
    const payload = {
      ChannelId: parseInt(options.providerInstanceId, 10),
      ChannelName: options.channelName,
      ChannelLimitTrafficGb: options.limitTrafficGb,
      Enable: options.enable,
      ChannelPassword: options.channelPassword,
    };
    const response = await this.postApi<{ Data: boolean }>('/OpenApiEditChannel', payload);
    return response.Data;
  }

  async getChannelTraffic(options: GetChannelTrafficOptions): Promise<ChannelTrafficResult> {
    this.ensureInitialized();
    // const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };
    const payload = {
      ChannelId: parseInt(options.providerInstanceId, 10) || 0,
      DateType: options.dateType || this.ipnuxDefaultParams.default_traffic_date_type || 0,
      StartTime: options.startTime,
      EndTime: options.endTime,
    };
    const response = await this.postApi<IpnuxChannelTrafficDto>('/OpenApiGetUseTraffic', payload);
    return {
      items: response.Items.map((item) => ({
        channelId: item.ChannelId,
        useTraffic: item.UseTraffic,
        useTime: new Date(item.UseTime),
      })),
      totalTrafficGb: response.UseTrafficGb.TotalTrafficGb,
    };
  }

  async generateEndpoints(options: GenerateEndpointOptions): Promise<string[]> {
    this.ensureInitialized();
    // Merge defaults from initialization with product-specific overrides from options
    const effectiveConfig = { ...this.ipnuxDefaultParams, ...(options.productConfigDetails?.ipnux_specific_params || {}) };

    const payload: IpnuxGenerateEndpointPayload = {
      ChannelId: parseInt(options.providerInstanceId, 10),
      Location: options.location,
      StickySessionTime: options.stickySessionTime,
      Count: options.count,
      Domain: options.domain || effectiveConfig.default_endpoint_domain || 'Global',
      State: options.state,
      City: options.city,
      ProtocolsType: effectiveConfig.protocols_type || 3, // Use default from config or hardcoded
    };
    const response = await this.postApi<string[]>('/OpenApiGenerateCustomEndpoints', payload);
    return response;
  }

  async listEndpoints(providerInstanceId: string): Promise<any[]> {
    this.logger.warn(
      `listEndpoints is not directly supported by the IPNux API. It should be implemented by querying the local 'shop_proxy_endpoints' table for providerInstanceId: ${providerInstanceId}.`,
    );
    // This implementation should be done in the calling service (e.g., DynamicProxyService)
    // which has access to the database repository.
    return Promise.resolve([]);
  }

  async deleteEndpoint(endpointProviderId: string): Promise<boolean> {
    this.logger.warn(
      `deleteEndpoint is not directly supported by the IPNux API. It should be implemented by deleting from the local 'shop_proxy_endpoints' table using the endpoint's unique ID (not provider-specific ID): ${endpointProviderId}.`,
    );
    // This implementation should be done in the calling service (e.g., DynamicProxyService)
    return Promise.resolve(true); // Assume success as there's no API call to fail
  }
}
