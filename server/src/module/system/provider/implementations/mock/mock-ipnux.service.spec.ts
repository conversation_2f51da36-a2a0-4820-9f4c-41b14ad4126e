import { MockIpnuxService } from './mock-ipnux.service';
import { Provider, ProviderType } from '../../entities/provider.entity';

describe('System MockIpnuxService', () => {
  let service: MockIpnuxService;

  beforeEach(() => {
    service = new MockIpnuxService();
  });

  describe('initialization guard', () => {
    it('should throw error if testConnection called before initialize', async () => {
      await expect(service.testConnection()).rejects.toThrow(
        'MockIpnuxService is not initialized',
      );
    });

    it('should throw error if purchaseInstances called before initialize', async () => {
      await expect(service.purchaseInstances({
        productMode: 'STATIC',
        quantity: 1,
        durationDays: 1,
        orderId: 'test',
        customerId: 1,
      } as any)).rejects.toThrow(
        'MockIpnuxService is not initialized',
      );
    });
  });

  describe('initialization', () => {
    it('should initialize with config details', () => {
      const configDetails = { apiKey: 'test', baseUrl: 'http://test.com' };
      
      expect(() => service.initialize(configDetails)).not.toThrow();
      expect(service.getProviderCode()).toBe('ipnux_mock');
    });

    it('should initialize with provider entity', () => {
      const provider = new Provider();
      provider.providerCode = 'system_test_provider';
      provider.providerType = ProviderType.API;
      
      service.initialize({}, provider);
      expect(service.getProviderCode()).toBe('system_test_provider');
    });

    it('should default provider code when no entity provided', () => {
      service.initialize({});
      expect(service.getProviderCode()).toBe('ipnux_mock');
    });
  });

  describe('after initialize', () => {
    beforeEach(() => {
      service.initialize({ some: 'config' });
    });

    describe('testConnection', () => {
      it('should return successful connection test with system mode', async () => {
        const result = await service.testConnection();
        
        expect(result.success).toBe(true);
        expect(result.message).toBe('Mock connection successful.');
        expect(result.details?.mode).toBe('mock');
      });
    });

    describe('purchaseInstances STATIC', () => {
      it('should return correct quantity with system-specific IDs and ports', async () => {
        const quantity = 2;
        const results = await service.purchaseInstances({
          productMode: 'STATIC',
          quantity,
          durationDays: 10,
          orderId: 'sys_order123',
          customerId: 1,
          id: 1,
          externalProductId: 'ext_123',
          productConfigDetails: {}
        });

        expect(results).toHaveLength(quantity);
        results.forEach((item, idx) => {
          expect(item.providerInstanceId).toMatch(/^mock_inst_\d+_\d+$/);
          expect(item.port).toBe(8000 + idx);
          expect(item.providerOrderId).toMatch(/^mock_order_\d+$/);
          expect(item.ipAddress).toMatch(/^192\.168\.\d+\.\d+$/);
          expect(item.username).toContain('user_');
          expect(item.password).toContain('pass_');
          expect(item.protocols).toBe('HTTP,SOCKS5');
          expect(item.continentName).toBe('North America');
          expect(item.isUnlimited).toBe(true);
          expect(item.metadata?.mock_instance).toBe(true);
          expect(item.metadata?.isp).toBe('Mock ISP');
        });
      });

      it('should use default location (Los Angeles) for system module', async () => {
        const results = await service.purchaseInstances({
          productMode: 'STATIC',
          quantity: 1,
          durationDays: 7,
          orderId: 'sys_order456',
          customerId: 2,
          id: 1,
          externalProductId: 'ext_456',
          productConfigDetails: {}
        });

        expect(results[0].countryCode).toBe('US');
        expect(results[0].cityName).toBe('Los Angeles');
      });
    });

    describe('purchaseInstances DYNAMIC', () => {
      it('should return single channel with system-specific structure', async () => {
        const [channel] = await service.purchaseInstances({
          productMode: 'DYNAMIC',
          quantity: 1,
          durationDays: 15,
          orderId: 'sys_order789',
          customerId: 50,
          id: 2,
          externalProductId: 'ext_channel',
          productConfigDetails: {}
        });

        expect(channel.providerInstanceId).toMatch(/^mock_channel_\d+$/);
        expect(channel.channelName).toContain('MockChannel_50_');
        expect(channel.password).toMatch(/^mock_pass_\w{8}$/);
        expect(channel.isUnlimited).toBe(true);
        expect(channel.totalFlowMb).toBeUndefined();
        expect(channel.metadata?.mock_instance).toBe(true);
        expect(channel.metadata?.ChannelStatus).toBe(1);
        expect(channel.metadata?.UsedTraffic).toBe(0);
      });

      it('should handle traffic limit correctly', async () => {
        const [channel] = await service.purchaseInstances({
          productMode: 'DYNAMIC',
          quantity: 1,
          durationDays: 20,
          orderId: 'sys_order_limited',
          customerId: 60,
          id: 2,
          externalProductId: 'ext_limited',
          productConfigDetails: {},
          limitTrafficGb: 25
        });

        expect(channel.totalFlowMb).toBe(25 * 1024);
        expect(channel.isUnlimited).toBe(false);
      });
    });

    describe('purchaseInstances error handling', () => {
      it('should throw error for invalid product mode', async () => {
        await expect(
          service.purchaseInstances({
            productMode: 'INVALID' as any,
            quantity: 1,
            durationDays: 1,
            orderId: 'sys_order_invalid',
            customerId: 1,
            id: 1,
            externalProductId: 'ext_invalid',
            productConfigDetails: {}
          }),
        ).rejects.toThrow('Invalid product mode for mock purchase');
      });
    });

    describe('getInstanceDetails', () => {
      it('should return null for non-mock instance ID', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'shop_mock_inst_123',
          productMode: 'STATIC',
        });

        expect(details).toBeNull();
      });

      it('should return static instance details for mock instance', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'mock_inst_123456_0',
          productMode: 'STATIC',
        });

        expect(details).not.toBeNull();
        expect(details?.providerInstanceId).toBe('mock_inst_123456_0');
        expect(details?.port).toBe(8000);
        expect(details?.ipAddress).toMatch(/^192\.168\.\d+\.\d+$/);
        expect(details?.username).toContain('user_');
        expect(details?.status).toBe('ACTIVE');
        expect(details?.isUnlimited).toBe(true);
        expect(details?.metadata?.mock_instance).toBe(true);
      });

      it('should return dynamic instance details for mock channel', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'mock_channel_123456',
          productMode: 'DYNAMIC',
        });

        expect(details).not.toBeNull();
        expect(details?.providerInstanceId).toBe('mock_channel_123456');
        expect(details?.channelName).toContain('MockChannel_');
        expect(details?.password).toMatch(/^mock_pass_\w{8}$/);
        expect(details?.status).toBe('ACTIVE');
        expect(details?.totalFlowMb).toBe(10 * 1024);
        expect(details?.usedFlowMb).toBeGreaterThanOrEqual(0);
        expect(details?.usedFlowMb).toBeLessThan(1024);
        expect(details?.isUnlimited).toBe(false);
        expect(details?.metadata?.mock_instance).toBe(true);
      });
    });

    describe('renewInstance', () => {
      it('should extend expiry correctly', async () => {
        const currentDate = new Date('2023-06-01T00:00:00.000Z');
        const duration = 14;

        const result = await service.renewInstance({
          providerInstanceId: 'mock_inst_xyz',
          currentExpiresAt: currentDate,
          durationDays: duration,
          productMode: 'STATIC',
        });

        const expectedDate = new Date(currentDate);
        expectedDate.setDate(expectedDate.getDate() + duration);

        expect(result.newExpiresAt).toEqual(expectedDate);
        expect(result.providerOrderId).toMatch(/^mock_renew_\d+$/);
        expect(result.metadata?.renewalType).toBe('mock');
        expect(result.metadata?.originalExpiry).toBe(currentDate.toISOString());
        expect(result.metadata?.productMode).toBe('STATIC');
      });
    });

    describe('replaceInstance', () => {
      it('should replace instance with system-specific mock data', async () => {
        const originalId = 'mock_inst_123_0';
        
        const result = await service.replaceInstance!(originalId, {
          targetLocation: { country: 'FR', city: 'Paris' }
        });

        expect(result.providerInstanceId).toMatch(/^mock_inst_\d+_replaced$/);
        expect(result.ipAddress).toMatch(/^192\.168\.\d+\.\d+$/);
        expect(result.port).toBeGreaterThanOrEqual(8000);
        expect(result.port).toBeLessThan(9000);
        expect(result.countryCode).toBe('FR');
        expect(result.cityName).toBe('Paris');
        expect(result.metadata?.replacedFrom).toBe(originalId);
        expect(result.metadata?.mock_instance).toBe(true);
      });
    });

    describe('updateInstanceCredentials', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateInstanceCredentials!(
          'mock_inst_123',
          { username: 'newuser', password: 'newpass' }
        );

        expect(result).toBe(true);
      });
    });

    describe('updateInstanceWhitelist', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateInstanceWhitelist!(
          'mock_inst_123',
          ['***********', '********']
        );

        expect(result).toBe(true);
      });
    });

    describe('updateChannel', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateChannel!({
          providerInstanceId: 'mock_channel_123',
          channelName: 'UpdatedChannelName',
          limitTrafficGb: 75,
          enable: true
        });

        expect(result).toBe(true);
      });
    });

    describe('getChannelTraffic', () => {
      it('should return mock traffic data with system-specific values', async () => {
        const result = await service.getChannelTraffic!({
          providerInstanceId: 'mock_channel_123'
        });

        expect(result.items).toHaveLength(7);
        expect(result.totalTrafficGb).toBeGreaterThan(0);
        
        result.items.forEach(item => {
          expect(item.channelId).toBe('mock_channel_123');
          expect(item.useTraffic).toBeGreaterThanOrEqual(0);
          expect(item.useTraffic).toBeLessThan(1000);
          expect(item.useTime).toBeInstanceOf(Date);
        });
      });
    });

    describe('generateEndpoints', () => {
      it('should generate endpoints with system-specific port range', async () => {
        const count = 3;
        const result = await service.generateEndpoints!({
          providerInstanceId: 'mock_channel_123',
          location: 'US',
          stickySessionTime: 900,
          count,
          productConfigDetails: {}
        });

        expect(result).toHaveLength(count);
        result.forEach((endpoint, idx) => {
          expect(endpoint).toMatch(/^mock-proxy\.example\.com:1000\d$/);
          expect(endpoint).toBe(`mock-proxy.example.com:${10000 + idx}`);
        });
      });

      it('should use custom domain when provided', async () => {
        const customDomain = 'system.proxy.com';
        const result = await service.generateEndpoints!({
          providerInstanceId: 'mock_channel_456',
          location: 'GB',
          stickySessionTime: 1800,
          count: 1,
          domain: customDomain,
          productConfigDetails: {}
        });

        expect(result).toHaveLength(1);
        expect(result[0]).toBe(`${customDomain}:10000`);
      });
    });
  });
});