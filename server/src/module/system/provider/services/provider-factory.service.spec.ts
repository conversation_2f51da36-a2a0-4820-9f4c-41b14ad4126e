import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ProviderFactoryService } from './provider-factory.service';
import { Provider, ProviderType } from '../entities/provider.entity';
import { IpnuxService } from '../implementations/ipnux/ipnux.service';
import { MockIpnuxService } from '../implementations/mock/mock-ipnux.service';

describe('System ProviderFactoryService', () => {
  let service: ProviderFactoryService;
  let configService: jest.Mocked<ConfigService>;
  let moduleRef: jest.Mocked<ModuleRef>;
  let mockIpnuxService: jest.Mocked<IpnuxService>;
  let mockSystemMockIpnuxService: jest.Mocked<MockIpnuxService>;

  beforeEach(async () => {
    mockIpnuxService = {
      initialize: jest.fn(),
    } as any;

    mockSystemMockIpnuxService = {
      initialize: jest.fn(),
    } as any;

    const mockConfigService = {
      get: jest.fn(),
    } as any;

    const mockModuleRef = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProviderFactoryService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<ProviderFactoryService>(ProviderFactoryService);
    configService = module.get(ConfigService);
    moduleRef = module.get(ModuleRef);
  });

  describe('getProvider parameter validation', () => {
    it('should throw BadRequestException for null provider entity', () => {
      expect(() => service.getProvider(null as any)).toThrow(BadRequestException);
      expect(() => service.getProvider(null as any)).toThrow(
        '必须提供完整的Provider实体对象'
      );
    });

    it('should throw BadRequestException for non-object provider entity', () => {
      expect(() => service.getProvider('string' as any)).toThrow(BadRequestException);
      expect(() => service.getProvider('string' as any)).toThrow(
        '必须提供完整的Provider实体对象'
      );
    });

    it('should throw BadRequestException for provider entity without providerType', () => {
      const provider = { providerCode: 'ipnux' } as Provider;
      
      expect(() => service.getProvider(provider)).toThrow(BadRequestException);
      expect(() => service.getProvider(provider)).toThrow(
        'Provider实体缺少providerType字段'
      );
    });
  });

  describe('getProvider with API provider type', () => {
    let validProvider: Provider;

    beforeEach(() => {
      validProvider = {
        providerCode: 'ipnux',
        providerType: ProviderType.API,
        configDetails: { apiKey: 'test-key', baseUrl: 'https://api.ipnux.com' }
      } as any;
    });

    describe('when MOCK_IPNUX_PROVIDER is enabled', () => {
      beforeEach(() => {
        configService.get.mockImplementation((key: string) => {
          if (key === 'MOCK_IPNUX_PROVIDER') return 'true';
          return undefined;
        });
        moduleRef.get.mockImplementation((token: string) => {
          if (token === 'MockIpnuxService') return mockSystemMockIpnuxService;
          throw new Error(`Unexpected token: ${token}`);
        });
      });

      it('should return MockIpnuxService and call initialize', () => {
        const result = service.getProvider(validProvider);

        expect(result).toBe(mockSystemMockIpnuxService);
        expect(moduleRef.get).toHaveBeenCalledWith('MockIpnuxService', { strict: false });
        expect(mockSystemMockIpnuxService.initialize).toHaveBeenCalledWith(
          validProvider.configDetails,
          validProvider
        );
      });
    });

    describe('when MOCK_IPNUX_PROVIDER is disabled', () => {
      beforeEach(() => {
        configService.get.mockImplementation((key: string) => {
          if (key === 'MOCK_IPNUX_PROVIDER') return 'false';
          return undefined;
        });
        moduleRef.get.mockImplementation((token: string) => {
          if (token === 'IpnuxService') return mockIpnuxService;
          throw new Error(`Unexpected token: ${token}`);
        });
      });

      it('should return real IpnuxService and call initialize', () => {
        const result = service.getProvider(validProvider);

        expect(result).toBe(mockIpnuxService);
        expect(moduleRef.get).toHaveBeenCalledWith('IpnuxService', { strict: false });
        expect(mockIpnuxService.initialize).toHaveBeenCalledWith(
          validProvider.configDetails,
          validProvider
        );
      });
    });

    describe('when provider code is not ipnux', () => {
      it('should throw NotFoundException for non-ipnux API provider', () => {
        const nonIpnuxProvider = {
          ...validProvider,
          providerCode: 'other_api'
        };

        expect(() => service.getProvider(nonIpnuxProvider)).toThrow(NotFoundException);
        expect(() => service.getProvider(nonIpnuxProvider)).toThrow(
          'No specific API service configured for provider code "other_api"'
        );
      });
    });

    describe('service initialization', () => {
      beforeEach(() => {
        configService.get.mockReturnValue('false');
        moduleRef.get.mockReturnValue(mockIpnuxService);
      });

      it('should pass empty config when configDetails is undefined', () => {
        const providerWithoutConfig = {
          providerCode: 'ipnux',
          providerType: ProviderType.API,
          configDetails: undefined
        } as Provider;

        service.getProvider(providerWithoutConfig);

        expect(mockIpnuxService.initialize).toHaveBeenCalledWith({}, providerWithoutConfig);
      });

      it('should warn when service does not have initialize method', () => {
        const serviceWithoutInit = {} as any;
        moduleRef.get.mockReturnValue(serviceWithoutInit);

        const loggerSpy = jest.spyOn((service as any).logger, 'warn');

        const result = service.getProvider(validProvider);

        expect(result).toBe(serviceWithoutInit);
        expect(loggerSpy).toHaveBeenCalledWith(
          expect.stringContaining('does not have an initialize method')
        );
      });
    });
  });

  describe('getProvider with SELF_HOSTED provider type', () => {
    it('should throw NotFoundException for SELF_HOSTED provider type', () => {
      const selfHostedProvider = {
        providerCode: 'self_hosted',
        providerType: ProviderType.SELF_HOSTED,
        configDetails: {}
      } as Provider;

      expect(() => service.getProvider(selfHostedProvider)).toThrow(NotFoundException);
      expect(() => service.getProvider(selfHostedProvider)).toThrow(
        'Provider type "SELF_HOSTED" for "self_hosted" is not yet implemented'
      );
    });
  });

  describe('getProvider with unsupported provider type', () => {
    it('should throw NotFoundException for unsupported provider type', () => {
      const unsupportedProvider = {
        providerCode: 'test',
        providerType: 'UNSUPPORTED' as ProviderType,
        configDetails: {}
      } as Provider;

      expect(() => service.getProvider(unsupportedProvider)).toThrow(NotFoundException);
      expect(() => service.getProvider(unsupportedProvider)).toThrow(
        'Provider type "UNSUPPORTED" for "test" is not supported or not found'
      );
    });
  });

  describe('error handling', () => {
    let validProvider: Provider;

    beforeEach(() => {
      validProvider = {
        providerCode: 'ipnux',
        providerType: ProviderType.API,
        configDetails: { apiKey: 'test' }
      } as any;
      configService.get.mockReturnValue('false');
    });

    it('should throw InternalServerErrorException when moduleRef.get returns null', () => {
      moduleRef.get.mockReturnValue(null);

      expect(() => service.getProvider(validProvider)).toThrow(InternalServerErrorException);
      expect(() => service.getProvider(validProvider)).toThrow(
        'Could not resolve service instance for provider "ipnux" with type "API"'
      );
    });

    it('should re-throw NotFoundException from moduleRef.get', () => {
      moduleRef.get.mockImplementation(() => {
        throw new NotFoundException('Service not found');
      });

      expect(() => service.getProvider(validProvider)).toThrow(NotFoundException);
    });

    it('should wrap generic errors in InternalServerErrorException', () => {
      moduleRef.get.mockImplementation(() => {
        throw new Error('Generic error');
      });

      expect(() => service.getProvider(validProvider)).toThrow(InternalServerErrorException);
      expect(() => service.getProvider(validProvider)).toThrow(
        'Failed to get provider service for "ipnux": Generic error'
      );
    });
  });

  describe('getAllProviders', () => {
    beforeEach(() => {
      moduleRef.get.mockImplementation((token: string) => {
        if (token === 'IpnuxService') return mockIpnuxService;
        throw new Error(`Unexpected token: ${token}`);
      });
    });

    it('should return array with IpnuxService template', () => {
      const providers = service.getAllProviders();

      expect(providers).toHaveLength(1);
      expect(providers[0]).toBe(mockIpnuxService);
      expect(moduleRef.get).toHaveBeenCalledWith('IpnuxService', { strict: false });
    });

    it('should return empty array when service instantiation fails', () => {
      moduleRef.get.mockImplementation(() => {
        throw new Error('Service initialization failed');
      });

      const loggerSpy = jest.spyOn((service as any).logger, 'error');
      const providers = service.getAllProviders();

      expect(providers).toHaveLength(0);
      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining('Could not instantiate all provider service templates'),
        expect.any(String)
      );
    });

    it('should warn about returning service templates instead of configured instances', () => {
      const loggerSpy = jest.spyOn((service as any).logger, 'warn');

      service.getAllProviders();

      expect(loggerSpy).toHaveBeenCalledWith(
        'getAllProviders currently returns service templates, not configured instances. Review usage.'
      );
    });
  });
});