import { Provider } from '../entities/provider.entity'; // Import Provider entity

export interface PurchaseInstanceOptions {
  id: number; // Internal product ID from your system
  externalProductId: string; // 供应商处的产品标识 (如IPNux的proxies_type)
  productConfigDetails: Record<string, any>;
  productMode: "STATIC" | "DYNAMIC"; // 产品模式
  customerId: number;
  orderId: string;
  quantity: number; // 对于静态IP，是IP数量；对于动态IP，通常是1个Channel
  durationDays: number;
  targetLocation?: { country?: string; countryCode?: string; city?: string; continent?: string }; // 主要用于静态IP
  // 动态IP Channel特定参数
  channelName?: string;
  limitTrafficGb?: number; // 动态IP的流量限制 (GB)
}

export interface PurchaseInstanceResult {
  providerInstanceId: string; // 供应商处的实例ID (静态为proxy_id, 动态为ChannelId)
  providerOrderId?: string; // 供应商处的订单ID
  // 静态IP特有
  ipAddress?: string;
  port?: number;
  username?: string;
  password?: string; // 对于动态IP，可能是Channel的密码
  protocols?: string;
  // 动态IP特有
  channelName?: string; // Channel名称
  // 通用
  countryCode?: string;
  cityName?: string;
  continentName?: string;
  activatedAt?: Date;
  expiresAt: Date;
  // 流量相关 (动态IP Channel创建时可能有初始流量包)
  totalFlowMb?: number; // 总流量 (MB)
  isUnlimited?: boolean; // 是否无限流量
  metadata?: Record<string, any>;
}

export interface RenewInstanceOptions {
  providerInstanceId: string; // 静态为proxy_id, 动态为ChannelId
  currentExpiresAt: Date;
  durationDays: number;
  productMode: "STATIC" | "DYNAMIC";
  externalProductId?: string; // Kept for now
  productConfigDetails?: Record<string, any>; // Kept for now
}

export interface RenewInstanceResult {
  newExpiresAt: Date;
  providerOrderId?: string;
  metadata?: Record<string, any>;
}

export interface GetInstanceDetailsOptions {
  providerInstanceId: string;
  productMode: "STATIC" | "DYNAMIC";
  externalProductId?: string; // Kept for now
  productConfigDetails?: Record<string, any>; // Kept for now
}export interface InstanceDetailsResult extends Partial<PurchaseInstanceResult> {
  status: string; // 供应商侧的实例状态
  usedFlowMb?: number; // 已用流量 (MB), 主要用于动态IP Channel
}
export interface UpdateChannelOptions {
  providerInstanceId: string; // ChannelId
  channelName?: string;
  channelPassword?: string;
  limitTrafficGb?: number;
  enable?: boolean; // 对应IPNux的Enable字段
  productConfigDetails?: Record<string, any>; // Kept for now
}

export interface GetChannelTrafficOptions {
  providerInstanceId: string; // ChannelId
  dateType?: number; // IPNux的DateType
  startTime?: string; // YYYY-MM-DD
  endTime?: string; // YYYY-MM-DD
  productConfigDetails?: Record<string, any>; // Kept for now
}

export interface ChannelTrafficDataPoint {
  channelId: number | string;
  useTraffic: number;
  useTime: Date | string;
}

export interface ChannelTrafficResult {
  items: ChannelTrafficDataPoint[];
  totalTrafficGb: number;
}

export interface GenerateEndpointOptions {
  providerInstanceId: string; // ChannelId
  location: string; // 国家代码或 \"Global\"
  stickySessionTime: number; // 0 为旋转
  count: number;
  domain?: string;
  state?: string;
  city?: string;
  productConfigDetails: Record<string, any>; // Kept for now, as it might be essential for protocol_type etc.
}

export interface ProxyProvider {
  /**
   * Initializes the provider service with specific configuration.
   * This method should be called by the factory after instantiating the service.
   * @param configDetails Configuration object from the shop_provider.config_details column.
   * @param providerEntity Optional: The full provider entity if needed for context (e.g., providerId, providerCode).
   */
  initialize(configDetails: Record<string, any>, providerEntity?: Provider): void;

  getProviderCode(): string;

  purchaseInstances(
    options: PurchaseInstanceOptions
  ): Promise<PurchaseInstanceResult[]>;

  renewInstance(options: RenewInstanceOptions): Promise<RenewInstanceResult>;

  getInstanceDetails(
    options: GetInstanceDetailsOptions
  ): Promise<InstanceDetailsResult | null>;

  // --- Static IP specific methods ---
  replaceInstance?(
    providerInstanceId: string,
    options: {
      targetLocation?: any;
      productConfigDetails?: Record<string, any>; // Kept for now
    }
  ): Promise<PurchaseInstanceResult>;

  updateInstanceCredentials?(
    providerInstanceId: string,
    credentials: { username?: string; password?: string },
    productConfigDetails?: Record<string, any> // Kept for now
  ): Promise<boolean>;

  updateInstanceWhitelist?(
    providerInstanceId: string,
    whitelistedIps: string[],
    productConfigDetails?: Record<string, any> // Kept for now
  ): Promise<boolean>;

  // --- Dynamic IP Channel specific methods ---
  updateChannel?(options: UpdateChannelOptions): Promise<boolean>;

  getChannelTraffic?(
    options: GetChannelTrafficOptions
  ): Promise<ChannelTrafficResult>;

  generateEndpoints?(options: GenerateEndpointOptions): Promise<string[]>;

  listEndpoints?(providerInstanceId: string): Promise<any[]>;

  deleteEndpoint?(endpointProviderId: string): Promise<boolean>;

  /**
   * Optional: A method to test the connection/credentials after initialization.
   * Called by ProviderService's testProviderConnection.
   */
  testConnection?(): Promise<{ success: boolean; message?: string; details?: any }>;
}