export enum ProxiesTypeEnum {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  // 根据实际业务需求添加其他代理类型
  // This enum is no longer directly used by GetNodeInventory request/response DTOs for proxies_type
}

export interface IPAvailabilityCountryItem {
  city_id?: string;
  city_name?: string;
  continents_id?: string;
  continents_name?: string;
  country_code?: string;
  number?: number; // 库存
  is_hot?: boolean; // 是否热门
}

export interface OpenApiGetNodeInventoryData {
  country_list?: IPAvailabilityCountryItem[];
  proxies_format?: number;
  proxies_type?: string; // MODIFIED: Changed from ProxiesTypeEnum
  purpose_web?: string;
}

export interface OpenApiGetNodeInventoryRequest {
  city_name?: string | null;
  continents_id?: string | null;
  country_code?: string | null;
  proxies_format: number;
  proxies_type: string; // MODIFIED: Changed from ProxiesTypeEnum
  purpose_web: string;
}
