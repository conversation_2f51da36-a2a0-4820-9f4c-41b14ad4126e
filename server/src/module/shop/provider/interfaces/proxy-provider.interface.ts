export interface PurchaseInstanceOptions {
  id: number;
  externalProductId: string; // 供应商处的产品标识 (如IPNux的proxies_type)
  productConfigDetails: Record<string, any>;
  productMode: "STATIC" | "DYNAMIC"; // 产品模式
  customerId: number;
  orderId: string;
  quantity: number; // 对于静态IP，是IP数量；对于动态IP，通常是1个Channel
  durationDays: number;
  targetLocation?: { country?: string; countryCode?: string; city?: string; continent?: string }; // 主要用于静态IP
  // 动态IP Channel特定参数
  channelName?: string;
  limitTrafficGb?: number; // 动态IP的流量限制 (GB)
}

export interface PurchaseInstanceResult {
  providerInstanceId: string; // 供应商处的实例ID (静态为proxy_id, 动态为ChannelId)
  providerOrderId?: string; // 供应商处的订单ID
  // 静态IP特有
  ipAddress?: string;
  port?: number;
  username?: string;
  password?: string; // 对于动态IP，可能是Channel的密码
  protocols?: string;
  // 动态IP特有
  channelName?: string; // Channel名称
  // 通用
  countryCode?: string;
  cityName?: string;
  continentName?: string;
  activatedAt?: Date;
  expiresAt: Date;
  // 流量相关 (动态IP Channel创建时可能有初始流量包)
  totalFlowMb?: number; // 总流量 (MB)
  isUnlimited?: boolean; // 是否无限流量
  metadata?: Record<string, any>;
}

export interface RenewInstanceOptions {
  providerInstanceId: string; // 静态为proxy_id, 动态为ChannelId
  currentExpiresAt: Date;
  durationDays: number;
  productMode: "STATIC" | "DYNAMIC";
}

export interface RenewInstanceResult {
  newExpiresAt: Date;
  providerOrderId?: string;
  metadata?: Record<string, any>;
}

export interface GetInstanceDetailsOptions {
  providerInstanceId: string;
  productMode: "STATIC" | "DYNAMIC";
  externalProductId?: string;
  productConfigDetails?: Record<string, any>;
}

export interface InstanceDetailsResult extends Partial<PurchaseInstanceResult> {
  status: string; // 供应商侧的实例状态
  usedFlowMb?: number; // 已用流量 (MB), 主要用于动态IP Channel
}
export interface UpdateChannelOptions {
  providerInstanceId: string; // ChannelId
  channelName?: string;
  channelPassword?: string;
  limitTrafficGb?: number;
  enable?: boolean; // 对应IPNux的Enable字段
}

export interface GetChannelTrafficOptions {
  providerInstanceId: string; // ChannelId
  dateType?: number; // IPNux的DateType
  startTime?: string; // YYYY-MM-DD
  endTime?: string; // YYYY-MM-DD
}

export interface ChannelTrafficDataPoint {
  channelId: number; // 或 providerInstanceId
  useTraffic: number; // GB
  useTime: Date;
}

export interface ChannelTrafficResult {
  items: ChannelTrafficDataPoint[];
  totalTrafficGb: number;
}

export interface GenerateEndpointOptions {
  providerInstanceId: string; // ChannelId
  location: string; // 国家代码或 "Global"
  stickySessionTime: number; // 0 为旋转
  count: number;
  domain?: string;
  state?: string;
  city?: string;
  productConfigDetails: Record<string, any>; // 可能需要其中的 protocol_type 等
}

export interface ProxyProvider {
  getProviderCode(): string;

  // 购买/开通 (统一入口，内部根据productMode区分)
  purchaseInstances(
    options: PurchaseInstanceOptions
  ): Promise<PurchaseInstanceResult[]>;

  // 续费 (主要用于静态IP或包周期动态Channel)
  renewInstance(options: RenewInstanceOptions): Promise<RenewInstanceResult>;

  // 获取实例详情 (静态IP详情 或 动态Channel详情)
  getInstanceDetails(
    options: GetInstanceDetailsOptions
  ): Promise<InstanceDetailsResult | null>;

  // --- 静态IP特有方法 ---
  replaceInstance?(
    providerInstanceId: string,
    options: {
      targetLocation?: any;
      productConfigDetails?: Record<string, any>;
    }
  ): Promise<PurchaseInstanceResult>;
  updateInstanceCredentials?(
    providerInstanceId: string,
    credentials: { username?: string; password?: string },
    productConfigDetails?: Record<string, any>
  ): Promise<boolean>;
  updateInstanceWhitelist?(
    providerInstanceId: string,
    whitelistedIps: string[],
    productConfigDetails?: Record<string, any>
  ): Promise<boolean>;

  // --- 动态IP Channel特有方法 ---
  updateChannel?(options: UpdateChannelOptions): Promise<boolean>;
  getChannelTraffic?(
    options: GetChannelTrafficOptions
  ): Promise<ChannelTrafficResult>;
  generateEndpoints?(options: GenerateEndpointOptions): Promise<string[]>; // 返回endpoint字符串列表
  listEndpoints?(providerInstanceId: string): Promise<any[]>;
  deleteEndpoint?(endpointProviderId: string): Promise<boolean>;
}
