import { BigIntJsonUtil } from '@/common/utils/bigint-json.util';
import { OpenApiGetNodeInventoryData, OpenApiGetNodeInventoryRequest } from '@/module/shop/types'; // Import from shared types
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import {
  ChannelTrafficResult,
  GenerateEndpointOptions,
  GetChannelTrafficOptions,
  GetInstanceDetailsOptions,
  InstanceDetailsResult,
  ProxyProvider,
  PurchaseInstanceOptions,
  PurchaseInstanceResult,
  RenewInstanceOptions,
  RenewInstanceResult,
  UpdateChannelOptions,
} from '../../interfaces/proxy-provider.interface';
import {
  IpnuxCalculatePricePayload,
  IpnuxCalculatePriceResponse,
  IpnuxChannelCreateDto,
  IpnuxChannelDetailDto,
  IpnuxChannelTrafficDto,
  IpnuxGenerateEndpointPayload,
  IpnuxNodeInfo,
  IpnuxNodeListPayload,
  IpnuxPurchaseNewOrderDto,
  IpnuxPurchaseNewOrderPayload,
  IpnuxRenewOrderPayload,
} from './ipnux.interfaces';

@Injectable()
export class IpnuxService implements ProxyProvider {
  private readonly logger = new Logger(IpnuxService.name);
  private readonly userId: string;
  private readonly token: string;
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.userId = this.configService.get<string>('IPNUX_USER_ID');
    this.token = this.configService.get<string>('IPNUX_TOKEN');
    this.baseUrl = this.configService.get<string>('IPNUX_API_BASE_URL', 'https://api.ipnux.com/V1/OpenApi');

    if (!this.userId || !this.token) {
      this.logger.error('IPNUX_USER_ID and IPNUX_TOKEN must be set in the environment variables');
      throw new Error('IPNux service requires IPNUX_USER_ID and IPNUX_TOKEN environment variables');
    }

    this.logger.log(`IpnuxService initialized from environment variables - baseUrl: ${this.baseUrl}`);
  }

  private async makeApiCall<T>(endpoint: string, data: any, method: 'GET' | 'POST' = 'POST'): Promise<T> {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    const url = `${this.baseUrl}${endpoint}`;

    try {
      // 将 UserId 和 Token 添加到请求头
      const headers = {
        UserId: this.userId,
        Token: this.token,
        'Content-Type': 'application/json',
      };

      // 详细的请求日志 - 标准化格式
      this.logger.log({
        message: 'Supplier API call started',
        event_type: 'api_call_start',
        supplier: 'IPNux',
        supplier_request_id: requestId,
        api_endpoint: endpoint,
        api_url: url,
        http_method: method,
        request_payload: this.sanitizePayload(data),
      });

      // 配置请求选项
      const requestConfig = {
        headers: headers,
        timeout: 30000, // 30秒超时
        // 使用自定义的 transformRequest 来处理发送的大整数
        transformRequest: [
          (data) => {
            if (typeof data === 'object' && data !== null) {
              try {
                // 使用 BigIntJsonUtil 来安全序列化包含大整数的对象
                return BigIntJsonUtil.stringify(data);
              } catch (error) {
                this.logger.warn(`Failed to stringify with BigInt support, falling back to standard JSON.stringify: ${error.message}`);
                return JSON.stringify(data);
              }
            }
            return data;
          },
        ],
        // 使用自定义的 transformResponse 来处理大整数
        transformResponse: [
          (data) => {
            if (typeof data === 'string') {
              try {
                // 使用 BigIntJsonUtil 来安全解析包含大整数的 JSON
                const parsed = BigIntJsonUtil.parse(data);
                // 处理 IPNux API 响应中的大整数字段
                if (parsed && parsed.Data) {
                  parsed.Data = BigIntJsonUtil.convertBigIntFields(parsed.Data, ['proxy_id', 'continents_id', 'created_at_unix', 'expired_at_unix']);
                }
                return parsed;
              } catch (error) {
                this.logger.warn(`Failed to parse JSON with BigInt support, falling back to standard JSON.parse: ${error.message}`);
                return JSON.parse(data);
              }
            }
            return data;
          },
        ],
      };

      // 发送 HTTP 请求
      const response = await lastValueFrom(
        method === 'GET'
          ? this.httpService.get<{ Code: number; Message: string; Data: T }>(url, requestConfig)
          : this.httpService.post<{ Code: number; Message: string; Data: T }>(url, data, requestConfig),
      );

      const duration = Date.now() - startTime;

      // 详细的响应日志 - 标准化格式
      this.logger.log({
        message: 'Supplier API call successful',
        event_type: 'api_call_success',
        supplier: 'IPNux',
        supplier_request_id: requestId,
        api_endpoint: endpoint,
        http_status_code: response.status,
        duration_ms: duration,
        provider_response: {
          code: response.data?.Code,
          message: response.data?.Message,
          data_size: response.data?.Data ? JSON.stringify(response.data.Data).length : 0,
        },
      });

      if (response.data.Code !== 1000) {
        // 业务错误日志 - 使用 warn 级别，因为这是可预期的业务逻辑错误
        this.logger.warn({
          message: 'Supplier API call failed with business error',
          event_type: 'api_call_failure',
          supplier: 'IPNux',
          supplier_request_id: requestId,
          api_endpoint: endpoint,
          http_status_code: response.status,
          duration_ms: duration,
          error_details: {
            provider_error_code: response.data.Code,
            provider_error_message: response.data.Message,
            response_body: response.data,
            request_payload: this.sanitizePayload(data),
          },
        });


        throw new Error(`IPNux API error: [${response.data.Code}] ${response.data.Message}`);
      }

      return response.data.Data;
    } catch (error) {
      const duration = Date.now() - startTime;
      // 系统错误日志 - 网络、超时等不可预期的错误
      this.logger.error({
        message: 'Supplier API call failed with system error',
        event_type: 'api_call_failure',
        supplier: 'IPNux',
        supplier_request_id: requestId,
        api_endpoint: endpoint,
        duration_ms: duration,
        error_details: {
          system_error_message: error.message,
          system_error_stack: error.stack,
          http_status_code: error.response?.status,
          request_payload: this.sanitizePayload(data),
          is_axios_error: error.isAxiosError || false,
        },
      });


      throw error;
    }
  }

  private async postApi<T>(endpoint: string, data: any): Promise<T> {
    return this.makeApiCall<T>(endpoint, data, 'POST');
  }

  private async getApi<T>(endpoint: string, params?: any): Promise<T> {
    return this.makeApiCall<T>(endpoint, params, 'GET');
  }

  /**
   * 清理敏感信息的载荷数据用于日志记录
   */
  private sanitizePayload(payload: any): any {
    if (!payload) return payload;

    const sanitized = JSON.parse(JSON.stringify(payload));

    // 移除或脱敏敏感字段
    if (sanitized.Token) sanitized.Token = '[REDACTED]';
    if (sanitized.password) sanitized.password = '[REDACTED]';
    if (sanitized.secret) sanitized.secret = '[REDACTED]';

    return sanitized;
  }

  /**
   * 清理敏感信息的请求头用于日志记录
   */
  private sanitizeHeaders(headers: any): any {
    if (!headers) return headers;

    const sanitized = JSON.parse(JSON.stringify(headers));

    // 脱敏敏感请求头
    if (sanitized.Token) sanitized.Token = '[REDACTED]';
    if (sanitized.token) sanitized.token = '[REDACTED]';
    if (sanitized.Authorization) sanitized.Authorization = '[REDACTED]';
    if (sanitized.authorization) sanitized.authorization = '[REDACTED]';
    if (sanitized.UserId) sanitized.UserId = '[REDACTED]';
    if (sanitized.userid) sanitized.userid = '[REDACTED]';

    return sanitized;
  }

  /**
   * 分类IPNux业务错误
   */
  private categorizeIPNuxError(errorCode: number): string {
    switch (errorCode) {
      case 2001:
        return 'inventory_insufficient'; // 库存不足
      case 2002:
        return 'balance_insufficient'; // 余额不足
      case 2003:
        return 'parameter_invalid'; // 参数无效
      case 2004:
        return 'authentication_failed'; // 认证失败
      case 2005:
        return 'permission_denied'; // 权限不足
      case 2006:
        return 'rate_limit_exceeded'; // 频率限制
      case 2007:
        return 'service_unavailable'; // 服务不可用
      default:
        return 'unknown_business_error';
    }
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(errorCode: number): boolean {
    const retryableErrors = [2001, 2007]; // 库存不足、服务不可用
    return retryableErrors.includes(errorCode);
  }

  /**
   * 获取错误的建议操作
   */
  private getSuggestedAction(errorCode: number): string {
    switch (errorCode) {
      case 2001:
        return '尝试其他地区或稍后重试';
      case 2002:
        return '检查供应商账户余额';
      case 2003:
        return '检查请求参数是否符合API规范';
      case 2004:
        return '检查UserId和Token配置';
      case 2005:
        return '检查账户权限设置';
      case 2006:
        return '降低请求频率后重试';
      case 2007:
        return '稍后重试或联系供应商客服';
      default:
        return '检查错误详情并联系技术支持';
    }
  }

  /**
   * 判断是否为网络错误
   */
  private isNetworkError(error: any): boolean {
    const networkErrorCodes = ['ECONNREFUSED', 'ENOTFOUND', 'ECONNRESET', 'ECONNABORTED', 'ETIMEDOUT'];
    return networkErrorCodes.includes(error.code) || error.message?.includes('network') || error.message?.includes('connect');
  }

  /**
   * 判断是否为超时错误
   */
  private isTimeoutError(error: any): boolean {
    return error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT' || error.message?.includes('timeout') || error.message?.includes('timed out');
  }

  /**
   * 分类系统错误
   */
  private categorizeSystemError(error: any): string {
    if (this.isTimeoutError(error)) return 'timeout_error';
    if (this.isNetworkError(error)) return 'network_error';
    if (error.code === 'ENOTFOUND') return 'dns_error';
    if (error.code === 'ECONNREFUSED') return 'connection_refused';
    if (error.response?.status >= 500) return 'server_error';
    if (error.response?.status >= 400) return 'client_error';
    return 'unknown_system_error';
  }

  /**
   * 获取系统错误的建议操作
   */
  private getSuggestedSystemErrorAction(error: any): string {
    if (this.isTimeoutError(error)) return '增加超时时间或检查网络连接';
    if (error.code === 'ENOTFOUND') return '检查域名解析或网络配置';
    if (error.code === 'ECONNREFUSED') return '检查服务器状态和防火墙设置';
    if (error.response?.status >= 500) return '供应商服务器错误，稍后重试';
    if (error.response?.status >= 400) return '检查请求参数和认证信息';
    return '检查网络连接和系统配置';
  }

  /**
   * 获取IPNux API接受的城市名称
   * 将中文城市名称映射为英文名称
   */
  private getCityNameForIPNux(cityName?: string): string | null {
    if (!cityName) return null;

    // 中文到英文城市名称映射
    const cityNameMap: Record<string, string> = {
      香港: 'Hong Kong',
      台北: 'Taipei',
      东京: 'Tokyo',
      首尔: 'Seoul',
      新加坡: 'Singapore',
      曼谷: 'Bangkok',
      雅加达: 'Jakarta',
      马尼拉: 'Manila',
      胡志明市: 'Ho Chi Minh City',
      吉隆坡: 'Kuala Lumpur',
      纽约: 'New York',
      洛杉矶: 'Los Angeles',
      芝加哥: 'Chicago',
      迈阿密: 'Miami',
      西雅图: 'Seattle',
      伦敦: 'London',
      巴黎: 'Paris',
      法兰克福: 'Frankfurt',
      阿姆斯特丹: 'Amsterdam',
      悉尼: 'Sydney',
      墨尔本: 'Melbourne',
    };

    // 如果是中文名称，返回对应的英文名称
    if (cityNameMap[cityName]) {
      return cityNameMap[cityName];
    }

    // 如果已经是英文名称或未知名称，直接返回
    return cityName;
  }

  /**
   * 验证IPNux API参数
   */
  private validateIPNuxParameters(params: { proxies_type: string; proxies_format: number; purpose_web: string; city_name: string }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证 proxies_type
    const validProxyTypes = ['Premium (ISP) proxies', 'Shared (ISP) proxies'];
    if (!validProxyTypes.includes(params.proxies_type)) {
      errors.push(`Invalid proxies_type: ${params.proxies_type}. Valid values: ${validProxyTypes.join(', ')}`);
    }

    // 验证 proxies_format
    const validFormats = [1, 2]; // 1: IP:Port:User:Pass, 2: User:Pass@IP:Port
    if (!validFormats.includes(params.proxies_format)) {
      errors.push(`Invalid proxies_format: ${params.proxies_format}. Valid values: ${validFormats.join(', ')}`);
    }

    // 🎯 验证 purpose_web 参数说明：
    // 用途网站标识，支持的值包括：TikTok、Instagram、Facebook、Twitter、Any 等
    // IPNux API 支持大小写不敏感，这里进行大小写不敏感的验证
    const validPurposeWebs = ['tiktok', 'instagram', 'facebook', 'twitter', 'any'];
    const purposeWebLower = params.purpose_web?.toLowerCase();
    if (!purposeWebLower || !validPurposeWebs.includes(purposeWebLower)) {
      errors.push(`Invalid purpose_web: ${params.purpose_web}. Valid values (case insensitive): TikTok, Instagram, Facebook, Twitter, Any`);
    }

    // 验证 city_name
    if (!params.city_name || params.city_name.trim().length === 0) {
      errors.push('city_name is required and cannot be empty');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 新增方法：获取已购买的IP列表（详细信息）
  async getPurchasedIPList(options: {
    proxies_type: string;
    proxies_format: number;
    order_id?: string;
    proxy_status?: number;
    city_name?: string;
    continents_name?: string;
    expiring_days?: number;
  }): Promise<{ count: number; results: IpnuxNodeInfo[] }> {
    this.logger.debug(`Fetching purchased IP list from IPNux with options: ${JSON.stringify(options)}`);

    const payload = {
      proxies_type: options.proxies_type,
      proxies_format: options.proxies_format,
      ...(options.order_id && { order_id: options.order_id }),
      ...(options.proxy_status && { proxy_status: options.proxy_status }),
      ...(options.city_name && { city_name: options.city_name }),
      ...(options.continents_name && { continents_name: options.continents_name }),
      ...(options.expiring_days && { expiring_days: options.expiring_days }),
    };

    try {
      const response = await this.postApi<{
        count: number;
        proxies_type: string;
        proxies_format: number;
        results: Array<{
          proxy_id: number;
          username: string;
          password: string;
          proxy_address: string;
          port: number;
          protocols: number;
          proxy_status: number;
          continents_id: number;
          continents_name: string;
          country_code: string;
          city_id: string;
          city_name: string;
          created_at: string;
          expired_at: string;
          udp_status: boolean;
          order_id: string;
          permissions: boolean;
          created_at_unix: number;
          expired_at_unix: number;
        }>;
      }>('/OpenApiNodeList', payload);

      // 转换IPNux返回的数据格式为统一的IpnuxNodeInfo格式
      const results: IpnuxNodeInfo[] = response.results.map((item) => ({
        proxy_id: BigIntJsonUtil.toSafeString(item.proxy_id), // 安全转换为字符串
        proxy_address: `${item.proxy_address}:${item.port}`,
        username: item.username,
        password: item.password,
        protocols_type: item.protocols,
        country_code: item.country_code,
        city_name: item.city_name,
        expire_time_data: item.expired_at,
        status: item.proxy_status,
        isp: '', // IPNux API可能不返回这个字段
        ip_type_name: '', // IPNux API可能不返回这个字段
        last_heartbeat_time: item.created_at,
      }));

      return {
        count: response.count,
        results,
      };
    } catch (error) {
      this.logger.error(`Error fetching purchased IP list: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getAvailableInventory(request: OpenApiGetNodeInventoryRequest): Promise<OpenApiGetNodeInventoryData> {
    this.logger.log(`📦 [IPNux] 获取库存开始 | 请求参数: ${JSON.stringify(request)}`);

    // IPNux API 的 /OpenApiGetNodeInventory 端点通常返回一个包含国家列表的结构
    // 假设它直接返回 OpenApiGetNodeInventoryData 结构
    // postApi 的第二个参数 data 现在只包含业务相关的参数
    const response = await this.postApi<OpenApiGetNodeInventoryData>('/OpenApiGetNodeInventory', request);

    // 统计响应数据
    const countryCount = response?.country_list?.length || 0;
    const totalStock = response?.country_list?.reduce((sum, country) => sum + (country.number || 0), 0) || 0;

    this.logger.log(`📦 [IPNux] 库存获取成功 | 国家数量: ${countryCount} | 总库存: ${totalStock} | 响应大小: ${JSON.stringify(response).length} bytes`);
    this.logger.debug(`IPNux inventory /OpenApiGetNodeInventory response: ${JSON.stringify(response)}`);

    return response;
  }

  // 新增方法：计算价格
  async calculatePrice(request: IpnuxCalculatePricePayload): Promise<IpnuxCalculatePriceResponse> {
    this.logger.log(`💰 [IPNux] 价格计算开始 | 请求参数: ${JSON.stringify(request)}`);

    try {
      const response = await this.postApi<IpnuxCalculatePriceResponse>('/OpenApiGetCurrentVersionPrice', request);

      // 提取关键价格信息
      const priceInfo = {
        originalPrice: response?.proxies_count_discount_tiers?.[0]?.per_proxy_price || 'N/A',
        currency: request.currency || 'USD',
        proxiesType: request.proxies_type,
        timePeriod: request.time_period,
        proxiesFormat: request.proxies_format,
      };

      this.logger.log(
        `💰 [IPNux] 价格计算成功 | 价格: ${priceInfo.originalPrice} ${priceInfo.currency} | 类型: ${priceInfo.proxiesType} | 周期: ${priceInfo.timePeriod}天 | 格式: ${priceInfo.proxiesFormat}`,
      );
      this.logger.debug(`IPNux price calculation response: ${JSON.stringify(response)}`);

      return response;
    } catch (error) {
      this.logger.error(`💰 [IPNux] 价格计算失败 | 请求: ${JSON.stringify(request)} | 错误: ${error.message}`, error.stack);
      throw error;
    }
  }

  getProviderCode(): string {
    return 'ipnux';
  }
  async purchaseInstances(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    if (options.productMode === 'STATIC') {
      return this.purchaseStaticInstances(options);
    } else if (options.productMode === 'DYNAMIC') {
      return this.purchaseDynamicChannel(options); // 动态通常一次购买一个Channel
    }
    throw new Error('Invalid product mode for purchase.');
  }

  private async purchaseStaticInstances(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    const ipnuxConfig = options.productConfigDetails?.ipnux_specific_params || {};

    // 🔧 修复 proxies_type 参数：使用产品配置中的 proxyType 而不是 externalProductId
    const proxiesType = options.productConfigDetails?.proxyType || 'Shared (ISP) proxies';

    // 🔧 修复城市名称：优先使用英文名称，如果没有则使用中文名称
    const cityName = this.getCityNameForIPNux(options.targetLocation?.city) || ipnuxConfig.default_city || 'Los Angeles';

    // 🔧 修复 purpose_web：从产品配置获取
    const purposeWeb = options.productConfigDetails?.purposeWeb || ipnuxConfig.purpose_web || 'tiktok';

    const buyData = [
      {
        city_name: cityName,
        count: options.quantity,
      },
    ];

    const payload: IpnuxPurchaseNewOrderPayload = {
      proxies_type: proxiesType, // 🔧 使用正确的代理类型
      proxies_format: options.productConfigDetails?.proxiesFormat || ipnuxConfig.proxies_format || 2,
      purpose_web: purposeWeb, // 🔧 使用产品配置的目标网站
      time_period: options.durationDays,
      currency: ipnuxConfig.currency || 'USD',
      udp_status: ipnuxConfig.udp_status || false,
      protocols_type: ipnuxConfig.protocols_type || 3, // HTTP+SOCKS5
      buyData: buyData,
    };

    // 🔍 添加参数验证
    const validation = this.validateIPNuxParameters({
      proxies_type: proxiesType,
      proxies_format: payload.proxies_format,
      purpose_web: purposeWeb,
      city_name: cityName,
    });

    if (!validation.isValid) {
      const errorMsg = `IPNux参数验证失败: ${validation.errors.join('; ')}`;
      this.logger.error(`❌ ${errorMsg}`, {
        proxies_type: proxiesType,
        city_name: cityName,
        purpose_web: purposeWeb,
        proxies_format: payload.proxies_format,
        externalProductId: options.externalProductId,
        productConfigDetails: options.productConfigDetails,
      });
      throw new Error(errorMsg);
    }

    // 🔍 添加参数验证日志
    this.logger.log(`📋 IPNux Purchase Parameters Validated`, {
      proxies_type: proxiesType,
      city_name: cityName,
      purpose_web: purposeWeb,
      proxies_format: payload.proxies_format,
      externalProductId: options.externalProductId,
      productConfigDetails: options.productConfigDetails,
    });

    let response: IpnuxPurchaseNewOrderDto;

    response = await this.postApi<IpnuxPurchaseNewOrderDto>('/OpenApiPurchaseNewOrder', payload);

    if (!response || !response.NodeInfos || response.NodeInfos.length === 0) {
      throw new Error('IPNux static purchase did not return any NodeInfos.');
    }
    return response.NodeInfos.map((node: IpnuxNodeInfo) => {
      this.logger.log({
        message: 'Processing IPNux node',
        event_type: 'ipnux_node_processing',
        node: node,
      });

      // IPNux 返回的格式中，IP 和端口是分开的字段
      const ipAddress = node.proxy_address;
      const port = (node as any).port || parseInt(node.proxy_address.split(':')[1], 10);

      if (!port || isNaN(port)) {
        this.logger.error(`Invalid port for node: ${JSON.stringify(node)}`);
        throw new Error(`Invalid port for proxy ${node.proxy_id}: ${port}`);
      }

      // 处理过期时间 - IPNux API 返回的字段可能是 expired_at 或 expire_time_data
      const expirationTime = (node as any).expired_at || node.expire_time_data;
      let expiresAt: Date;

      if (expirationTime) {
        expiresAt = new Date(expirationTime);
      } else {
        // 如果 API 没有返回过期时间，则根据购买天数计算
        this.logger.warn(`No expiration time in response, calculating from duration: ${options.durationDays} days`);
        expiresAt = new Date(Date.now() + options.durationDays * 24 * 60 * 60 * 1000);
      }

      return {
        providerInstanceId: BigIntJsonUtil.toSafeString(node.proxy_id), // 确保安全转换为字符串
        providerOrderId: response.OrderNo,
        ipAddress: ipAddress,
        port: port,
        username: node.username,
        password: node.password,
        protocols: this.mapIpnuxProtocolTypeToString((node as any).protocols || node.protocols_type || 3),
        countryCode: node.country_code,
        cityName: node.city_name,
        activatedAt: new Date(),
        expiresAt: expiresAt,
        isUnlimited: true, // IPNux 静态住宅通常无限流量
        metadata: { isp: node.isp, ip_type_name: node.ip_type_name },
      };
    });
  }

  private async purchaseDynamicChannel(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    const ipnuxConfig = options.productConfigDetails?.ipnux_specific_params || {};
    const payload = {
      ChannelName: options.channelName || `Channel_${options.customerId}_${Date.now()}`,
      ChannelLimitTrafficGb: options.limitTrafficGb || ipnuxConfig.default_limit_traffic_gb || 0, // 0表示不限制或按量
    };
    const response = await this.postApi<IpnuxChannelCreateDto>('/OpenApiCreateChannel', payload);

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + options.durationDays);

    return [
      {
        providerInstanceId: response.ChannelId.toString(),
        channelName: response.ChannelName,
        password: response.ChannelPassword, // IPNux 返回的密码
        activatedAt: new Date(response.CreateTime), // IPNux 返回的创建时间
        expiresAt: expiresAt, // 自己计算过期时间
        totalFlowMb: response.ChannelLimitTrafficGb > 0 ? response.ChannelLimitTrafficGb * 1024 : undefined,
        isUnlimited: response.ChannelLimitTrafficGb === 0,
        metadata: {
          ChannelStatus: response.ChannelStatus,
          UsedTraffic: response.UsedTraffic,
        },
      },
    ];
  }
  private mapIpnuxProtocolTypeToString(protocolType: number): string {
    // 1: HTTP, 2: SOCKS5, 3: HTTP+SOCKS5
    if (protocolType === 1) return 'HTTP';
    if (protocolType === 2) return 'SOCKS5';
    if (protocolType === 3) return 'HTTP,SOCKS5';
    return 'UNKNOWN';
  }

  private mapStringToIpnuxProtocolType(protocols: string): number {
    const lowerProtocols = protocols.toLowerCase();
    if (lowerProtocols.includes('http') && lowerProtocols.includes('socks5')) return 3;
    if (lowerProtocols.includes('http')) return 1;
    if (lowerProtocols.includes('socks5')) return 2;
    return 3; // Default
  }

  async renewInstance(options: RenewInstanceOptions): Promise<RenewInstanceResult> {
    if (options.productMode === 'STATIC') {
      const payload: IpnuxRenewOrderPayload = {
        proxies_ids: [options.providerInstanceId], // 使用字符串避免精度丢失
        time_period: options.durationDays,
        currency: 'USD', // 应从产品或全局配置获取
      };
      const response = await this.postApi<any>('/OpenApiNoneRenew', payload);
      const newExpiresAt = new Date(options.currentExpiresAt);
      newExpiresAt.setDate(newExpiresAt.getDate() + options.durationDays);
      return {
        newExpiresAt: newExpiresAt,
        providerOrderId: response?.OrderNo,
        metadata: { renewalResponse: response },
      };
    } else if (options.productMode === 'DYNAMIC') {
      // IPNux Channel 续费逻辑：通常是业务层延长 shop_proxy_instances.expires_at
      // 假设目前是系统管理有效期，直接计算新的过期时间
      this.logger.log(`Renewing dynamic channel ${options.providerInstanceId} in local system.`);
      const newExpiresAt = new Date(options.currentExpiresAt);
      newExpiresAt.setDate(newExpiresAt.getDate() + options.durationDays);
      return { newExpiresAt };
    }
    throw new Error('Invalid product mode for renewal.');
  }

  async getInstanceDetails(options: GetInstanceDetailsOptions): Promise<InstanceDetailsResult | null> {
    if (options.productMode === 'STATIC') {
      // 🔧 修复：使用产品配置中的 proxyType 而不是 externalProductId
      const proxies_type = options.productConfigDetails?.proxyType || 'Shared (ISP) proxies';
      const proxies_format = options.productConfigDetails?.proxiesFormat || options.productConfigDetails?.ipnux_specific_params?.proxies_format || 2;

      if (!proxies_type) {
        this.logger.error(`proxies_type (externalProductId) is required for IPNux GetInstanceDetails (Static). ProviderInstanceId: ${options.providerInstanceId}`);
        throw new Error('proxies_type (externalProductId) is required for IPNux GetInstanceDetails (Static).');
      }

      const listPayload: IpnuxNodeListPayload = {
        proxy_ids: [options.providerInstanceId], // 使用字符串避免精度丢失
        proxies_type: proxies_type,
        proxies_format: proxies_format,
        page: 1,
        limit: 1,
      };
      const response = await this.postApi<{ NodeInfos: IpnuxNodeInfo[]; Total: number }>('/OpenApiNodeList', listPayload);
      if (response && response.NodeInfos && response.NodeInfos.length > 0) {
        const node = response.NodeInfos[0];
        return {
          providerInstanceId: BigIntJsonUtil.toSafeString(node.proxy_id), // 安全转换为字符串
          ipAddress: node.proxy_address.split(':')[0],
          port: parseInt(node.proxy_address.split(':')[1], 10),
          username: node.username,
          // password: node.password, // 通常不返回密码
          protocols: this.mapIpnuxProtocolTypeToString(node.protocols_type),
          countryCode: node.country_code,
          cityName: node.city_name,
          expiresAt: new Date(node.expire_time_data),
          status: this.mapIpnuxStaticStatus(node.status),
          isUnlimited: true,
          metadata: {
            isp: node.isp,
            ip_type_name: node.ip_type_name,
            last_heartbeat_time: node.last_heartbeat_time,
          },
        };
      }
    } else if (options.productMode === 'DYNAMIC') {
      const listPayload = {
        ChannelId: parseInt(options.providerInstanceId, 10),
      };
      // OpenApiSubUsers 返回的是列表，即使指定了ChannelId，也可能返回数组
      const response = await this.postApi<IpnuxChannelDetailDto[]>('/OpenApiSubUsers', listPayload);
      const channelInfo = response.find((ch) => ch.ChannelId.toString() === options.providerInstanceId);

      if (channelInfo) {
        // Channel的过期时间由我方系统管理，IPNux不直接提供Channel的expire_time_data
        // status 也由我方系统结合IPNux的 ChannelStatus 和 expires_at 综合判断
        return {
          providerInstanceId: channelInfo.ChannelId.toString(),
          channelName: channelInfo.ChannelName,
          password: channelInfo.ChannelPassword,
          status: this.mapIpnuxDynamicStatus(channelInfo.ChannelStatus), // IPNux的ChannelStatus
          totalFlowMb: channelInfo.LimieTrafficGb > 0 ? channelInfo.LimieTrafficGb * 1024 : undefined,
          usedFlowMb: channelInfo.UseTrafficGb * 1024, // IPNux返回的是GB
          isUnlimited: channelInfo.LimieTrafficGb === 0,
          metadata: { CreateTime: channelInfo.CreateTime },
        };
      }
    }
    return null;
  }

  private mapIpnuxStaticStatus(status: number | string): string {
    // IPNux Node status
    // 根据IPNux文档调整
    const statusStr = status.toString().toLowerCase();
    if (statusStr === 'normal' || status === 1 || status === '1') return 'active';
    if (statusStr === 'expired' || status === 2 || status === '2') return 'expired';
    // ... 其他状态
    return 'unknown';
  }

  private mapIpnuxDynamicStatus(channelStatus: number): string {
    // IPNux Channel status
    // 1:Normal 2:Expired(Traffic run out) 3:Disable
    if (channelStatus === 1) return 'active';
    if (channelStatus === 2) return 'flow_exhausted'; // 或 'expired'
    if (channelStatus === 3) return 'suspended'; // 或 'disabled'
    return 'unknown';
  }

  // --- 静态IP方法实现 (确保传递 productConfigDetails 以获取 proxies_type/format) ---
  async replaceInstance(
    providerInstanceId: string,
    options: {
      targetLocation?: any;
      productConfigDetails?: Record<string, any>;
    },
  ): Promise<PurchaseInstanceResult> {
    const ipnuxConfig = options.productConfigDetails?.ipnux_specific_params || {};
    // 🔧 修复：使用产品配置中的 proxyType 而不是 externalProductId
    const proxies_type = options.productConfigDetails?.proxyType || 'Shared (ISP) proxies';
    const proxies_format = options.productConfigDetails?.proxiesFormat || ipnuxConfig.proxies_format || 2;

    if (!proxies_type) throw new Error('proxies_type (externalProductId) is required for replaceInstance.');

    const payload = {
      proxies_ids: [providerInstanceId], // 使用字符串避免精度丢失
      proxies_type: proxies_type,
      proxies_format: proxies_format,
      new_city_name: options.targetLocation?.city,
      new_country_code: options.targetLocation?.country_code, // IPNux API 可能用 city_name
    };
    const response = await this.postApi<IpnuxNodeInfo>('/OpenApiSwitchNode', payload); // 假设返回单个NodeInfo
    if (!response) throw new Error('IPNux replace did not return valid NodeInfo.');

    return {
      providerInstanceId: BigIntJsonUtil.toSafeString(response.proxy_id), // 安全转换为字符串
      ipAddress: response.proxy_address.split(':')[0],
      port: parseInt(response.proxy_address.split(':')[1], 10),
      username: response.username,
      password: response.password,
      protocols: this.mapIpnuxProtocolTypeToString(response.protocols_type),
      countryCode: response.country_code,
      cityName: response.city_name,
      activatedAt: new Date(), // 替换后视为新激活
      expiresAt: new Date(response.expire_time_data), // 过期时间通常不变或API返回
      isUnlimited: true,
      metadata: { isp: response.isp, ip_type_name: response.ip_type_name },
    };
  }

  async updateInstanceCredentials(providerInstanceId: string, credentials: { username?: string; password?: string }, productConfigDetails?: Record<string, any>): Promise<boolean> {
    // 此方法主要用于静态IP。动态IP的密码在Channel级别。
    // 🔧 修复：使用产品配置中的 proxyType 而不是 externalProductId
    const proxies_type = productConfigDetails?.proxyType || 'Shared (ISP) proxies';
    if (!proxies_type) throw new Error('proxies_type (proxyType) is required for updateInstanceCredentials.');

    if (!credentials.username || !credentials.password) {
      throw new Error('Username and password are required for IPNux credential update.');
    }

    // 验证用户名长度（IPNux要求6-32字符）
    if (credentials.username.length < 6 || credentials.username.length > 32) {
      throw new Error(`Username length must be between 6 and 32 characters. Current length: ${credentials.username.length}`);
    }
    // 🔧 修复：对于静态IP的大整数proxy_id，需要特殊处理以避免精度丢失
    // 检查是否是大整数（长度超过15位）
    let proxyIdForApi: number | string;
    if (providerInstanceId.length > 15) {
      // 大整数：直接使用字符串，让 json-bigint 在序列化时处理
      this.logger.warn(`Large proxy_id detected: ${providerInstanceId}, using string representation`);
      proxyIdForApi = providerInstanceId;
    } else {
      // 小整数：安全转换为数字
      proxyIdForApi = parseInt(providerInstanceId, 10);
      if (isNaN(proxyIdForApi)) {
        throw new Error(`Invalid providerInstanceId: ${providerInstanceId}`);
      }
    }

    const payload = {
      proxy_ids: [proxyIdForApi], // 根据大小使用数字或字符串
      proxies_type: proxies_type,
      username: credentials.username,
      password: credentials.password,
    };
    const responseData = await this.postApi<boolean>('/OpenApiEditNodeUserAndPass', payload);
    return responseData;
  }

  async updateInstanceWhitelist(providerInstanceId: string, whitelistedIps: string[], productConfigDetails?: Record<string, any>): Promise<boolean> {
    // 此方法主要用于静态IP。
    // 🔧 修复：使用产品配置中的 proxyType 而不是 externalProductId
    const proxies_type = productConfigDetails?.proxyType || 'Shared (ISP) proxies';
    if (!proxies_type) throw new Error('proxies_type (proxyType) is required for updateInstanceWhitelist.');

    // 🔧 修复：对于静态IP的大整数proxy_id，需要特殊处理以避免精度丢失
    // 检查是否是大整数（长度超过15位）
    let proxyIdForApi: number | string;
    if (providerInstanceId.length > 15) {
      // 大整数：直接使用字符串，让 json-bigint 在序列化时处理
      this.logger.warn(`Large proxy_id detected: ${providerInstanceId}, using string representation`);
      proxyIdForApi = providerInstanceId;
    } else {
      // 小整数：安全转换为数字
      proxyIdForApi = parseInt(providerInstanceId, 10);
      if (isNaN(proxyIdForApi)) {
        throw new Error(`Invalid providerInstanceId: ${providerInstanceId}`);
      }
    }

    const payload = {
      proxy_ids: [proxyIdForApi], // 根据大小使用数字或字符串
      proxies_type: proxies_type,
      ips: whitelistedIps.join(','), // IPNux API 要求逗号分隔的字符串
    };
    // API 返回 { "Code": 1000, "Message": "success", "Data": true }
    const responseData = await this.postApi<boolean>('/OpenApiEditNodeIpWhiteList', payload);
    return responseData;
  }
  // --- 动态IP Channel和Endpoint方法实现 ---
  async updateChannel(options: UpdateChannelOptions): Promise<boolean> {
    const payload = {
      ChannelId: parseInt(options.providerInstanceId, 10),
      ChannelName: options.channelName,
      ChannelLimitTrafficGb: options.limitTrafficGb,
      Enable: options.enable, // true or false
      ChannelPassword: options.channelPassword,
    };
    // API 返回 { "Code": 1000, "Message": "success", "Data": true }
    const response = await this.postApi<boolean>('/OpenApiEditChannel', payload);
    return response;
  }

  async getChannelTraffic(options: GetChannelTrafficOptions): Promise<ChannelTrafficResult> {
    const payload = {
      ChannelId: parseInt(options.providerInstanceId, 10) || 0, // 0 for all user's channels
      DateType: options.dateType || 0, // 0:Today, 1:Yesterday, 2:Last 7d, 3:Last 30d, 4:Custom
      StartTime: options.startTime, // YYYY-MM-DD
      EndTime: options.endTime, // YYYY-MM-DD
    };
    const response = await this.postApi<IpnuxChannelTrafficDto>('/OpenApiGetUseTraffic', payload);
    return {
      items: response.Items.map((item) => ({
        channelId: item.ChannelId, // 或 providerInstanceId
        useTraffic: item.UseTraffic, // GB
        useTime: new Date(item.UseTime),
      })),
      totalTrafficGb: response.UseTrafficGb.TotalTrafficGb,
    };
  }

  async generateEndpoints(options: GenerateEndpointOptions): Promise<string[]> {
    // 从 productConfigDetails 获取协议类型等
    const ipnuxConfig = options.productConfigDetails?.ipnux_specific_params || {};
    const protocols_type = ipnuxConfig.protocols_type || 3; // 默认为 HTTP+SOCKS5

    const payload: IpnuxGenerateEndpointPayload = {
      ChannelId: parseInt(options.providerInstanceId, 10),
      Location: options.location, // e.g., "US" or "Global"
      StickySessionTime: options.stickySessionTime, // minutes, 0 for rotation
      Count: options.count,
      Domain: options.domain || 'Global', // IPNux specific
      State: options.state,
      City: options.city,
      ProtocolsType: protocols_type, // 1:HTTP, 2:SOCKS5, 3:HTTP+SOCKS5
    };
    // API 返回 { "Code": 1000, "Message": "success", "Data": ["endpoint1", "endpoint2"] }
    const response = await this.postApi<string[]>('/OpenApiGenerateCustomEndpoints', payload);
    return response; // 直接返回字符串列表
  }

  async listEndpoints(providerInstanceId: string): Promise<any[]> {
    this.logger.warn(
      `listEndpoints is not directly supported by the IPNux API. It should be implemented by querying the local 'shop_proxy_endpoints' table for providerInstanceId: ${providerInstanceId}.`,
    );
    // This implementation should be done in the calling service (e.g., DynamicProxyService)
    // which has access to the database repository.
    return Promise.resolve([]);
  }

  async deleteEndpoint(endpointProviderId: string): Promise<boolean> {
    this.logger.warn(
      `deleteEndpoint is not directly supported by the IPNux API. It should be implemented by deleting from the local 'shop_proxy_endpoints' table using the endpoint's unique ID (not provider-specific ID): ${endpointProviderId}.`,
    );
    // This implementation should be done in the calling service (e.g., DynamicProxyService)
    return Promise.resolve(true); // Assume success as there's no API call to fail
  }
}
