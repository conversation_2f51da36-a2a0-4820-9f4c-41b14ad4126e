import { BadRequestException } from '@nestjs/common';
import { MockIpnuxService } from './mock-ipnux.service';
import { Provider, ProviderType } from '../../../../system/provider/entities/provider.entity';

describe('Shop MockIpnuxService', () => {
  let service: MockIpnuxService;

  beforeEach(() => {
    service = new MockIpnuxService();
  });

  describe('initialization guard', () => {
    it('should throw BadRequestException if testConnection called before initialize', async () => {
      await expect(service.testConnection()).rejects.toThrow(
        'Shop MockIpnuxService is not initialized',
      );
    });

    it('should throw BadRequestException if purchaseInstances called before initialize', async () => {
      await expect(service.purchaseInstances({
        productMode: 'STATIC',
        quantity: 1,
        durationDays: 1,
        orderId: 'test',
        customerId: 1,
      } as any)).rejects.toThrow(
        'Shop MockIpnuxService is not initialized',
      );
    });

    it('should throw BadRequestException if getInstanceDetails called before initialize', async () => {
      await expect(service.getInstanceDetails({
        providerInstanceId: 'test',
        productMode: 'STATIC',
      } as any)).rejects.toThrow(
        'Shop MockIpnuxService is not initialized',
      );
    });
  });

  describe('initialization', () => {
    it('should initialize with valid config details', () => {
      const configDetails = { apiKey: 'test', baseUrl: 'http://test.com' };
      
      expect(() => service.initialize(configDetails)).not.toThrow();
      expect(service.getProviderCode()).toBe('ipnux_mock');
    });

    it('should initialize with provider entity', () => {
      const provider = new Provider();
      provider.providerCode = 'test_provider';
      provider.providerType = ProviderType.API;
      
      service.initialize({}, provider);
      expect(service.getProviderCode()).toBe('test_provider');
    });

    it('should throw BadRequestException with invalid config details', () => {
      expect(() => service.initialize('invalid' as any)).toThrow(
        'Invalid configuration details provided to Shop MockIpnuxService'
      );
    });

    it('should default provider code when no entity provided', () => {
      service.initialize({});
      expect(service.getProviderCode()).toBe('ipnux_mock');
    });
  });

  describe('after initialize', () => {
    beforeEach(() => {
      service.initialize({ some: 'config' });
    });

    describe('testConnection', () => {
      it('should return successful connection test', async () => {
        const result = await service.testConnection();
        
        expect(result.success).toBe(true);
        expect(result.message).toBe('Shop Mock connection successful.');
        expect(result.details?.mode).toBe('shop_mock');
      });
    });

    describe('purchaseInstances STATIC', () => {
      it('should return correct quantity with proper IDs and ports', async () => {
        const quantity = 3;
        const results = await service.purchaseInstances({
          productMode: 'STATIC',
          quantity,
          durationDays: 5,
          orderId: 'order123',
          customerId: 1,
          id: 1,
          externalProductId: 'ext_123',
          productConfigDetails: {}
        });

        expect(results).toHaveLength(quantity);
        results.forEach((item, idx) => {
          expect(item.providerInstanceId).toMatch(/^shop_mock_inst_\d+_\d+$/);
          expect(item.port).toBe(9000 + idx);
          expect(item.providerOrderId).toMatch(/^shop_mock_order_\d+$/);
          expect(item.ipAddress).toMatch(/^10\.0\.\d+\.\d+$/);
          expect(item.username).toContain('shop_user_');
          expect(item.password).toContain('shop_pass_');
          expect(item.protocols).toBe('HTTP,SOCKS5');
          expect(item.isUnlimited).toBe(true);
          expect(item.metadata?.shop_mock_instance).toBe(true);
          expect(item.metadata?.isp).toBe('Shop Mock ISP');
        });
      });

      it('should use target location when provided', async () => {
        const results = await service.purchaseInstances({
          productMode: 'STATIC',
          quantity: 1,
          durationDays: 7,
          orderId: 'order456',
          customerId: 2,
          id: 1,
          externalProductId: 'ext_456',
          productConfigDetails: {},
          targetLocation: { country: 'CA', city: 'Toronto' }
        });

        expect(results[0].countryCode).toBe('CA');
        expect(results[0].cityName).toBe('Toronto');
      });

      it('should use default location when not provided', async () => {
        const results = await service.purchaseInstances({
          productMode: 'STATIC',
          quantity: 1,
          durationDays: 7,
          orderId: 'order789',
          customerId: 3,
          id: 1,
          externalProductId: 'ext_789',
          productConfigDetails: {}
        });

        expect(results[0].countryCode).toBe('US');
        expect(results[0].cityName).toBe('New York');
      });
    });

    describe('purchaseInstances DYNAMIC', () => {
      it('should return single channel with correct structure', async () => {
        const [channel] = await service.purchaseInstances({
          productMode: 'DYNAMIC',
          quantity: 1,
          durationDays: 10,
          orderId: 'order456',
          customerId: 99,
          id: 2,
          externalProductId: 'ext_channel',
          productConfigDetails: {}
        });

        expect(channel.providerInstanceId).toMatch(/^shop_mock_channel_\d+$/);
        expect(channel.channelName).toContain('ShopMockChannel_99_');
        expect(channel.password).toMatch(/^shop_mock_pass_\w{8}$/);
        expect(channel.isUnlimited).toBe(true);
        expect(channel.totalFlowMb).toBeUndefined();
        expect(channel.metadata?.shop_mock_instance).toBe(true);
        expect(channel.metadata?.ChannelStatus).toBe(1);
        expect(channel.metadata?.UsedTraffic).toBe(0);
      });

      it('should handle traffic limit when provided', async () => {
        const [channel] = await service.purchaseInstances({
          productMode: 'DYNAMIC',
          quantity: 1,
          durationDays: 15,
          orderId: 'order789',
          customerId: 100,
          id: 2,
          externalProductId: 'ext_limited',
          productConfigDetails: {},
          limitTrafficGb: 50
        });

        expect(channel.totalFlowMb).toBe(50 * 1024);
        expect(channel.isUnlimited).toBe(false);
      });

      it('should use custom channel name when provided', async () => {
        const customName = 'MyCustomChannel';
        const [channel] = await service.purchaseInstances({
          productMode: 'DYNAMIC',
          quantity: 1,
          durationDays: 20,
          orderId: 'order_custom',
          customerId: 101,
          id: 2,
          externalProductId: 'ext_custom',
          productConfigDetails: {},
          channelName: customName
        });

        expect(channel.channelName).toBe(customName);
      });
    });

    describe('purchaseInstances error handling', () => {
      it('should throw BadRequestException for invalid product mode', async () => {
        await expect(
          service.purchaseInstances({
            productMode: 'UNKNOWN' as any,
            quantity: 1,
            durationDays: 1,
            orderId: 'order789',
            customerId: 1,
            id: 1,
            externalProductId: 'ext_invalid',
            productConfigDetails: {}
          }),
        ).rejects.toThrow(BadRequestException);
      });
    });

    describe('getInstanceDetails', () => {
      it('should return null for non-shop_mock instance ID', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'mock_inst_123',
          productMode: 'STATIC',
        });

        expect(details).toBeNull();
      });

      it('should return static instance details for shop_mock instance', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'shop_mock_inst_123456_0',
          productMode: 'STATIC',
        });

        expect(details).not.toBeNull();
        expect(details?.providerInstanceId).toBe('shop_mock_inst_123456_0');
        expect(details?.port).toBe(9000);
        expect(details?.ipAddress).toMatch(/^10\.0\.\d+\.\d+$/);
        expect(details?.username).toContain('shop_user_');
        expect(details?.status).toBe('ACTIVE');
        expect(details?.isUnlimited).toBe(true);
        expect(details?.metadata?.shop_mock_instance).toBe(true);
      });

      it('should return dynamic instance details for shop_mock channel', async () => {
        const details = await service.getInstanceDetails({
          providerInstanceId: 'shop_mock_channel_123456',
          productMode: 'DYNAMIC',
        });

        expect(details).not.toBeNull();
        expect(details?.providerInstanceId).toBe('shop_mock_channel_123456');
        expect(details?.channelName).toContain('ShopMockChannel_');
        expect(details?.password).toMatch(/^shop_mock_pass_\w{8}$/);
        expect(details?.status).toBe('ACTIVE');
        expect(details?.totalFlowMb).toBe(15 * 1024);
        expect(details?.usedFlowMb).toBeGreaterThanOrEqual(0);
        expect(details?.usedFlowMb).toBeLessThan(1500);
        expect(details?.isUnlimited).toBe(false);
        expect(details?.metadata?.shop_mock_instance).toBe(true);
      });
    });

    describe('renewInstance', () => {
      it('should extend expiry correctly', async () => {
        const currentDate = new Date('2023-01-01T00:00:00.000Z');
        const duration = 7;

        const result = await service.renewInstance({
          providerInstanceId: 'shop_mock_inst_x',
          currentExpiresAt: currentDate,
          durationDays: duration,
          productMode: 'STATIC',
        });

        const expectedDate = new Date(currentDate);
        expectedDate.setDate(expectedDate.getDate() + duration);

        expect(result.newExpiresAt).toEqual(expectedDate);
        expect(result.providerOrderId).toMatch(/^shop_mock_renew_\d+$/);
        expect(result.metadata?.renewalType).toBe('shop_mock');
        expect(result.metadata?.originalExpiry).toBe(currentDate.toISOString());
        expect(result.metadata?.productMode).toBe('STATIC');
      });
    });

    describe('replaceInstance', () => {
      it('should replace static instance with new mock instance', async () => {
        const originalId = 'shop_mock_inst_123_0';
        
        const result = await service.replaceInstance!(originalId, {
          targetLocation: { country: 'GB', city: 'London' }
        });

        expect(result.providerInstanceId).toMatch(/^shop_mock_inst_\d+_replaced$/);
        expect(result.ipAddress).toMatch(/^10\.0\.\d+\.\d+$/);
        expect(result.port).toBeGreaterThanOrEqual(9000);
        expect(result.port).toBeLessThan(10000);
        expect(result.countryCode).toBe('GB');
        expect(result.cityName).toBe('London');
        expect(result.metadata?.replacedFrom).toBe(originalId);
        expect(result.metadata?.shop_mock_instance).toBe(true);
      });
    });

    describe('updateInstanceCredentials', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateInstanceCredentials!(
          'shop_mock_inst_123',
          { username: 'newuser', password: 'newpass' }
        );

        expect(result).toBe(true);
      });
    });

    describe('updateInstanceWhitelist', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateInstanceWhitelist!(
          'shop_mock_inst_123',
          ['***********', '********']
        );

        expect(result).toBe(true);
      });
    });

    describe('updateChannel', () => {
      it('should always return true for mock service', async () => {
        const result = await service.updateChannel!({
          providerInstanceId: 'shop_mock_channel_123',
          channelName: 'NewChannelName',
          limitTrafficGb: 100,
          enable: true
        });

        expect(result).toBe(true);
      });
    });

    describe('getChannelTraffic', () => {
      it('should return mock traffic data for 7 days', async () => {
        const result = await service.getChannelTraffic!({
          providerInstanceId: 'shop_mock_channel_123'
        });

        expect(result.items).toHaveLength(7);
        expect(result.totalTrafficGb).toBeGreaterThan(0);
        
        result.items.forEach(item => {
          expect(item.channelId).toBe('shop_mock_channel_123');
          expect(item.useTraffic).toBeGreaterThanOrEqual(0);
          expect(item.useTraffic).toBeLessThan(800);
          expect(item.useTime).toBeInstanceOf(Date);
        });
      });
    });

    describe('generateEndpoints', () => {
      it('should generate correct number of endpoints with shop-specific port range', async () => {
        const count = 5;
        const result = await service.generateEndpoints!({
          providerInstanceId: 'shop_mock_channel_123',
          location: 'US',
          stickySessionTime: 600,
          count,
          productConfigDetails: {}
        });

        expect(result).toHaveLength(count);
        result.forEach((endpoint, idx) => {
          expect(endpoint).toMatch(/^shop-mock-proxy\.example\.com:1100\d$/);
          expect(endpoint).toBe(`shop-mock-proxy.example.com:${11000 + idx}`);
        });
      });

      it('should use custom domain when provided', async () => {
        const customDomain = 'custom.proxy.com';
        const result = await service.generateEndpoints!({
          providerInstanceId: 'shop_mock_channel_123',
          location: 'US',
          stickySessionTime: 300,
          count: 2,
          domain: customDomain,
          productConfigDetails: {}
        });

        expect(result).toHaveLength(2);
        expect(result[0]).toBe(`${customDomain}:11000`);
        expect(result[1]).toBe(`${customDomain}:11001`);
      });
    });
  });
});