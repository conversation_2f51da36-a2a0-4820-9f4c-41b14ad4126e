import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Provider } from '../../../../system/provider/entities/provider.entity';
import {
  ChannelTrafficResult,
  GenerateEndpointOptions,
  GetChannelTrafficOptions,
  GetInstanceDetailsOptions,
  InstanceDetailsResult,
  ProxyProvider,
  PurchaseInstanceOptions,
  PurchaseInstanceResult,
  RenewInstanceOptions,
  RenewInstanceResult,
  UpdateChannelOptions,
} from '../../interfaces/proxy-provider.interface';

@Injectable()
export class MockIpnuxService implements ProxyProvider {
  private readonly logger = new Logger(MockIpnuxService.name);
  private initialized = false;
  private providerCode: string;
  private mockConfig: Record<string, any> = {};

  /**
   * 初始化Mock服务
   */
  public initialize(configDetails: Record<string, any>, providerEntity?: Provider): void {
    // 验证配置参数
    if (configDetails && typeof configDetails !== 'object') {
      throw new BadRequestException('Invalid configuration details provided to Shop MockIpnuxService');
    }

    this.mockConfig = configDetails || {};

    if (providerEntity?.providerCode) {
      this.providerCode = providerEntity.providerCode;
    } else {
      this.providerCode = 'ipnux_mock';
    }

    this.initialized = true;
    this.logger.log(`Shop MockIpnuxService for provider code '${this.providerCode}' initialized successfully.`);
  }

  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new BadRequestException('Shop MockIpnuxService is not initialized. Please call initialize first.');
    }
  }

  public getProviderCode(): string {
    return this.providerCode || 'ipnux_mock';
  }

  public async testConnection(): Promise<{ success: boolean; message?: string; details?: any }> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock connection test for ${this.providerCode}`);

    return {
      success: true,
      message: 'Shop Mock connection successful.',
      details: { provider: this.providerCode, mode: 'shop_mock' },
    };
  }

  async purchaseInstances(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock purchase instances for ${this.providerCode}`, {
      productMode: options.productMode,
      quantity: options.quantity,
      durationDays: options.durationDays,
    });

    if (options.productMode === 'STATIC') {
      return this.purchaseStaticInstancesMock(options);
    } else if (options.productMode === 'DYNAMIC') {
      return this.purchaseDynamicChannelMock(options);
    }

    throw new BadRequestException('Invalid product mode for shop mock purchase. Valid modes: STATIC, DYNAMIC');
  }

  private async purchaseStaticInstancesMock(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    const results: PurchaseInstanceResult[] = [];
    const baseTimestamp = Date.now();

    // 从产品配置中提取地理位置信息
    const locationInfo = this.extractLocationFromProduct(options);

    for (let i = 0; i < options.quantity; i++) {
      const instanceId = `shop_mock_inst_${baseTimestamp}_${i}`;
      const port = 9000 + i; // 使用不同的端口范围区分shop模块
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + options.durationDays);

      results.push({
        providerInstanceId: instanceId,
        providerOrderId: `shop_mock_order_${baseTimestamp}`,
        ipAddress: `10.0.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        port: port,
        username: `shop_user_${instanceId}`,
        password: `shop_pass_${instanceId.slice(-8)}`,
        protocols: 'HTTP,SOCKS5',
        countryCode: locationInfo.countryCode,
        cityName: locationInfo.cityName,
        continentName: locationInfo.continentName,
        activatedAt: new Date(),
        expiresAt: expiresAt,
        isUnlimited: true,
        metadata: {
          isp: 'Shop Mock ISP',
          ip_type_name: 'Datacenter',
          shop_mock_instance: true,
          // 保存产品信息以便续费使用
          productId: options.id,
          productCode: options.productConfigDetails?.productCode,
          originalLocation: {
            country: locationInfo.country,
            city: locationInfo.cityName,
            countryCode: locationInfo.countryCode,
            region: locationInfo.region
          }
        },
      });
    }

    this.logger.log(`Generated ${results.length} shop mock static instances for order ${options.orderId}`, {
      location: `${locationInfo.countryCode}-${locationInfo.cityName}`,
      productId: options.id
    });
    return results;
  }

  private async purchaseDynamicChannelMock(options: PurchaseInstanceOptions): Promise<PurchaseInstanceResult[]> {
    const baseTimestamp = Date.now();
    const channelId = `shop_mock_channel_${baseTimestamp}`;
    const channelName = options.channelName || `ShopMockChannel_${options.customerId}_${baseTimestamp}`;
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + options.durationDays);

    const result: PurchaseInstanceResult = {
      providerInstanceId: channelId,
      channelName: channelName,
      password: `shop_mock_pass_${baseTimestamp.toString().slice(-8)}`,
      activatedAt: new Date(),
      expiresAt: expiresAt,
      totalFlowMb: options.limitTrafficGb ? options.limitTrafficGb * 1024 : undefined,
      isUnlimited: !options.limitTrafficGb || options.limitTrafficGb === 0,
      metadata: {
        ChannelStatus: 1,
        UsedTraffic: 0,
        shop_mock_instance: true,
      },
    };

    this.logger.log(`Generated shop mock dynamic channel for order ${options.orderId}`, { channelName, channelId });
    return [result];
  }

  async renewInstance(options: RenewInstanceOptions): Promise<RenewInstanceResult> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock renew instance ${options.providerInstanceId} for ${options.durationDays} days`);

    const newExpiresAt = new Date(options.currentExpiresAt);
    newExpiresAt.setDate(newExpiresAt.getDate() + options.durationDays);

    return {
      newExpiresAt: newExpiresAt,
      providerOrderId: `shop_mock_renew_${Date.now()}`,
      metadata: {
        renewalType: 'shop_mock',
        originalExpiry: options.currentExpiresAt.toISOString(),
        productMode: options.productMode,
      },
    };
  }

  async getInstanceDetails(options: GetInstanceDetailsOptions): Promise<InstanceDetailsResult | null> {
    this.ensureInitialized();
    this.logger.debug(`Shop Mock get instance details for ${options.providerInstanceId}`);

    // 检查是否是Shop Mock实例
    if (!options.providerInstanceId.startsWith('shop_mock_')) {
      this.logger.warn(`Non-shop-mock instance ID ${options.providerInstanceId} requested from Shop MockIpnuxService`);
      return null;
    }

    if (options.productMode === 'STATIC') {
      return this.getStaticInstanceDetailsMock(options);
    } else if (options.productMode === 'DYNAMIC') {
      return this.getDynamicInstanceDetailsMock(options);
    }

    return null;
  }

  private async getStaticInstanceDetailsMock(options: GetInstanceDetailsOptions): Promise<InstanceDetailsResult> {
    const instanceId = options.providerInstanceId;
    const port = 9000 + parseInt(instanceId.split('_').pop() || '0', 10);

    return {
      providerInstanceId: instanceId,
      ipAddress: `10.0.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
      port: port,
      username: `shop_user_${instanceId}`,
      protocols: 'HTTP,SOCKS5',
      countryCode: 'US',
      cityName: 'New York',
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      status: 'ACTIVE',
      isUnlimited: true,
      metadata: {
        isp: 'Shop Mock ISP',
        ip_type_name: 'Datacenter',
        last_heartbeat_time: new Date().toISOString(),
        shop_mock_instance: true,
      },
    };
  }

  private async getDynamicInstanceDetailsMock(options: GetInstanceDetailsOptions): Promise<InstanceDetailsResult> {
    const channelId = options.providerInstanceId;

    return {
      providerInstanceId: channelId,
      channelName: `ShopMockChannel_${channelId.split('_').pop()}`,
      password: `shop_mock_pass_${channelId.slice(-8)}`,
      status: 'ACTIVE',
      totalFlowMb: 15 * 1024, // 15GB，与system模块区分
      usedFlowMb: Math.floor(Math.random() * 1500),
      isUnlimited: false,
      metadata: {
        CreateTime: new Date().toISOString(),
        shop_mock_instance: true,
      },
    };
  }

  // Static IP 特有方法
  async replaceInstance?(
    providerInstanceId: string,
    options: {
      targetLocation?: any;
      productConfigDetails?: Record<string, any>;
    },
  ): Promise<PurchaseInstanceResult> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock replace instance ${providerInstanceId}`);

    const newInstanceId = `shop_mock_inst_${Date.now()}_replaced`;
    const port = 9000 + Math.floor(Math.random() * 1000);

    return {
      providerInstanceId: newInstanceId,
      ipAddress: `10.0.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
      port: port,
      username: `shop_user_${newInstanceId}`,
      password: `shop_pass_${newInstanceId.slice(-8)}`,
      protocols: 'HTTP,SOCKS5',
      countryCode: options.targetLocation?.country || 'US',
      cityName: options.targetLocation?.city || 'New York',
      activatedAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      isUnlimited: true,
      metadata: {
        isp: 'Shop Mock ISP',
        ip_type_name: 'Datacenter',
        replacedFrom: providerInstanceId,
        shop_mock_instance: true,
      },
    };
  }

  async updateInstanceCredentials?(providerInstanceId: string, credentials: { username?: string; password?: string }, productConfigDetails?: Record<string, any>): Promise<boolean> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock update credentials for instance ${providerInstanceId}`, {
      username: credentials.username,
      hasPassword: !!credentials.password,
    });

    return true;
  }

  async updateInstanceWhitelist?(providerInstanceId: string, whitelistedIps: string[], productConfigDetails?: Record<string, any>): Promise<boolean> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock update whitelist for instance ${providerInstanceId}`, {
      whitelistedIpsCount: whitelistedIps.length,
    });

    return true;
  }

  // Dynamic IP Channel 特有方法
  async updateChannel?(options: UpdateChannelOptions): Promise<boolean> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock update channel ${options.providerInstanceId}`, {
      channelName: options.channelName,
      limitTrafficGb: options.limitTrafficGb,
      enable: options.enable,
    });

    return true;
  }

  async getChannelTraffic?(options: GetChannelTrafficOptions): Promise<ChannelTrafficResult> {
    this.ensureInitialized();
    this.logger.debug(`Shop Mock get channel traffic for ${options.providerInstanceId}`);

    const items = [];
    const days = 7;

    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      items.push({
        channelId: options.providerInstanceId,
        useTraffic: Math.floor(Math.random() * 800), // 与system模块使用不同的流量范围
        useTime: date,
      });
    }

    const totalTrafficGb = items.reduce((sum, item) => sum + item.useTraffic, 0) / 1024;

    return {
      items,
      totalTrafficGb,
    };
  }

  async generateEndpoints?(options: GenerateEndpointOptions): Promise<string[]> {
    this.ensureInitialized();
    this.logger.log(`Shop Mock generate ${options.count} endpoints for channel ${options.providerInstanceId}`, {
      location: options.location,
      stickySessionTime: options.stickySessionTime,
    });

    const endpoints = [];
    const basePort = 11000; // 与system模块使用不同的端口范围

    for (let i = 0; i < options.count; i++) {
      const port = basePort + i;
      const domain = options.domain || 'shop-mock-proxy.example.com';
      const endpoint = `${domain}:${port}`;
      endpoints.push(endpoint);
    }

    return endpoints;
  }

  /**
   * 从产品配置中提取地理位置信息
   */
  private extractLocationFromProduct(options: PurchaseInstanceOptions): {
    countryCode: string;
    cityName: string;
    continentName: string;
    country: string;
    region: string;
  } {
    this.logger.debug(`Extracting location from product options`, {
      targetLocation: options.targetLocation,
      productId: options.id,
      hasProductConfig: !!options.productConfigDetails
    });

    // 1. 优先使用目标位置信息（用户选择的具体位置）
    if (options.targetLocation?.countryCode && options.targetLocation?.city) {
      const continentName = this.getContinent(options.targetLocation.countryCode);
      this.logger.log(`Using target location: ${options.targetLocation.city}, ${options.targetLocation.countryCode}`);
      return {
        countryCode: options.targetLocation.countryCode,
        cityName: options.targetLocation.city,
        continentName: continentName,
        country: this.getCountryName(options.targetLocation.countryCode),
        region: continentName
      };
    }

    // 1.1 处理旧版本的country字段
    if (options.targetLocation?.country && options.targetLocation?.city) {
      const continentName = this.getContinent(options.targetLocation.country);
      this.logger.log(`Using target location (legacy): ${options.targetLocation.city}, ${options.targetLocation.country}`);
      return {
        countryCode: options.targetLocation.country,
        cityName: options.targetLocation.city,
        continentName: continentName,
        country: this.getCountryName(options.targetLocation.country),
        region: continentName
      };
    }

    // 2. 从产品配置详情中提取位置信息
    const productConfig = options.productConfigDetails;
    if (productConfig?.supplierLocation) {
      const supplierLocation = productConfig.supplierLocation;
      const continentName = supplierLocation.continentName || this.getContinent(supplierLocation.countryCode);
      return {
        countryCode: supplierLocation.countryCode || 'US',
        cityName: supplierLocation.cityName || 'Unknown',
        continentName: continentName,
        country: this.getCountryName(supplierLocation.countryCode),
        region: continentName
      };
    }

    // 3. 从产品配置的快照数据中提取
    if (productConfig?.rawSnapshot) {
      try {
        const snapshot = typeof productConfig.rawSnapshot === 'string' 
          ? JSON.parse(productConfig.rawSnapshot) 
          : productConfig.rawSnapshot;
        
        if (snapshot.country_code && snapshot.city_name) {
          const continentName = this.getContinent(snapshot.country_code);
          return {
            countryCode: snapshot.country_code,
            cityName: snapshot.city_name,
            continentName: continentName,
            country: this.getCountryName(snapshot.country_code),
            region: continentName
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to parse rawSnapshot: ${error.message}`);
      }
    }

    // 4. 回退到默认值
    this.logger.warn(`No location info found in product config for product ${options.id}, using default HK/Hong Kong`, {
      targetLocation: options.targetLocation,
      productConfigDetails: options.productConfigDetails
    });
    return {
      countryCode: 'HK',
      cityName: 'Hong Kong',
      continentName: 'Asia',
      country: 'Hong Kong',
      region: 'Asia'
    };
  }

  /**
   * 根据国家代码获取大洲名称
   */
  private getContinent(countryCode: string): string {
    const continentMap: Record<string, string> = {
      // 亚洲
      'CN': 'Asia', 'JP': 'Asia', 'KR': 'Asia', 'IN': 'Asia', 'SG': 'Asia', 
      'TH': 'Asia', 'MY': 'Asia', 'VN': 'Asia', 'ID': 'Asia', 'PH': 'Asia', 
      'HK': 'Asia', 'TW': 'Asia',
      
      // 北美洲
      'US': 'North America', 'CA': 'North America', 'MX': 'North America',
      
      // 欧洲
      'GB': 'Europe', 'DE': 'Europe', 'FR': 'Europe', 'IT': 'Europe', 
      'ES': 'Europe', 'NL': 'Europe', 'CH': 'Europe', 'SE': 'Europe', 
      'NO': 'Europe', 'PL': 'Europe', 'RU': 'Europe',
      
      // 南美洲
      'BR': 'South America', 'AR': 'South America', 'CL': 'South America', 'CO': 'South America',
      
      // 非洲
      'ZA': 'Africa', 'EG': 'Africa', 'NG': 'Africa',
      
      // 大洋洲
      'AU': 'Oceania', 'NZ': 'Oceania'
    };
    
    return continentMap[countryCode] || 'Unknown';
  }

  /**
   * 根据国家代码获取国家名称
   */
  private getCountryName(countryCode: string): string {
    const countryMap: Record<string, string> = {
      'CN': 'China', 'JP': 'Japan', 'KR': 'South Korea', 'IN': 'India', 
      'SG': 'Singapore', 'TH': 'Thailand', 'MY': 'Malaysia', 'VN': 'Vietnam', 
      'ID': 'Indonesia', 'PH': 'Philippines', 'HK': 'Hong Kong', 'TW': 'Taiwan',
      'US': 'United States', 'CA': 'Canada', 'MX': 'Mexico',
      'GB': 'United Kingdom', 'DE': 'Germany', 'FR': 'France', 'IT': 'Italy', 
      'ES': 'Spain', 'NL': 'Netherlands', 'CH': 'Switzerland', 'SE': 'Sweden', 
      'NO': 'Norway', 'PL': 'Poland', 'RU': 'Russia',
      'BR': 'Brazil', 'AR': 'Argentina', 'CL': 'Chile', 'CO': 'Colombia',
      'ZA': 'South Africa', 'EG': 'Egypt', 'NG': 'Nigeria',
      'AU': 'Australia', 'NZ': 'New Zealand'
    };
    
    return countryMap[countryCode] || countryCode;
  }
}
