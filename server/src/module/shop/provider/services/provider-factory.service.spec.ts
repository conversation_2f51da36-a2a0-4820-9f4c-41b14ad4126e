import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { NotFoundException } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ProviderFactoryService } from './provider-factory.service';
import { IpnuxService } from '../implementations/ipnux/ipnux.service';
import { MockIpnuxService } from '../implementations/mock/mock-ipnux.service';

describe('Shop ProviderFactoryService', () => {
  let service: ProviderFactoryService;
  let configService: jest.Mocked<ConfigService>;
  let moduleRef: jest.Mocked<ModuleRef>;
  let mockIpnuxService: IpnuxService;
  let mockShopMockIpnuxService: MockIpnuxService;

  beforeEach(async () => {
    mockIpnuxService = {} as IpnuxService;
    mockShopMockIpnuxService = {} as MockIpnuxService;

    const mockConfigService = {
      get: jest.fn(),
    } as any;

    const mockModuleRef = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProviderFactoryService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
        {
          provide: IpnuxService,
          useValue: mockIpnuxService,
        },
      ],
    }).compile();

    service = module.get<ProviderFactoryService>(ProviderFactoryService);
    configService = module.get(ConfigService);
    moduleRef = module.get(ModuleRef);
  });

  describe('getProvider', () => {
    describe('when MOCK_IPNUX_PROVIDER is enabled', () => {
      beforeEach(() => {
        configService.get.mockImplementation((key: string, defaultValue: string) => {
          if (key === 'MOCK_IPNUX_PROVIDER') return 'true';
          return defaultValue;
        });
        moduleRef.get.mockImplementation((token: string) => {
          if (token === 'ShopMockIpnuxService') return mockShopMockIpnuxService;
          throw new Error(`Unexpected token: ${token}`);
        });
      });

      it('should return ShopMockIpnuxService for ipnux provider', () => {
        const provider = service.getProvider('ipnux');

        expect(provider).toBe(mockShopMockIpnuxService);
        expect(moduleRef.get).toHaveBeenCalledWith('ShopMockIpnuxService', { strict: false });
        expect(configService.get).toHaveBeenCalledWith('MOCK_IPNUX_PROVIDER', 'false');
      });

      it('should return ShopMockIpnuxService for IPNUX provider (case insensitive)', () => {
        const provider = service.getProvider('IPNUX');

        expect(provider).toBe(mockShopMockIpnuxService);
        expect(moduleRef.get).toHaveBeenCalledWith('ShopMockIpnuxService', { strict: false });
      });
    });

    describe('when MOCK_IPNUX_PROVIDER is disabled', () => {
      beforeEach(() => {
        configService.get.mockImplementation((key: string, defaultValue: string) => {
          if (key === 'MOCK_IPNUX_PROVIDER') return 'false';
          return defaultValue;
        });
      });

      it('should return real IpnuxService for ipnux provider', () => {
        const provider = service.getProvider('ipnux');

        expect(provider).toBe(mockIpnuxService);
        expect(moduleRef.get).not.toHaveBeenCalled();
      });
    });

    describe('mock environment variable variations', () => {
      it.each([
        ['true', true],
        ['1', true],
        ['yes', true],
        ['false', false],
        ['0', false],
        ['no', false],
        ['', false],
        [undefined, false],
      ])('should handle MOCK_IPNUX_PROVIDER=%s correctly', (envValue, shouldUseMock) => {
        configService.get.mockImplementation((key: string, defaultValue: string) => {
          if (key === 'MOCK_IPNUX_PROVIDER') return envValue || defaultValue;
          return defaultValue;
        });

        if (shouldUseMock) {
          moduleRef.get.mockReturnValue(mockShopMockIpnuxService);
        }

        const provider = service.getProvider('ipnux');

        if (shouldUseMock) {
          expect(provider).toBe(mockShopMockIpnuxService);
          expect(moduleRef.get).toHaveBeenCalledWith('ShopMockIpnuxService', { strict: false });
        } else {
          expect(provider).toBe(mockIpnuxService);
        }
      });
    });

    describe('error handling', () => {
      it('should throw NotFoundException for unknown provider code', () => {
        expect(() => service.getProvider('unknown_provider')).toThrow(NotFoundException);
        expect(() => service.getProvider('unknown_provider')).toThrow(
          'Provider with code "unknown_provider" not found.'
        );
      });

      it('should throw NotFoundException for empty provider code', () => {
        expect(() => service.getProvider('')).toThrow(NotFoundException);
      });

      it('should handle ModuleRef.get throwing error when getting mock service', () => {
        configService.get.mockReturnValue('true');
        moduleRef.get.mockImplementation(() => {
          throw new Error('Service not found');
        });

        expect(() => service.getProvider('ipnux')).toThrow(NotFoundException);
        expect(() => service.getProvider('ipnux')).toThrow(
          'Could not instantiate provider "ipnux". Service not found'
        );
      });
    });
  });

  describe('getAllProviders', () => {
    it('should return array with ipnux provider when mock is disabled', () => {
      configService.get.mockReturnValue('false');

      const providers = service.getAllProviders();

      expect(providers).toHaveLength(1);
      expect(providers[0]).toBe(mockIpnuxService);
    });

    it('should return array with mock service when mock is enabled', () => {
      configService.get.mockReturnValue('true');
      moduleRef.get.mockReturnValue(mockShopMockIpnuxService);

      const providers = service.getAllProviders();

      expect(providers).toHaveLength(1);
      expect(providers[0]).toBe(mockShopMockIpnuxService);
    });

    it('should return empty array when provider instantiation fails', () => {
      configService.get.mockReturnValue('true');
      moduleRef.get.mockImplementation(() => {
        throw new Error('Service initialization failed');
      });

      const providers = service.getAllProviders();

      expect(providers).toHaveLength(0);
    });
  });
});