import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository, SelectQueryBuilder } from 'typeorm';
import { ProductQueryDto, ProductSortBy, SortOrder } from './dto/product-query.dto';
import { Product } from './entities/product.entity';
// import { PriceDisplayService } from './services/price-display.service'; // Temporarily remove or re-evaluate
import { ShopProductDto } from './dto/shop-product.dto';
import { ProductListResponseDto } from './dto/product-list-response.dto';
import { ProductResolveParamsDto } from './dto/product-resolve-params.dto';
import { ProductStatsDto } from './dto/product-stats.dto';
import { ResolvedProductDto } from './dto/ResolvedProductDto';
import { ProductResolveResultDto } from './dto/ProductResolveResultDto';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    // private readonly priceDisplayService: PriceDisplayService, // Temporarily remove
  ) {}

  async findAll(queryDto: ProductQueryDto): Promise<ProductListResponseDto> {
    this.logger.debug(`进入 ProductsService.findAll, 查询参数: ${JSON.stringify(queryDto)}`);
    const {
      keyword,
      priceMin,
      priceMax,
      productType,
      proxyType, // 添加代理类型参数
      region,
      country,
      city,
      cityId, // This is standardLocationCityId in practice for query
      currency,
      status = '0', // Default to '0' (normal) if not provided
      sortBy = ProductSortBy.CREATETIME, // Default sort
      sortOrder = SortOrder.DESC, // Default sort order
    } = queryDto;

    const queryBuilder = this.productRepository.createQueryBuilder('product')
      .leftJoinAndSelect('product.locationCity', 'locationCity')
      .leftJoinAndSelect('locationCity.country', 'locationCountry')
      .leftJoinAndSelect('locationCountry.region', 'locationRegion')
      .where('product.delFlag = :delFlag', { delFlag: '0' }); // Always filter non-deleted

    if (status) {
      queryBuilder.andWhere('product.status = :status', { status });
    }

    // Keyword search (applies if searchProducts is merged or if findAll also needs keyword)
    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('product.productName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('product.productDesc LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('product.standardCityName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('product.standardCountryName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('product.standardRegionName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('locationCity.cityName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('locationCountry.countryName LIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('locationRegion.regionName LIKE :keyword', { keyword: `%${keyword}%` });
        }),
      );
    }

    if (productType) {
      queryBuilder.andWhere('product.productType = :productType', { productType });
    }

    // 添加代理类型查询支持 - 使用虚拟列 config_proxy_type
    if (proxyType) {
      queryBuilder.andWhere('product.config_proxy_type = :proxyType', { proxyType });
    }

    if (currency) {
      queryBuilder.andWhere('product.currency = :currency', { currency });
    }

    // Price range
    if (priceMin !== undefined) {
      queryBuilder.andWhere('product.price >= :priceMin', { priceMin });
    }
    if (priceMax !== undefined) {
      queryBuilder.andWhere('product.price <= :priceMax', { priceMax });
    }

    // Location based filtering
    if (cityId) {
      // 使用正确的字段名 city_id
      queryBuilder.andWhere('product.cityId = :cityId', { cityId });
    } else {
      if (city) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.where('locationCity.cityName LIKE :city', { city: `%${city}%` });
          }),
        );
      }
      if (country) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.where('locationCountry.countryName LIKE :country', { country: `%${country}%` });
          }),
        );
      }
      if (region) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.where('locationRegion.regionName LIKE :region', { region: `%${region}%` });
          }),
        );
      }
    }

    // Sorting
    let sortField = `product.${sortBy}`;
    if (sortBy === ProductSortBy.LOCATION) {
      // 使用标准化地理位置进行排序
      queryBuilder.orderBy('locationRegion.regionName', sortOrder.toUpperCase() as 'ASC' | 'DESC', 'NULLS LAST');
      queryBuilder.addOrderBy('locationCountry.countryName', sortOrder.toUpperCase() as 'ASC' | 'DESC', 'NULLS LAST');
      queryBuilder.addOrderBy('locationCity.cityName', sortOrder.toUpperCase() as 'ASC' | 'DESC', 'NULLS LAST');
    } else if (sortBy === ProductSortBy.CREATETIME) {
      sortField = 'product.createTime'; // Assuming createTime is on the base entity or Product entity
      queryBuilder.orderBy(sortField, sortOrder.toUpperCase() as 'ASC' | 'DESC');
    } else if (sortBy === ProductSortBy.PRICE) {
      sortField = 'product.price';
      queryBuilder.orderBy(sortField, sortOrder.toUpperCase() as 'ASC' | 'DESC');
    } else if (sortBy === ProductSortBy.NAME) {
      sortField = 'product.productName';
      queryBuilder.orderBy(sortField, sortOrder.toUpperCase() as 'ASC' | 'DESC');
    } else {
      // Default sort if sortBy is not recognized or if it's a generic field like 'sortOrder'
      queryBuilder.orderBy('product.sortOrder', 'ASC').addOrderBy('product.createTime', 'DESC');
    }

    const entities = await queryBuilder.getMany();
    const dtoList = ShopProductDto.fromEntities(entities);

    return new ProductListResponseDto(dtoList, entities.length, 1, entities.length);
  }

  async findOne(id: number): Promise<ShopProductDto> {
    const productEntity = await this.productRepository.findOne({
      where: {
        id: id, // 使用数据库主键id
        status: '0',
        delFlag: '0',
      },
      relations: ['locationCity', 'locationCity.country', 'locationCity.country.region'],
    });

    if (!productEntity) {
      // 保持与 static-proxy.service.ts 中单个实体查找一致的错误处理方式
      throw new NotFoundException(`未找到ID为 ${id} 的商品或商品状态不正常。`);
    }
    return ShopProductDto.fromEntity(productEntity);
  }

  async resolveProduct(params: ProductResolveParamsDto): Promise<ProductResolveResultDto> {
    this.logger.log(`[ProductsService] resolveProduct called with params: ${JSON.stringify(params, null, 2)}`);
    this.logger.debug(`Attempting to resolve product with params: ${JSON.stringify(params, null, 2)}`);
    this.logger.log(`Provider Code: ${params.providerCode}, Proxies Type: ${params.proxiesType}, Supplier City ID: ${params.supplierCityId}, Proxies Format: ${params.proxiesFormat}`);

    // --- 第一阶段：精确匹配 ---
    this.logger.log(`Starting exact match phase.`);
    this.logger.log(`Exact match criteria: provider.provider_code = '${params.providerCode}' (Note: externalProductId match based on params.proxiesType is removed as it's not a SKU)`);
    // 精确匹配条件: provider.provider_code
    // 移除了 product.externalProductId = params.proxiesType 的条件，因为 params.proxiesType 不是SKU
    // 同时确保产品状态正常 (status='0', delFlag='0')
    try {
      const exactMatchQuery = this.productRepository
        .createQueryBuilder('product')
        .innerJoin('product.provider', 'provider')
        .where('provider.provider_code = :providerCode', { providerCode: params.providerCode })
        // .andWhere('product.externalProductId = :proxiesType', { proxiesType: params.proxiesType }) // REMOVED - params.proxiesType is not externalProductId
        .andWhere('product.status = :status', { status: '0' })
        .andWhere('product.delFlag = :delFlag', { delFlag: '0' });

      // 如果将来 params 中能提供一个确切的 externalProductId，可以在这里加回来
      // if (params.externalProductId) {
      //   exactMatchQuery.andWhere('product.externalProductId = :externalProductId', { externalProductId: params.externalProductId });
      // }

      this.logger.debug(`Exact match SQL (approximate): ${exactMatchQuery.getSql()}`);
      const exactMatchProducts = await exactMatchQuery.getMany(); // Use getMany in case providerCode alone isn't unique enough for a single product

      if (exactMatchProducts.length === 1) {
        const exactMatchProduct = exactMatchProducts[0];
        this.logger.log(`Exact match FOUND (based on providerCode): Product ID ${exactMatchProduct.id}`);
        // 如果只找到一个，可以考虑返回。但通常 resolveProduct 需要更精确的匹配。
        // 为了安全起见，我们可能仍然希望它通过后续的模糊匹配来确认其他参数。
        // 暂时注释掉直接返回，让它继续走模糊匹配流程，除非业务明确 providerCode 单独匹配就足够。
        // return { success: true, product: ResolvedProductDto.fromEntity(exactMatchProduct) };
        this.logger.log(`Exact match by providerCode found one product, but proceeding to fuzzy match for further refinement.`);
      } else if (exactMatchProducts.length > 1) {
        this.logger.log(`Exact match by providerCode found ${exactMatchProducts.length} products. Proceeding to fuzzy match for refinement.`);
      } else {
        this.logger.log(`Exact match NOT found (based on providerCode).`);
      }
    } catch (error) {
      this.logger.error(`Error during exact match for product resolution: ${error.message}`, error.stack);
    }

    this.logger.log(`Proceeding to fuzzy match for proxiesType '${params.proxiesType}', supplierCityId '${params.supplierCityId}', and proxiesFormat '${params.proxiesFormat}'.`);

    // --- 第二阶段：模糊匹配 ---
    this.logger.log(`Starting fuzzy match phase.`);
    try {
      const proxiesTypeNamePattern = `%${params.proxiesType}%`; // For matching against product name
      this.logger.log(`Fuzzy match criteria: supplierCityId (LIKE or ID) = '${params.supplierCityId}', productName (LIKE) = '${proxiesTypeNamePattern}' (proxiesFormat NOT USED)`); // Updated log

      const queryBuilder = this.productRepository.createQueryBuilder('product').leftJoinAndSelect('product.provider', 'provider');

      // Provider code must still match if provided
      if (params.providerCode) {
        queryBuilder.andWhere('provider.provider_code = :providerCode', { providerCode: params.providerCode });
      }

      // 城市/国家/区域匹配: supplierCityId 模糊匹配标准化地理位置字段
      // 同时增加对 cityId 的精确匹配尝试
      queryBuilder.andWhere(
        // Changed from where to andWhere to combine with providerCode if present
        new Brackets((qb) => {
          qb.where('product.cityName LIKE :supplierCityIdPattern', { supplierCityIdPattern: `%${params.supplierCityId}%` })
            .orWhere('product.countryName LIKE :supplierCityIdPattern', { supplierCityIdPattern: `%${params.supplierCityId}%` })
            .orWhere('product.regionName LIKE :supplierCityIdPattern', { supplierCityIdPattern: `%${params.supplierCityId}%` });
          if (!isNaN(parseInt(params.supplierCityId, 10))) {
            qb.orWhere('product.cityId = :exactSupplierCityId', { exactSupplierCityId: parseInt(params.supplierCityId, 10) });
            this.logger.log(`Fuzzy match also attempting exact match for cityId = ${parseInt(params.supplierCityId, 10)}`);
          }
        }),
      );

      // 产品名称模糊匹配: productName 包含 proxiesType (描述性文本)
      // 移除了对 externalProductId 的模糊匹配，因为它不适用描述性文本
      queryBuilder.andWhere(
        new Brackets((qb) => {
          // qb.where('product.externalProductId LIKE :proxiesTypePattern', { proxiesTypePattern }) // REMOVED
          qb.where('product.productName LIKE :proxiesTypeNamePattern', { proxiesTypeNamePattern });
        }),
      );

      // 用户指示：proxiesFormat 参数不应参与匹配，移除相关逻辑
      // if (params.proxiesFormat !== undefined) {
      //   // 具体的 JSON 查询语法取决于数据库类型 (e.g., PostgreSQL, MySQL)
      //   // PostgreSQL: product.productConfigDetails ->> 'format'
      //   // MySQL: JSON_UNQUOTE(JSON_EXTRACT(product.productConfigDetails, '$.format'))
      //   // 以下为 MySQL/MariaDB 示例, 请根据您的数据库调整
      //   const formatValue = params.proxiesFormat.toString();
      //   // TypeORM doesn't have a universal way to query JSON that works across all DBs easily with queryBuilder.
      //   // Using a raw string for the condition is often necessary for complex JSON queries.
      //   // Ensure this path '$.proxiesFormat' is correct for your JSON structure.
      //   queryBuilder.andWhere(`JSON_UNQUOTE(JSON_EXTRACT(product.productConfigDetails, '$.proxiesFormat')) = :proxiesFormat`, { proxiesFormat: formatValue });
      //   this.logger.log(
      //     `Fuzzy match attempting to filter by productConfigDetails.proxiesFormat = ${formatValue}. Ensure JSON query syntax ('$.proxiesFormat') is correct for your DB (MySQL example shown).`,
      //   );
      // }

      queryBuilder.andWhere('product.status = :status', { status: '0' });
      queryBuilder.andWhere('product.delFlag = :delFlag', { delFlag: '0' });

      queryBuilder.orderBy('product.id', 'ASC');

      this.logger.debug(`Fuzzy match SQL (approximate): ${queryBuilder.getSql()}`);
      const fuzzyMatchProducts = await queryBuilder.getMany();
      this.logger.log(`Fuzzy match query returned ${fuzzyMatchProducts.length} products.`);

      if (fuzzyMatchProducts && fuzzyMatchProducts.length > 0) {
        const chosenProduct = fuzzyMatchProducts[0]; // Consider if multiple matches require specific selection logic
        this.logger.log(`Fuzzy match FOUND. Selected product ID ${chosenProduct.id} from ${fuzzyMatchProducts.length} potential matches.`);
        return { success: true, product: ResolvedProductDto.fromEntity(chosenProduct) };
      }
      this.logger.log(`Fuzzy match NOT found.`);
    } catch (error) {
      this.logger.error(`Error during fuzzy match for product resolution: ${error.message}`, error.stack);
    }

    this.logger.warn(`No product found after both exact and fuzzy matching for params: ${JSON.stringify(params, null, 2)}`);
    return { success: false, error: 'No matching product found.' };
  }

  // Placeholder for getProductStats
  async getProductStats(): Promise<ProductStatsDto> {
    const activeProducts = await this.productRepository.find({
      where: { status: '0', delFlag: '0' },
    });

    if (activeProducts.length === 0) {
      return {
        totalProducts: 0,
        totalLocations: 0,
        averagePrice: 0,
        currency: 'CNY', // Default currency
        priceRange: { min: 0, max: 0 },
      };
    }

    const totalProducts = activeProducts.length;

    const locations = new Set<string>();
    activeProducts.forEach((p) => {
      const cityName = p.productConfigDetails?.supplierLocation?.cityName;
      if (cityName) locations.add(cityName);
    });
    const totalLocations = locations.size;

    const prices = activeProducts.map((p) => p.price).filter((p) => p !== null && p !== undefined) as number[];
    const averagePrice = prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0;
    const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

    // Assuming all products share the same currency or using the first one's as representative
    const currency = activeProducts[0]?.currency || 'CNY';

    return {
      totalProducts,
      totalLocations,
      averagePrice: parseFloat(averagePrice.toFixed(2)),
      currency,
      priceRange: {
        min: parseFloat(minPrice.toFixed(2)),
        max: parseFloat(maxPrice.toFixed(2)),
      },
    };
  }

  // searchProducts is effectively merged into findAll by adding 'keyword' to ProductQueryDto
  // If a dedicated /search endpoint is still needed, it can call findAll with the keyword.
  // For now, we assume 'keyword' in ProductQueryDto handles search.

  // getRecommendProducts - this was in the original file.
  // If it's not part of the current task's scope (based on standardized-products.ts),
  // it can be removed or kept if it serves another purpose.
  // For now, I'll keep it but comment it out if not directly requested.
  /*
  async getRecommendProducts(limit: number = 5): Promise<ShopProductDto[]> {
    const products = await this.productRepository.find({
      where: {
        status: '0',
        delFlag: '0',
      },
      order: {
        salesCount: 'DESC',
        sortOrder: 'ASC',
      },
      take: limit,
    });
    return ShopProductDto.fromEntities(products);
  }
  */
}
