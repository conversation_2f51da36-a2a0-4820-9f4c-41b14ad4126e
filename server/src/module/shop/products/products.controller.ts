import { Controller, Get, Param, Post, Query, ParseIntPipe, NotFoundException, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Public } from '../../../common/decorators/public.decorator';
import { ProductsService } from './products.service';
import { ProductLocationSyncService } from './services/product-location-sync.service';
import { ProductQueryDto } from './dto/product-query.dto';
import { ShopProductDto } from './dto/shop-product.dto';
import { PublicShopProductDto } from './dto/public-shop-product.dto';
import { ProductListResponseDto } from './dto/product-list-response.dto';
import { PublicProductListResponseDto } from './dto/public-product-list-response.dto';
import { ProductResolveParamsDto } from './dto/product-resolve-params.dto';
import { ProductStatsDto } from './dto/product-stats.dto';
import { ProductResolveResultDto } from './dto/ProductResolveResultDto';

@ApiTags('Shop / Standardized Products') // 更新Tag名称
@Controller('shop/products') // 基础路径不变
export class ProductsController {
  private readonly logger = new Logger(ProductsController.name);

  constructor(
    private readonly productsService: ProductsService,
    private readonly locationSyncService: ProductLocationSyncService,
  ) {}

  @Get()
  @Public() // 公开接口，不需要权限认证
  @ApiOperation({
    summary: '获取标准化产品列表 (支持搜索)',
    description: '根据查询参数获取产品列表。如果提供了 `keyword`，则执行搜索。不包含敏感的成本价格信息。',
  })
  @ApiResponse({ description: '获取成功', type: [PublicShopProductDto] })
  async getProducts(@Query() query: ProductQueryDto): Promise<PublicShopProductDto[]> {
    const result = await this.productsService.findAll(query);
    // 转换为公开DTO，移除敏感信息
    const publicList = PublicShopProductDto.fromShopProductDtos(result.list);
    
    return publicList;
  }

  @Get('stats')
  @ApiOperation({ summary: '获取产品统计信息' })
  @ApiResponse({ description: '获取成功', type: ProductStatsDto })
  async getProductStats(): Promise<ProductStatsDto> {
    return this.productsService.getProductStats();
  }

  @Get('sync-status')
  @ApiOperation({
    summary: '获取地理位置同步状态',
    description: '获取需要同步地理位置的商品数量'
  })
  @ApiResponse({ description: '获取成功' })
  async getSyncStatus() {
    const count = await this.locationSyncService.getProductsNeedingSyncCount();
    return {
      message: '获取同步状态成功',
      data: {
        needsSyncCount: count
      }
    };
  }

  @Get('resolve')
  @ApiOperation({ summary: '根据供应商信息解析标准化产品' })
  @ApiResponse({ description: '解析成功，返回产品解析结果。', type: ProductResolveResultDto })
  async resolveProduct(@Query() params: ProductResolveParamsDto): Promise<ProductResolveResultDto> {
    this.logger.log(`Received resolveProduct request with params: ${JSON.stringify(params)}`);
    return this.productsService.resolveProduct(params);
  }

  // ':id' 路由应该在 'stats' 和 'resolve' 之后，以避免路由冲突
  @Get(':id')
  @Public() // 公开接口，不需要权限认证
  @ApiOperation({ summary: '获取单个标准化产品详情' })
  @ApiParam({ name: 'id', description: '产品数据库主键ID', type: Number })
  @ApiResponse({ description: '获取成功。如果产品不存在或不可用，响应体为null。不包含敏感的成本价格信息。', type: PublicShopProductDto })
  @ApiResponse({ status: 404, description: '产品未找到 (如果控制器选择抛出异常而不是返回null)' })
  async getProductById(@Param('id', ParseIntPipe) id: number): Promise<PublicShopProductDto | null> {
    const product = await this.productsService.findOne(id);
    if (!product) {
      return null;
    }
    
    // 转换为公开DTO，移除敏感信息
    const publicProduct = PublicShopProductDto.fromShopProductDto(product);
    
    return publicProduct;
  }

  @Post('sync-locations')
  @ApiOperation({
    summary: '同步商品地理位置信息',
    description: '将商品配置中的地理位置信息同步到标准化字段中'
  })
  @ApiResponse({ description: '同步成功' })
  async syncProductLocations() {
    const result = await this.locationSyncService.syncAllProductLocations();
    return {
      message: '地理位置同步完成',
      data: result
    };
  }





  // 移除了 getRecommendProducts 端点，因为它不在 standardized-products.ts 的范围内
  // @Get('recommend')
  // @ApiOperation({ summary: '获取推荐商品' })
  // @ApiQuery({ name: 'limit', description: '推荐商品数量', required: false, type: Number })
  // @ApiResponse({  description: '获取成功' })
  // async getRecommendProducts(@Query('limit') limit?: number) {
  //   return this.productsService.getRecommendProducts(limit);
  // }
}
