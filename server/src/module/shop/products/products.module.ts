import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductsController } from './products.controller';
import { ProductsService } from './products.service';
import { Product } from './entities/product.entity';
import { PriceDisplayService } from './services/price-display.service';
import { ProxyTypeService } from './services/proxy-type.service';
import { ProductLocationSyncService } from './services/product-location-sync.service';
import { DictModule } from '../../system/dict/dict.module';
import { LocationCity } from '../../system/location/entities/location-city.entity';
import { LocationCountry } from '../../system/location/entities/location-country.entity';
import { LocationRegion } from '../../system/location/entities/location-region.entity';
import { LocationParserFactoryService } from '../../system/products/services/location-parser-factory.service';
import { IpnuxLocationParser } from '../../system/products/parsers/ipnux-location.parser';
import { CityTranslationService } from './services/city-translation.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, LocationCity, LocationCountry, LocationRegion]),
    DictModule
  ],
  controllers: [ProductsController],
  providers: [ProductsService, PriceDisplayService, ProxyTypeService, ProductLocationSyncService, LocationParserFactoryService, IpnuxLocationParser, CityTranslationService],
  exports: [ProductsService, PriceDisplayService, ProxyTypeService, ProductLocationSyncService],
})
export class ProductsModule {}
