import { BadRequestException, ConflictException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { MailService } from 'src/module/common/mail/mail.service';
import { RedisService } from 'src/module/common/redis/redis.service';
import { Connection, In, LessThan, Repository } from 'typeorm';
import { DatabaseHealthService } from '../../../common/database/database-health.service';
import { PaymentService } from '../../payment/payment.service';
import { ConfigService as SystemConfigService } from '../../system/config/config.service';
import { Provider } from '../../system/provider/entities/provider.entity';
import { Customer } from '../auth/entities/customer.entity';
import { InventoryService } from '../inventory/inventory.service';
import { NotificationsService } from '../notifications/notifications.service';
import { Product } from '../products/entities/product.entity';
import { ProductsService } from '../products/products.service';
import { ProxyInstance, ProxyInstanceStatus } from '../proxy/entities/proxy-instance.entity';
import { SettingsService } from '../settings/settings.service';
import { AccountType, Transaction, TransactionDirection, TransactionStatus, TransactionType } from '../transactions/entities/transaction.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { CreateRechargeOrderDto } from './dto/create-recharge-order.dto';
import { CreateStaticIpOrderDto } from './dto/create-static-ip-order.dto';
import { OrderPublicDto, ProductPublicDto } from './dto/order-public.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { RenewStaticIpOrderDto } from './dto/renew-static-ip-order.dto';
import { Order, OrderStatus, OrderType, PaymentMethod } from './entities/order.entity';
import { OrderPaymentCompleteEvent } from './events/order-payment-complete.event';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);
  private readonly statusDescriptions = {
    [OrderStatus.PENDING]: '待支付',
    [OrderStatus.PAID]: '已支付',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDED]: '已退款',
  };

  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Provider)
    private providerRepository: Repository<Provider>,
    @InjectRepository(ProxyInstance)
    private proxyInstanceRepository: Repository<ProxyInstance>,
    private connection: Connection,
    private productsService: ProductsService,
    private notificationsService: NotificationsService,
    private settingsService: SettingsService,
    private redisService: RedisService,
    private mailService: MailService,
    private readonly configService: ConfigService,
    private readonly systemConfigService: SystemConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly inventoryService: InventoryService,
    private readonly databaseHealthService: DatabaseHealthService,
    @Inject('PaymentService') private readonly paymentService: PaymentService,
  ) {}

  async create(customerInfo: number, createOrderDto: CreateOrderDto): Promise<Order> {
    const customerId = customerInfo;
    if (typeof customerId !== 'number') {
      throw new BadRequestException('无效的客户信息');
    }

    const { id, quantity, paymentMethod, address, contactName, contactMobile, remark } = createOrderDto;

    // 检查商品是否存在并获取价格
    const product = await this.productsService.findOne(id); // Use productsService here

    // 在创建订单时，确保使用正确的产品价格
    const orderPrice = product.discountPrice ? parseFloat(product.discountPrice.toString()) : parseFloat(product.price.toString());

    // 确保单价计算正确
    const unitPrice = orderPrice / quantity;

    // 计算总金额
    const totalAmount = unitPrice * quantity;
    const paidAmount = totalAmount;

    // 创建订单
    const order = this.orderRepository.create({
      orderId: this.generateOrderId(),
      customerId,
      productId: product.id, // Save product.id as productId
      quantity,
      totalAmount,
      paidAmount,
      orderStatus: OrderStatus.PENDING,
      paymentMethod: this.mapPaymentMethodStringToEnum(paymentMethod) || PaymentMethod.ALIPAY, // 默认支付宝，或根据业务逻辑调整
      address,
      contactName,
      contactMobile,
      status: '0',
      delFlag: '0',
      createBy: customerId.toString(),
      updateBy: customerId.toString(),
      remark,
    });

    const savedOrder = await this.orderRepository.save(order);

    // 所有订单都是在线支付

    // Return the basic saved order, findOne will enrich it if needed later
    return savedOrder;
  }

  async createStaticIpOrder(customerId: number, createStaticIpOrderDto: CreateStaticIpOrderDto): Promise<Order> {
    this.logger.log(`[createStaticIpOrder] Received DTO: ${JSON.stringify(createStaticIpOrderDto)}`);
    const { id, quantity, duration, paymentMethod, remark } = createStaticIpOrderDto;

    // 1. 验证并获取产品信息
    const product = await this.productRepository.findOne({
      where: {
        id: parseInt(id), // 改为使用主键id
        delFlag: '0',
      },
    });

    if (!product) {
      throw new BadRequestException(`产品ID ${id} 不存在或已删除`);
    }
    this.logger.log(`[createStaticIpOrder] Product found: ${product.productName}, productProxyCategory: ${product.productProxyCategory}`);

    // 2. 验证并获取用户信息
    const customer = await this.customerRepository.findOne({
      where: {
        customerId,
        delFlag: '0',
      },
    });

    if (!customer) {
      throw new BadRequestException('用户信息不存在');
    }

    // 3. 业务逻辑验证
    if (quantity <= 0 || quantity > 100) {
      throw new BadRequestException('购买数量必须在1-100之间');
    }

    if (duration <= 0 || duration > 365) {
      throw new BadRequestException('有效期必须在1-365天之间');
    }

    // 4. 获取实时美元转人民币汇率
    const USD_TO_CNY_RATE = await this.inventoryService.getUsdToCnyRate();
    this.logger.log(`Using exchange rate: 1 USD = ${USD_TO_CNY_RATE} CNY`);

    // 5. 从产品信息计算价格（尊重产品的货币字段）
    const productCurrency = product.currency || 'CNY'; // 默认为CNY
    const productPrice = product.discountPrice ? parseFloat(product.discountPrice.toString()) : parseFloat(product.price.toString());

    // 根据产品货币进行正确的价格计算
    let pricePerUnitCNY: number;
    let originalCurrency: string;
    let originalPrice: number;

    if (productCurrency === 'CNY') {
      // 如果产品已经是CNY定价，直接使用
      pricePerUnitCNY = productPrice;
      originalCurrency = 'CNY';
      originalPrice = productPrice;
      this.logger.log(`Product is priced in CNY: ¥${productPrice}`);
    } else if (productCurrency === 'USD') {
      // 如果是USD定价，进行汇率转换
      pricePerUnitCNY = Number((productPrice * USD_TO_CNY_RATE).toFixed(2));
      originalCurrency = 'USD';
      originalPrice = productPrice;
      this.logger.log(`Product is priced in USD: $${productPrice}, converted to CNY: ¥${pricePerUnitCNY}`);
    } else {
      throw new BadRequestException(`不支持的产品货币类型: ${productCurrency}`);
    }

    const totalPriceCNY = Number((pricePerUnitCNY * quantity).toFixed(2));

    // 6. 创建订单元数据
    const orderMetadata = {
      type: 'static_ip_direct_purchase',
      proxyType: product.productProxyCategory || 'STATIC',
      locationId: '0', // 不支持多地区选择
      cityName: product.productConfigDetails?.supplierLocation?.cityName || '',
      countryCode: product.productConfigDetails?.supplierLocation?.countryCode || '',
      duration,
      originalPrice: originalPrice,
      originalCurrency: originalCurrency,
      pricePerUnitCNY: pricePerUnitCNY,
      exchangeRate: USD_TO_CNY_RATE,
      finalCurrency: 'CNY', // 最终支付货币统一为CNY
      productCurrency: productCurrency, // 产品原始货币
    };

    // 7. 创建订单
    const order = this.orderRepository.create({
      orderId: this.generateOrderId(),
      customerId,
      productId: product.id,
      quantity,
      totalAmount: totalPriceCNY,
      paidAmount: totalPriceCNY,
      orderStatus: OrderStatus.PENDING,
      paymentMethod: this.mapPaymentMethodStringToEnum(paymentMethod) || PaymentMethod.ALIPAY, // 默认支付宝，或根据业务逻辑调整
      contactName: customer.nickname || customer.username,
      contactMobile: customer.mobile || '',
      status: '0',
      delFlag: '0',
      createBy: customerId.toString(),
      updateBy: customerId.toString(),
      remark: JSON.stringify(orderMetadata),
    });

    const savedOrder = await this.orderRepository.save(order);
    return savedOrder;
  }

  /**
   * 获取订单的支付URL
   * @param customerId 客户ID
   * @param orderId 订单ID
   * @returns 支付URL
   */
  async getPaymentUrl(customerId: number, orderId: string): Promise<string> {
    const order = await this.findOne(customerId, orderId);

    if (order.orderStatus !== OrderStatus.PENDING) {
      throw new ConflictException('只有待支付的订单才能获取支付链接');
    }

    // 所有订单都支持在线支付

    try {
      // 这里我们假设已经注入了PaymentService
      // 实际实现时需要确保PaymentService已经被正确注入
      // 由于可能存在循环依赖，我们在controller中处理这个逻辑
      return null;
    } catch (error) {
      throw new BadRequestException(`获取支付链接失败: ${error.message}`);
    }
  }

  async findAll(customerId: number, query: OrderQueryDto): Promise<{ orders: OrderPublicDto[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, orderStatus, startDate, endDate } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product') // Join with product
      .leftJoinAndSelect('product.provider', 'provider') // Also join with provider
      .where('order.customerId = :customerId', { customerId })
      .andWhere('order.delFlag = :delFlag', { delFlag: '0' })
      .andWhere('order.productId > :minProductId', { minProductId: 0 }); // 排除充值订单(product_id = 0)

    if (orderStatus) {
      queryBuilder.andWhere('order.orderStatus = :orderStatus', { orderStatus });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('order.createTime BETWEEN :startDate AND :endDate', { startDate, endDate });
    }

    const [orders, total] = await queryBuilder.orderBy('order.createTime', 'DESC').skip(skip).take(limit).getManyAndCount();

    const processedOrders: OrderPublicDto[] = orders.map((order) => {
      const product = order.product;
      const productPublicDto: ProductPublicDto | null = product
        ? {
            id: product.id,
            productCode: product.productCode,
            productName: product.productName,
            productDesc: product.productDesc,
            productType: product.productType,
            price: product.price,
            discountPrice: product.discountPrice,
            currency: product.currency,
            costPrice: product.costPrice,
            costPriceCurrency: product.costPriceCurrency,
            flowAmount: product.flowAmount,
            validityPeriod: product.validityPeriod,
            syncStatus: product.syncStatus,
            lastSyncTime: product.lastSyncTime,
            minQuantity: product.minQuantity,
            maxQuantity: product.maxQuantity,
            inventoryStatus: product.inventoryStatus,
            availableCount: product.availableCount,
            cityId: product.cityId,
            city: product.city,
            region: product.region,
            country: product.country,
            countryCode: product.countryCode,
            syncStrategy: product.syncStrategy,
            isPriceManual: product.isPriceManual,
            imageUrl: product.imageUrl,
            salesCount: product.salesCount,
            sortOrder: product.sortOrder,
            externalProductId: product.externalProductId,
            productProxyCategory: product.productProxyCategory,
            // 仅包含公共的供应商信息
            provider: product.provider
              ? {
                  providerId: product.provider.providerId,
                  providerCode: product.provider.providerCode,
                  providerName: product.provider.providerName,
                  providerDesc: product.provider.providerDesc,
                  providerType: product.provider.providerType,
                }
              : null,
            supplierName: product.provider ? product.provider.providerName : null,
          }
        : null;

      return {
        orderId: order.orderId,
        customerId: order.customerId,
        productId: order.productId,
        quantity: order.quantity,
        totalAmount: order.totalAmount,
        paidAmount: order.paidAmount,
        orderStatus: order.orderStatus,
        paymentMethod: order.paymentMethod,
        paymentTime: order.paymentTime,
        transactionId: order.transactionId,
        paymentUrl: order.paymentUrl,
        address: order.address,
        contactName: order.contactName,
        contactMobile: order.contactMobile,
        orderType: order.orderType,
        renewalInstanceIds: order.renewalInstanceIds,
        fulfillmentStatus: order.fulfillmentStatus,
        fulfillmentError: order.fulfillmentError,
        fulfillmentTime: order.fulfillmentTime,
        fulfillmentRetryCount: order.fulfillmentRetryCount,
        product: productPublicDto,
        productName: product ? product.productName : '未知产品',
        productFlowAmount: product ? product.flowAmount : null,
        statusDescription: this.statusDescriptions[order.orderStatus] || '未知状态',
        paymentMethodName: this.getPaymentMethodName(order.paymentMethod),
        currency: product ? product.currency : 'CNY',
      };
    });

    return {
      orders: processedOrders,
      total,
      page,
      limit,
    };
  }

  // 获取订单详情
  async findOne(customerId: number, orderId: string): Promise<OrderPublicDto> {
    const order = await this.orderRepository.findOne({
      where: {
        orderId,
        customerId,
        delFlag: '0',
      },
      relations: ['product', 'product.provider'], // 加载产品和供应商关联
    });

    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }

    const product = order.product;

    const productPublicDto: ProductPublicDto | null = product
      ? {
          id: product.id,
          productCode: product.productCode,
          productName: product.productName,
          productDesc: product.productDesc,
          productType: product.productType,
          price: product.price,
          discountPrice: product.discountPrice,
          currency: product.currency,
          costPrice: product.costPrice,
          costPriceCurrency: product.costPriceCurrency,
          flowAmount: product.flowAmount,
          validityPeriod: product.validityPeriod,
          syncStatus: product.syncStatus,
          lastSyncTime: product.lastSyncTime,
          minQuantity: product.minQuantity,
          maxQuantity: product.maxQuantity,
          inventoryStatus: product.inventoryStatus,
          availableCount: product.availableCount,
          cityId: product.cityId,
          city: product.city,
          region: product.region,
          country: product.country,
          countryCode: product.countryCode,
          syncStrategy: product.syncStrategy,
          isPriceManual: product.isPriceManual,
          imageUrl: product.imageUrl,
          salesCount: product.salesCount,
          sortOrder: product.sortOrder,
          externalProductId: product.externalProductId,
          productProxyCategory: product.productProxyCategory,
          // 仅包含公共的供应商信息
          provider: product.provider
            ? {
                providerId: product.provider.providerId,
                providerCode: product.provider.providerCode,
                providerName: product.provider.providerName,
                providerDesc: product.provider.providerDesc,
                providerType: product.provider.providerType,
              }
            : null,
          supplierName: product.provider ? product.provider.providerName : null,
        }
      : null;

    // Create the public result object
    const result: OrderPublicDto = {
      orderId: order.orderId,
      customerId: order.customerId,
      productId: order.productId,
      quantity: order.quantity,
      totalAmount: order.totalAmount,
      paidAmount: order.paidAmount,
      orderStatus: order.orderStatus,
      paymentMethod: order.paymentMethod,
      paymentTime: order.paymentTime,
      transactionId: order.transactionId,
      paymentUrl: order.paymentUrl,
      address: order.address,
      contactName: order.contactName,
      contactMobile: order.contactMobile,
      orderType: order.orderType,
      renewalInstanceIds: order.renewalInstanceIds,
      fulfillmentStatus: order.fulfillmentStatus,
      fulfillmentError: order.fulfillmentError,
      fulfillmentTime: order.fulfillmentTime,
      fulfillmentRetryCount: order.fulfillmentRetryCount,
      product: productPublicDto,
      productName: product ? product.productName : '未知产品',
      productFlowAmount: product ? product.flowAmount : null,
      statusDescription: this.statusDescriptions[order.orderStatus] || '未知状态',
      paymentMethodName: this.getPaymentMethodName(order.paymentMethod),
      currency: product ? product.currency : 'CNY',
    };

    return result;
  }

  // 获取订单实体 - 内部使用
  async findOrderEntityByOrderId(orderId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderId,
        delFlag: '0',
      },
      relations: ['product', 'product.provider'], // 加载产品和供应商关联
    });

    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }

    return order;
  }

  async cancel(customerId: number, orderId: string): Promise<Order> {
    // findOne now returns EnrichedOrder, but we only need core Order fields for cancel
    const order = await this.orderRepository.findOneBy({ orderId, customerId, delFlag: '0' });
    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }

    if (order.orderStatus !== OrderStatus.PENDING) {
      // Use Enum
      throw new ConflictException('只有待支付的订单才能取消');
    }

    order.orderStatus = OrderStatus.CANCELLED; // Use Enum
    order.updateBy = customerId.toString();

    return this.orderRepository.save(order);
  }

  async pay(customerId: number, orderId: string, paymentMethod: string, transactionId: string): Promise<Order> {
    this.logger.log(`开始处理订单支付: 客户ID=${customerId}, 订单ID=${orderId}, 支付方式=${paymentMethod}, 交易号=${transactionId}`);

    // 使用事务处理支付更新
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 使用悲观锁查询订单
      const order = await queryRunner.manager.findOne(Order, {
        where: { orderId, customerId, delFlag: '0' },
        lock: { mode: 'pessimistic_write' },
      });

      if (!order) {
        this.logger.error(`订单不存在: ${orderId}`);
        throw new NotFoundException(`订单 ${orderId} 不存在`);
      }

      this.logger.log(`找到订单: ${order.orderId}, 当前状态: ${order.orderStatus}`);

      // 如果订单已经是已支付状态，直接返回订单
      if (order.orderStatus === OrderStatus.PAID) {
        this.logger.log(`订单已经是已支付状态，无需再次更新: ${orderId}`);
        await queryRunner.rollbackTransaction(); // 回滚事务，因为没有实际更新
        return order;
      }

      // 检查订单状态
      if (order.orderStatus !== OrderStatus.PENDING) {
        this.logger.error(`订单状态不允许支付: ${order.orderStatus}`);
        throw new ConflictException('只有待支付的订单才能支付');
      }

      // 更新订单状态
      order.orderStatus = OrderStatus.PAID; // 使用枚举
      order.paymentMethod = this.mapPaymentMethodStringToEnum(paymentMethod); // 使用辅助函数转换
      order.paymentTime = new Date();
      order.transactionId = transactionId;
      order.updateBy = customerId.toString();
      order.updateTime = new Date();

      // 保存订单更新
      const updatedOrder = await queryRunner.manager.save(order);
      this.logger.log(`订单状态更新成功: ${updatedOrder.orderId}, 新状态: ${updatedOrder.orderStatus}`);

      // 提交事务
      await queryRunner.commitTransaction();

      // 发送订单支付完成事件，触发代理实例创建
      this.eventEmitter.emit('order.payment.complete', new OrderPaymentCompleteEvent(updatedOrder, paymentMethod, transactionId));

      return updatedOrder;
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`处理订单支付失败: ${error.message}`);
      throw error;
    } finally {
      // 释放连接
      await queryRunner.release();
    }
  }

  /**
   * 获取支付方式的名称
   */
  private getPaymentMethodName(paymentMethod: string | PaymentMethod): string {
    const paymentMethodMap: Record<PaymentMethod, string> = {
      // Use Record for better typing
      [PaymentMethod.ALIPAY]: '支付宝',
      [PaymentMethod.WECHAT]: '微信',
      [PaymentMethod.BALANCE]: '余额支付',
    };
    return paymentMethodMap[paymentMethod as PaymentMethod] || '未知支付方式';
  }

  /**
   * 将支付方式字符串映射为 PaymentMethod 枚举值
   * @param method 支付方式字符串 (e.g., 'alipay', 'wechat', 'balance', '1', '2', '4')
   * @returns 对应的 PaymentMethod 枚举值，如果无法映射则返回 null
   */
  private mapPaymentMethodStringToEnum(method: string): PaymentMethod | null {
    const lowerCaseMethod = method.toLowerCase();
    switch (lowerCaseMethod) {
      case 'alipay':
      case '1':
        return PaymentMethod.ALIPAY;
      case 'wechat':
      case 'wechat_pay':
      case '2':
        return PaymentMethod.WECHAT;
      case 'balance':
      case '4':
        return PaymentMethod.BALANCE;
      default:
        return null;
    }
  }

  // refund method likely needs similar adjustment as cancel/pay if it relies on findOne result structure
  async refund(customerId: number, orderId: string): Promise<Order> {
    const order = await this.orderRepository.findOneBy({ orderId, customerId, delFlag: '0' });
    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }

    if (order.orderStatus !== OrderStatus.PAID) {
      throw new ConflictException('只有已支付的订单才能退款');
    }

    order.orderStatus = OrderStatus.REFUNDED; // Use Enum
    order.updateBy = customerId.toString();

    return this.orderRepository.save(order);
  }

  /**
   * 发送支付确认通知
   */
  private async sendPaymentConfirmationNotification(order: Order) {
    // Takes Order type
    try {
      const paymentMethodName = this.getPaymentMethodName(order.paymentMethod);
      const formattedPaymentTime = order.paymentTime ? order.paymentTime.toLocaleString() : 'N/A';

      const notificationContent = {
        title: '订单支付已确认',
        content: `尊敬的客户\n\n您的订单 ${order.orderId} 已成功支付并开通服务。\n\n订单金额：${order.paidAmount}\n支付方式：${paymentMethodName}\n支付时间：${formattedPaymentTime}\n\n如有任何疑问，请联系客服。\n\n感谢您的惠顾！`,
        notificationType: 'payment_confirmation',
      };

      await this.notificationsService.createNotification({
        customerId: order.customerId,
        ...notificationContent,
        createBy: 'system',
        updateBy: 'system',
      });
    } catch (error) {
      console.error('发送支付确认通知失败', error);
    }
  }

  async getPaymentStatus(customerId: number, orderId: string): Promise<{ orderStatus: OrderStatus; transactionId?: string; statusDescription: string }> {
    // Use findOne to get enriched data including statusDescription
    const order = await this.findOne(customerId, orderId);

    return {
      orderStatus: order.orderStatus, // Return the status code
      transactionId: order.transactionId,
      statusDescription: order.statusDescription || '未知状态', // Use the description from enriched data
    };
  }

  /**
   * 获取订单开通状态
   */
  async getActivationStatus(customerId: number, orderId: string) {
    const order = await this.findOne(customerId, orderId);
    
    // 查询关联的代理实例
    const instances = await this.proxyInstanceRepository.find({
      where: { orderId },
    });

    const totalInstances = instances.length;
    const completedInstances = instances.filter(i => i.status === ProxyInstanceStatus.ACTIVE).length;
    const failedInstances = instances.filter(i => i.status === ProxyInstanceStatus.PROVISION_FAILED).length;

    return {
      status: order.fulfillmentStatus,
      totalInstances,
      completedInstances,
      failedInstances,
      retryCount: order.fulfillmentRetryCount || 0,
      error: order.fulfillmentError,
      canRetry: order.fulfillmentStatus === 'failed' && order.fulfillmentRetryCount < 3,
    };
  }

  /**
   * 重试订单开通
   */
  async retryActivation(customerId: number, orderId: string) {
    const order = await this.findOne(customerId, orderId);
    
    if (order.fulfillmentStatus !== 'failed') {
      throw new BadRequestException('只有开通失败的订单才能重试');
    }

    if (order.fulfillmentRetryCount >= 3) {
      throw new BadRequestException('重试次数已达上限');
    }

    // 重置开通状态，让后台任务重新处理
    await this.orderRepository.update(
      { orderId },
      {
        fulfillmentStatus: 'pending',
        fulfillmentError: null,
        updateBy: customerId.toString(),
        updateTime: new Date(),
      }
    );

    // 这里可以触发重新开通的任务
    // 例如：await this.jobService.addActivationJob(orderId);

    return { message: '重试请求已提交，请稍后查看开通状态' };
  }

  private generateOrderId(): string {
    // Using simple timestamp + random for example, UUID might be better
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  /**
   * 检查并取消超时未支付的订单
   * 此方法将被定时任务调用
   */
  async cancelTimeoutOrders(): Promise<void> {
    try {
      this.logger.log('开始检查超时未支付订单...');

      // 使用数据库重试机制执行操作
      await this.databaseHealthService.executeWithRetry(
        async () => {
          // 从数据库获取支付超时时间（分钟），默认为30分钟
          const timeoutMinutes = await this.getConfigValue('payment.timeout_minutes', 30);

          // 计算超时时间点 (使用UTC时间避免时区问题)
          const timeoutDate = new Date();
          timeoutDate.setUTCMinutes(timeoutDate.getUTCMinutes() - timeoutMinutes);

          this.logger.log(`当前UTC时间: ${new Date().toISOString()}`);
          this.logger.log(`超时判断时间点: ${timeoutDate.toISOString()}`);
          this.logger.log(`将取消创建时间早于 ${timeoutDate.toISOString()} 的待支付订单`);

          // 查询超时未支付的订单
          const timeoutOrders = await this.orderRepository.find({
            where: {
              orderStatus: OrderStatus.PENDING, // 只查询待支付状态的订单
              createTime: LessThan(timeoutDate), // 创建时间早于超时时间点
              delFlag: '0', // 未删除
            },
          });

          if (timeoutOrders.length === 0) {
            this.logger.log('没有发现超时未支付的订单');
            return;
          }

          this.logger.log(`发现 ${timeoutOrders.length} 个超时未支付订单，准备取消...`);

          // 批量更新订单状态为已取消
          for (const order of timeoutOrders) {
            order.orderStatus = OrderStatus.CANCELLED;
            order.updateBy = 'system';
            order.updateTime = new Date();
            order.remark = (order.remark || '') + ' | 系统自动取消：支付超时';

            // 发送取消通知（使用try-catch避免通知失败影响主流程）
            try {
              await this.sendOrderCancelNotification(order);
            } catch (notificationError) {
              this.logger.warn(`发送订单取消通知失败 (订单: ${order.orderId}): ${notificationError.message}`);
            }
          }

          // 保存更新
          await this.orderRepository.save(timeoutOrders);

          this.logger.log(`成功取消 ${timeoutOrders.length} 个超时未支付订单`);
        },
        3,
        2000,
      ); // 最多重试3次，每次间隔2秒
    } catch (error) {
      this.logger.error(`取消超时订单时发生错误: ${error.message}`, error.stack);

      // 记录数据库健康状态
      const healthStatus = this.databaseHealthService.getHealthStatus();
      this.logger.error(`数据库健康状态: ${JSON.stringify(healthStatus)}`);
    }
  }

  /**
   * 发送订单取消通知
   */
  private async sendOrderCancelNotification(order: Order): Promise<void> {
    try {
      const notificationContent = {
        title: '订单已自动取消',
        content: `尊敬的客户\n\n您的订单 ${order.orderId} 因超时未支付已被系统自动取消。\n\n如需继续购买，请重新下单。\n\n如有任何疑问，请联系客服。`,
        notificationType: 'order_cancelled',
      };

      await this.notificationsService.createNotification({
        customerId: order.customerId,
        ...notificationContent,
        createBy: 'system',
        updateBy: 'system',
      });
    } catch (error) {
      this.logger.error(`发送订单取消通知失败: ${error.message}`);
    }
  }

  /**
   * 创建续费订单
   */
  async createRenewOrder(customerId: number, dto: RenewStaticIpOrderDto): Promise<Order> {
    const { instanceIds } = dto;

    // 将字符串格式的实例ID转换为数字
    const numericInstanceIds = instanceIds.map((id) => parseInt(id, 10));

    // 验证所有IP实例是否存在且属于该用户
    const instances = await this.proxyInstanceRepository.find({
      where: {
        instanceId: In(numericInstanceIds),
        customerId,
        status: ProxyInstanceStatus.ACTIVE, // 正常状态
      },
      relations: ['product'],
    });

    if (instances.length !== instanceIds.length) {
      throw new NotFoundException('部分IP实例不存在或不属于当前用户');
    }

    // 检查是否所有实例都来自相同的产品（因为续费价格可能不同）
    const productIds = [...new Set(instances.map((i) => i.productId))];
    if (productIds.length > 1) {
      throw new BadRequestException('续费的IP必须属于相同的产品套餐');
    }

    // 获取产品信息
    const product = instances[0].product;
    if (!product) {
      throw new NotFoundException('无法找到对应的产品信息');
    }

    // 计算续费总金额
    const quantity = instances.length;
    const effectivePrice = product.discountPrice && product.discountPrice > 0 ? product.discountPrice : product.price;

    const totalAmount = product.price * quantity;
    const paidAmount = effectivePrice * quantity;

    // 创建续费订单
    const orderData = {
      orderId: this.generateOrderId(),
      customerId,
      productId: product.id,
      quantity,
      totalAmount,
      paidAmount,
      orderStatus: OrderStatus.PENDING,
      paymentMethod: PaymentMethod.ALIPAY, // 默认支付方式，用户可以在支付时选择
      status: '0',
      delFlag: '0',
      createBy: customerId.toString(),
      updateBy: customerId.toString(),
      remark: `续费订单 - IP实例：${instanceIds.join(', ')}`,
      // 存储要续费的实例ID，以便支付成功后更新有效期
      orderType: OrderType.RENEWAL,
      renewalInstanceIds: instanceIds,
    };

    const order = this.orderRepository.create(orderData);
    const savedOrder = await this.orderRepository.save(order);

    // 创建通知
    await this.notificationsService.createNotification({
      customerId,
      title: '续费订单创建成功',
      content: `您的续费订单 ${Array.isArray(savedOrder) ? savedOrder[0].orderId : savedOrder.orderId} 已创建成功，续费IP数量：${quantity}个，金额：¥${paidAmount}。请尽快完成支付。`,
      notificationType: 'order_created',
      createBy: 'system',
      updateBy: 'system',
    });

    return Array.isArray(savedOrder) ? savedOrder[0] : savedOrder;
  }

  /**
   * 创建充值订单
   */
  async createRechargeOrder(
    customerId: number,
    createRechargeOrderDto: CreateRechargeOrderDto,
  ): Promise<{
    orderId: string;
    transactionId: string;
    paymentUrl: string;
    expiresAt: string;
    amount: number;
  }> {
    const { amount, paymentMethod, remark } = createRechargeOrderDto;

    // 验证充值金额
    if (amount < 0.01 || amount > 50000) {
      throw new BadRequestException('充值金额必须在0.01-50000元之间');
    }

    // 验证客户是否存在
    const customer = await this.customerRepository.findOne({
      where: { customerId, delFlag: '0' },
    });

    if (!customer) {
      throw new NotFoundException('客户不存在');
    }

    // 生成订单ID
    const orderId = this.generateOrderId();

    // 使用数据库事务确保订单和交易记录同时创建
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建充值订单
      const order = this.orderRepository.create({
        orderId,
        customerId,
        productId: null, // 充值订单没有关联产品
        quantity: 1,
        totalAmount: amount,
        paidAmount: amount,
        orderStatus: OrderStatus.PENDING,
        paymentMethod: paymentMethod as PaymentMethod,
        orderType: OrderType.RECHARGE,
        address: null,
        contactName: null,
        contactMobile: null,
        status: '0',
        delFlag: '0',
        createBy: customerId.toString(),
        updateBy: customerId.toString(),
        remark: remark || `用户充值 ¥${amount}`,
      });

      const savedOrder = await queryRunner.manager.save(order);

      // 创建对应的交易记录
      const transaction = this.transactionRepository.create({
        transactionId: orderId, // 使用订单ID作为交易ID
        operationId: `RECHARGE_${orderId}`,
        customerId,
        accountType: AccountType.WALLET_RECHARGE,
        accountId: null,
        transactionType: TransactionType.RECHARGE,
        amount,
        direction: TransactionDirection.CREDIT,
        rechargeComponentAmount: amount, // 全部作为充值金额
        bonusComponentAmount: 0, // 暂时不支持赠送
        walletId: null,
        paymentMethod: paymentMethod?.toString(),
        transactionStatus: TransactionStatus.PENDING_PAYMENT,
        externalReference: null, // 支付成功后会更新为支付平台的交易号
        relatedOrder: orderId,
        operatorUserId: null,
        balanceBefore: null, // 支付成功后会更新
        balanceAfter: null, // 支付成功后会更新
        status: '0',
        delFlag: '0',
        createBy: customerId.toString(),
        updateBy: customerId.toString(),
        remark: remark || `用户充值 ¥${amount}`,
        description: `客户 ${customerId} 充值 ¥${amount}`,
        paymentIntentId: null,
        paymentUrl: null, // 稍后更新
        paymentExpiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
      });

      await queryRunner.manager.save(transaction);

      // 提交事务
      await queryRunner.commitTransaction();

      // 使用真实的支付服务生成支付URL
      const paymentUrl = await this.paymentService.generatePaymentUrl(savedOrder);
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30分钟后过期

      // 更新订单的支付URL
      await this.orderRepository.update(orderId, { paymentUrl });
      // 同时更新交易记录的支付URL
      await this.transactionRepository.update(orderId, { paymentUrl });

      // 创建通知
      await this.notificationsService.createNotification({
        customerId,
        title: '充值订单创建成功',
        content: `您的充值订单 ${orderId} 已创建成功，充值金额：¥${amount}。请尽快完成支付。`,
        notificationType: 'order_created',
        createBy: 'system',
        updateBy: 'system',
      });

      this.logger.log(`充值订单和交易记录创建成功: ${orderId}, 客户: ${customerId}, 金额: ¥${amount}`);

      return {
        orderId,
        transactionId: orderId, // 对于充值订单，使用订单ID作为交易ID
        paymentUrl,
        expiresAt,
        amount,
      };
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`创建充值订单失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 获取可续费的IP实例
   */
  async getRenewableInstances(customerId: number): Promise<ProxyInstance[]> {
    // 获取用户所有的静态IP实例
    const instances = await this.proxyInstanceRepository.find({
      where: {
        customerId,
        status: ProxyInstanceStatus.ACTIVE, // 正常状态
      },
      relations: ['product'],
      order: {
        expiresAt: 'ASC', // 按过期时间升序，即将过期的在前
      },
    });

    // 过滤出静态代理产品的实例
    return instances.filter((instance) => {
      const product = instance.product;
      return product && product.productProxyCategory === 'STATIC';
    });
  }

  /**
   * 从数据库获取配置值
   * @param key 配置键
   * @param defaultValue 默认值
   * @returns 配置值
   */
  private async getConfigValue(key: string, defaultValue: any): Promise<any> {
    try {
      const value = await this.systemConfigService.getConfigValue(key);
      if (value === null || value === undefined) {
        return defaultValue;
      }
      // 处理数字类型的配置值
      if (typeof defaultValue === 'number') {
        return Number(value);
      }
      return value;
    } catch (error) {
      this.logger.warn(`获取配置 ${key} 失败，使用默认值: ${defaultValue}`);
      return defaultValue;
    }
  }
}
