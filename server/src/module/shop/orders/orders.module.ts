import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { MailModule } from 'src/module/common/mail/mail.module';
import { RedisModule } from 'src/module/common/redis/redis.module';
import { PaymentService } from 'src/module/payment/payment.service';
import { Customer } from '../auth/entities/customer.entity';
import { NotificationsModule } from '../notifications/notifications.module';
import { ProductsModule } from '../products/products.module';
import { Product } from '../products/entities/product.entity';
import { SettingsModule } from '../settings/settings.module';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Order } from './entities/order.entity';
import { Provider } from '../../system/provider/entities/provider.entity';
import { ProxyInstance } from '../proxy/entities/proxy-instance.entity';
import { OrdersEventListener } from './events/orders-event.listener';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { ProxyModule } from '../proxy/proxy.module';
import { InventoryModule } from '../inventory/inventory.module';
import { WalletModule } from '../wallet/wallet.module';
import { SysConfigModule } from '../../system/config/config.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, Transaction, Customer, Product, Provider, ProxyInstance]),
    HttpModule,
    ProductsModule,
    NotificationsModule,
    SettingsModule,
    RedisModule,
    MailModule,
    ProxyModule,
    InventoryModule,
    forwardRef(() => WalletModule),
    SysConfigModule,
  ],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    OrdersEventListener,
    {
      provide: 'PaymentService',
      useClass: PaymentService,
    },
  ],
  exports: [OrdersService],
})
export class OrdersModule {}
