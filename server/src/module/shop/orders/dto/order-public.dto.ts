import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus, OrderType, PaymentMethod } from '../entities/order.entity';

// 定义公共的Provider DTO，只包含非敏感信息
export class ProviderPublicDto {
  @ApiProperty({ description: '供应商ID' })
  providerId: number;

  @ApiProperty({ description: '供应商代码' })
  providerCode: string;

  @ApiProperty({ description: '供应商名称' })
  providerName: string;

  @ApiProperty({ description: '供应商描述', nullable: true })
  providerDesc: string | null;

  @ApiProperty({ description: '供应商类型' })
  providerType: string;
}

// 定义公共的Product DTO，不包含敏感的供应商配置信息
export class ProductPublicDto {
  @ApiProperty({ description: '产品ID (主键ID)' })
  id: number;

  @ApiProperty({ description: '产品编号/SKU', nullable: true })
  productCode: string | null;

  @ApiProperty({ description: '产品名称' })
  productName: string;

  @ApiProperty({ description: '产品描述', nullable: true })
  productDesc: string | null;

  @ApiProperty({ description: '产品类型' })
  productType: string;

  @ApiProperty({ description: '产品价格' })
  price: number;

  @ApiProperty({ description: '折扣价格', nullable: true })
  discountPrice: number | null;

  @ApiProperty({ description: '销售价格货币' })
  currency: string;

  @ApiProperty({ description: '成本价（人民币）', nullable: true })
  costPrice: number | null;

  @ApiProperty({ description: '成本价币种' })
  costPriceCurrency: string;

  @ApiProperty({ description: '流量大小(MB)' })
  flowAmount: number;

  @ApiProperty({ description: '有效期(天)' })
  validityPeriod: number;

  @ApiProperty({ description: '同步状态' })
  syncStatus: string;

  @ApiProperty({ description: '最后同步时间', nullable: true })
  lastSyncTime: Date | null;

  @ApiProperty({ description: '最小购买数量' })
  minQuantity: number;

  @ApiProperty({ description: '最大购买数量', nullable: true })
  maxQuantity: number | null;

  @ApiProperty({ description: '库存状态' })
  inventoryStatus: string;

  @ApiProperty({ description: '可用数量', nullable: true })
  availableCount: number | null;

  @ApiProperty({ description: '标准化城市ID', nullable: true })
  cityId: number | null;

  @ApiProperty({ description: '地区名称', nullable: true })
  region: string | null;

  @ApiProperty({ description: '国家名称', nullable: true })
  country: string | null;

  @ApiProperty({ description: '城市名称', nullable: true })
  city: string | null;

  @ApiProperty({ description: '国家代码', nullable: true })
  countryCode: string | null;

  @ApiProperty({ description: '同步策略' })
  syncStrategy: string;

  @ApiProperty({ description: '是否手动价格' })
  isPriceManual: boolean;

  @ApiProperty({ description: '产品图片URL', nullable: true })
  imageUrl: string | null;

  @ApiProperty({ description: '销售数量' })
  salesCount: number;

  @ApiProperty({ description: '排序' })
  sortOrder: number;

  @ApiProperty({ description: '供应商处的产品标识/SKU', nullable: true })
  externalProductId: string | null;

  @ApiProperty({ description: '产品代理分类' })
  productProxyCategory: string;

  @ApiProperty({ description: '供应商信息', type: () => ProviderPublicDto, nullable: true })
  provider: ProviderPublicDto | null;

  @ApiProperty({ description: '供应商名称', nullable: true })
  supplierName: string | null;
}

// 定义公共的Order DTO
export class OrderPublicDto {
  @ApiProperty({ description: '订单ID' })
  orderId: string;

  @ApiProperty({ description: '客户ID' })
  customerId: number;

  @ApiProperty({ description: '产品ID (充值订单时为NULL)', nullable: true })
  productId: number | null;

  @ApiProperty({ description: '购买数量' })
  quantity: number;

  @ApiProperty({ description: '订单总金额' })
  totalAmount: number;

  @ApiProperty({ description: '实付金额' })
  paidAmount: number;

  @ApiProperty({ description: '订单状态 (0:待支付, 1:已支付, 2:已取消, 3:已退款)', enum: OrderStatus })
  orderStatus: OrderStatus;

  @ApiProperty({ description: '支付方式 (1:支付宝, 2:微信, 4:余额支付)', enum: PaymentMethod, nullable: true })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付时间', nullable: true })
  paymentTime: Date;

  @ApiProperty({ description: '支付交易号', nullable: true })
  transactionId: string;

  @ApiProperty({ description: '支付URL/二维码链接', nullable: true })
  paymentUrl: string;

  @ApiProperty({ description: '收货地址', nullable: true })
  address: string;

  @ApiProperty({ description: '联系人', nullable: true })
  contactName: string;

  @ApiProperty({ description: '联系电话', nullable: true })
  contactMobile: string;

  @ApiProperty({ description: '订单类型: new-新购, renewal-续费, RECHARGE-充值', enum: OrderType })
  orderType: OrderType;

  @ApiProperty({ description: '续费的IP实例ID列表', type: [String], nullable: true })
  renewalInstanceIds: string[];

  @ApiProperty({ description: '产品开通状态', default: 'pending' })
  fulfillmentStatus: string;

  @ApiProperty({ description: '产品开通失败原因', nullable: true })
  fulfillmentError: string;

  @ApiProperty({ description: '产品开通完成时间', nullable: true })
  fulfillmentTime: Date;

  @ApiProperty({ description: '产品开通重试次数', default: 0 })
  fulfillmentRetryCount: number;

  @ApiProperty({ description: '产品信息', type: () => ProductPublicDto, nullable: true })
  product: ProductPublicDto | null;

  @ApiProperty({ description: '产品名称', nullable: true })
  productName: string;

  @ApiProperty({ description: '产品流量大小', nullable: true })
  productFlowAmount: number | null;

  @ApiProperty({ description: '订单状态描述' })
  statusDescription: string;

  @ApiProperty({ description: '支付方式名称' })
  paymentMethodName: string;

  @ApiProperty({ description: '订单货币' })
  currency: string;
}
