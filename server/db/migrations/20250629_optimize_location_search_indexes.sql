-- 地理位置搜索性能优化索引
-- 创建时间: 2025-06-29
-- 目的: 优化地理位置搜索查询性能

-- 城市搜索索引优化
-- 复合索引：状态 + 国家ID + 城市名称 (用于按国家筛选城市)
CREATE INDEX IF NOT EXISTS idx_city_search_compound 
ON sys_location_city (status, country_id, city_name);

-- 城市名称全文搜索索引 (用于模糊搜索)
CREATE INDEX IF NOT EXISTS idx_city_name_search 
ON sys_location_city (city_name, city_name_en);

-- 热门城市排序索引 (用于热门城市优先显示)
CREATE INDEX IF NOT EXISTS idx_city_hot_sort 
ON sys_location_city (is_hot DESC, sort_order ASC, id ASC);

-- 城市按区域搜索索引 (通过country表关联region)
-- 这个索引支持跨表查询优化
CREATE INDEX IF NOT EXISTS idx_city_country_region 
ON sys_location_city (country_id, status);

-- 国家搜索索引优化
-- 复合索引：状态 + 区域ID + 国家名称 (用于按区域筛选国家)
CREATE INDEX IF NOT EXISTS idx_country_search_compound 
ON sys_location_country (status, region_id, country_name);

-- 国家名称和代码搜索索引 (用于模糊搜索)
CREATE INDEX IF NOT EXISTS idx_country_name_search 
ON sys_location_country (country_name, country_name_en, country_code);

-- 国家排序索引
CREATE INDEX IF NOT EXISTS idx_country_sort 
ON sys_location_country (sort_order ASC, id ASC);

-- 区域搜索索引优化
-- 区域名称搜索索引
CREATE INDEX IF NOT EXISTS idx_region_name_search 
ON sys_location_region (region_name);

-- 区域状态和排序索引
CREATE INDEX IF NOT EXISTS idx_region_status_sort 
ON sys_location_region (status, sort_order ASC, id ASC);

-- 产品表的位置筛选索引优化 (用于产品管理页面筛选)
-- 标准化城市ID索引 (主要筛选字段)
CREATE INDEX IF NOT EXISTS idx_product_location_city 
ON shop_product (location_city_id, status);

-- 传统地理位置字段的复合索引 (向下兼容)
CREATE INDEX IF NOT EXISTS idx_product_location_legacy 
ON shop_product (region, country, city, status);

-- 供应商位置映射索引优化
-- 供应商和标准城市映射索引
CREATE INDEX IF NOT EXISTS idx_mapping_provider_city 
ON supplier_location_mapping (provider_id, standard_city_id, mapping_status);

-- 供应商原始城市ID索引
CREATE INDEX IF NOT EXISTS idx_mapping_native_city 
ON supplier_location_mapping (supplier_native_city_id, provider_id);

-- 分析索引使用情况的查询 (仅用于性能监控，不执行)
/*
-- 查看索引使用统计
SELECT 
    table_name,
    index_name,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type,
    comment
FROM information_schema.statistics 
WHERE table_schema = 'proxy_shop' 
  AND table_name IN ('sys_location_city', 'sys_location_country', 'sys_location_region', 'shop_product')
ORDER BY table_name, index_name;

-- 检查查询执行计划 (示例)
EXPLAIN SELECT * FROM sys_location_city 
WHERE status = '0' AND country_id = 1 AND city_name LIKE '%北京%';
*/

-- 说明：
-- 1. 这些索引专门为搜索功能优化，提升查询性能
-- 2. 复合索引的字段顺序很重要，按照查询频率和选择性排序
-- 3. 如果数据量很大，可以考虑分区表或者使用全文搜索引擎
-- 4. 定期监控索引使用情况，删除不必要的索引
-- 5. 建议在业务低峰期执行索引创建操作