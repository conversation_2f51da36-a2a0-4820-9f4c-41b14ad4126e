-- ==========================================
-- 产品地理位置系统优化 - 最终清理Legacy字段
-- 创建日期: 2025-06-29
-- 描述: 基于完成的代码重构，安全移除冗余的地理位置字段
-- 前置条件: 代码已重构完成，使用JOIN查询，保持DTO兼容性
-- 风险级别: LOW - 代码已适配，仅清理数据库schema
-- ==========================================

-- 第一步：验证系统状态和数据一致性
-- 验证所有产品都有标准化位置数据
SELECT 
  COUNT(*) as total_products,
  COUNT(location_city_id) as products_with_standard_location,
  COUNT(CASE WHEN location_city_id IS NULL THEN 1 END) as products_without_standard_location,
  ROUND(COUNT(location_city_id) * 100.0 / COUNT(*), 2) as coverage_percentage
FROM `shop_product`
WHERE del_flag = '0';

-- 验证legacy字段与标准化字段的一致性
SELECT 
  COUNT(*) as total_records,
  COUNT(CASE WHEN 
    region IS NOT NULL OR 
    country IS NOT NULL OR 
    city IS NOT NULL OR 
    country_code IS NOT NULL 
  THEN 1 END) as records_with_legacy_data,
  COUNT(CASE WHEN location_city_id IS NOT NULL THEN 1 END) as records_with_standard_data
FROM `shop_product`
WHERE del_flag = '0';

-- 第二步：创建完整备份（安全措施）
CREATE TABLE `shop_product_legacy_backup_20250629` AS
SELECT 
  id,
  product_code,
  product_name,
  location_city_id,
  region,
  country,
  city,
  country_code,
  create_time,
  update_time
FROM `shop_product`
WHERE del_flag = '0';

-- 验证备份完整性
SELECT COUNT(*) as backup_count FROM `shop_product_legacy_backup_20250629`;
SELECT COUNT(*) as original_count FROM `shop_product` WHERE del_flag = '0';

-- 第三步：更新实体注释，确保字段描述准确
ALTER TABLE `shop_product` 
MODIFY COLUMN `location_city_id` int NULL DEFAULT NULL COMMENT '标准化城市ID，关联 sys_location_city.id - 主要地理位置引用';

-- 第四步：安全移除legacy字段
-- 移除单个字段的索引（如果存在）
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_region`;
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_country`;
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_city`;
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_country_code`;
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_region_country_city`;

-- 移除legacy字段
ALTER TABLE `shop_product`
DROP COLUMN `region`,
DROP COLUMN `country`,
DROP COLUMN `city`,
DROP COLUMN `country_code`;

-- 第五步：优化标准化字段的索引
-- 确保location_city_id有合适的索引
ALTER TABLE `shop_product` DROP INDEX IF EXISTS `idx_location_city_id`;
ALTER TABLE `shop_product` 
ADD INDEX `idx_location_city_id` (`location_city_id`),
ADD INDEX `idx_product_location_status` (`location_city_id`, `status`, `del_flag`),
ADD INDEX `idx_product_type_location` (`product_type`, `location_city_id`);

-- 第六步：确保外键约束正确设置
-- 移除可能存在的旧约束
ALTER TABLE `shop_product` DROP FOREIGN KEY IF EXISTS `fk_shop_product_location_city`;
ALTER TABLE `shop_product` DROP FOREIGN KEY IF EXISTS `fk_location_city_id`;

-- 添加标准化的外键约束
ALTER TABLE `shop_product` 
ADD CONSTRAINT `fk_shop_product_location_city` 
FOREIGN KEY (`location_city_id`) REFERENCES `sys_location_city` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 第七步：创建地理位置查询优化视图
CREATE OR REPLACE VIEW `v_product_location_summary` AS
SELECT 
  p.id,
  p.product_code,
  p.product_name,
  p.product_type,
  p.price,
  p.status,
  lc.id as location_city_id,
  lc.city_name,
  lc.country_name,
  lc.country_code,
  lr.region_name,
  p.available_count,
  p.sync_status
FROM `shop_product` p
LEFT JOIN `sys_location_city` lc ON p.location_city_id = lc.id
LEFT JOIN `sys_location_country` lcountry ON lc.country_id = lcountry.id
LEFT JOIN `sys_location_region` lr ON lcountry.region_id = lr.id
WHERE p.del_flag = '0'
ORDER BY lr.region_name, lc.country_name, lc.city_name;

-- 第八步：更新统计信息
ANALYZE TABLE `shop_product`;
ANALYZE TABLE `sys_location_city`;
ANALYZE TABLE `sys_location_country`;
ANALYZE TABLE `sys_location_region`;

-- 第九步：验证清理结果
-- 验证表结构
SHOW CREATE TABLE `shop_product`;

-- 验证数据完整性
SELECT 
  COUNT(*) as total_products,
  COUNT(location_city_id) as products_with_location,
  COUNT(CASE WHEN location_city_id IS NULL THEN 1 END) as products_without_location,
  ROUND(COUNT(location_city_id) * 100.0 / COUNT(*), 2) as location_coverage_percentage
FROM `shop_product`
WHERE del_flag = '0';

-- 验证JOIN查询性能
EXPLAIN SELECT 
  p.product_name,
  lc.city_name,
  lc.country_name,
  lr.region_name
FROM `shop_product` p
LEFT JOIN `sys_location_city` lc ON p.location_city_id = lc.id
LEFT JOIN `sys_location_country` lcountry ON lc.country_id = lcountry.id
LEFT JOIN `sys_location_region` lr ON lcountry.region_id = lr.id
WHERE p.del_flag = '0' AND p.status = '0'
LIMIT 10;

-- 验证地理位置分布
SELECT 
  lr.region_name,
  COUNT(*) as product_count
FROM `shop_product` p
LEFT JOIN `sys_location_city` lc ON p.location_city_id = lc.id
LEFT JOIN `sys_location_country` lcountry ON lc.country_id = lcountry.id
LEFT JOIN `sys_location_region` lr ON lcountry.region_id = lr.id
WHERE p.del_flag = '0'
GROUP BY lr.region_name
ORDER BY product_count DESC;

-- ==========================================
-- 回滚脚本（紧急情况使用）
-- ==========================================
/*
-- 警告：回滚会恢复legacy字段，需要重新执行代码重构

-- 步骤 1：恢复legacy字段结构
ALTER TABLE `shop_product`
ADD COLUMN `region` varchar(100) NULL DEFAULT NULL COMMENT '地区名称',
ADD COLUMN `country` varchar(100) NULL DEFAULT NULL COMMENT '国家名称',
ADD COLUMN `city` varchar(100) NULL DEFAULT NULL COMMENT '城市名称',
ADD COLUMN `country_code` varchar(10) NULL DEFAULT NULL COMMENT '国家代码';

-- 步骤 2：从备份恢复数据
UPDATE `shop_product` p
INNER JOIN `shop_product_legacy_backup_20250629` b ON p.id = b.id
SET 
  p.region = b.region,
  p.country = b.country,
  p.city = b.city,
  p.country_code = b.country_code;

-- 步骤 3：恢复legacy字段索引
ALTER TABLE `shop_product` 
ADD INDEX `idx_region_country_city` (`region`, `country`, `city`);

-- 步骤 4：移除外键约束
ALTER TABLE `shop_product` DROP FOREIGN KEY `fk_shop_product_location_city`;

-- 步骤 5：删除优化视图
DROP VIEW IF EXISTS `v_product_location_summary`;

-- 步骤 6：删除备份表（可选）
-- DROP TABLE `shop_product_legacy_backup_20250629`;
*/

-- ==========================================
-- 执行总结
-- ==========================================
-- 本迁移完成以下工作：
-- 1. ✅ 验证系统状态和数据一致性
-- 2. ✅ 创建完整的数据备份
-- 3. ✅ 移除legacy字段 (region, country, city, country_code)
-- 4. ✅ 优化location_city_id相关索引
-- 5. ✅ 设置正确的外键约束
-- 6. ✅ 创建查询优化视图
-- 7. ✅ 更新统计信息和验证结果
-- 8. ✅ 提供完整的回滚方案
--
-- 前置条件已满足：
-- - 代码重构完成，使用JOIN查询
-- - DTO保持向后兼容性
-- - 同步服务不再写入legacy字段
-- 
-- 风险评估：LOW - 所有代码已适配标准化字段
-- ==========================================