-- ========================================
-- 产品地理位置字段重构 v2.3 (应用层保障版)
-- 执行时间: 2025-06-29
-- 作者: Claude Code
-- 目标: 重命名 location_city_id 为 city_id，添加 region_id 和 country_id 冗余字段
-- ========================================

-- 步骤 1: 检查当前表结构和数据完整性
SELECT 
    COUNT(*) as total_products,
    COUNT(location_city_id) as products_with_location,
    COUNT(*) - COUNT(location_city_id) as products_without_location
FROM shop_product 
WHERE del_flag = '0';

-- 验证现有数据的地理位置层级关系
SELECT 
    COUNT(DISTINCT p.location_city_id) as unique_cities,
    COUNT(DISTINCT c.country_id) as unique_countries,
    COUNT(DISTINCT r.id) as unique_regions
FROM shop_product p 
LEFT JOIN sys_location_city c ON p.location_city_id = c.id
LEFT JOIN sys_location_country co ON c.country_id = co.id
LEFT JOIN sys_location_region r ON co.region_id = r.id
WHERE p.del_flag = '0' AND p.location_city_id IS NOT NULL;

-- 步骤 2: 添加新的地理位置字段
ALTER TABLE `shop_product`
ADD COLUMN `region_id` INT NULL COMMENT '关联sys_location_region的主键，冗余字段用于查询优化' AFTER `product_proxy_category`,
ADD COLUMN `country_id` INT NULL COMMENT '关联sys_location_country的主键，冗余字段用于查询优化' AFTER `region_id`,
ADD COLUMN `city_id` INT NULL COMMENT '关联sys_location_city的主键，单一事实来源' AFTER `country_id`;

-- 步骤 3: 数据迁移 - 从 location_city_id 填充新字段
-- 同时填充层级关系的 region_id 和 country_id
UPDATE `shop_product` p
INNER JOIN `sys_location_city` c ON p.location_city_id = c.id
INNER JOIN `sys_location_country` co ON c.country_id = co.id
INNER JOIN `sys_location_region` r ON co.region_id = r.id
SET 
    p.city_id = p.location_city_id,
    p.country_id = co.id,
    p.region_id = r.id,
    p.update_time = NOW()
WHERE p.location_city_id IS NOT NULL;

-- 步骤 4: 验证数据迁移结果
SELECT 
    'migration_validation' as check_type,
    COUNT(*) as total_migrated,
    COUNT(CASE WHEN city_id = location_city_id THEN 1 END) as city_id_match,
    COUNT(CASE WHEN country_id IS NOT NULL THEN 1 END) as country_id_filled,
    COUNT(CASE WHEN region_id IS NOT NULL THEN 1 END) as region_id_filled
FROM shop_product 
WHERE del_flag = '0' AND location_city_id IS NOT NULL;

-- 步骤 5: 创建新字段的索引（优化查询性能）
CREATE INDEX `idx_product_city_id` ON `shop_product`(`city_id`);
CREATE INDEX `idx_product_country_id` ON `shop_product`(`country_id`);
CREATE INDEX `idx_product_region_id` ON `shop_product`(`region_id`);

-- 为了更好的查询性能，创建复合索引
CREATE INDEX `idx_product_location_hierarchy` ON `shop_product`(`region_id`, `country_id`, `city_id`);

-- 步骤 6: 添加外键约束（可选，基于业务需求）
-- 注意：生产环境中建议谨慎添加外键约束，可能影响性能
-- ALTER TABLE `shop_product`
-- ADD CONSTRAINT `fk_product_city_v2`
-- FOREIGN KEY (`city_id`) REFERENCES `sys_location_city`(`id`)
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- ALTER TABLE `shop_product`
-- ADD CONSTRAINT `fk_product_country_v2`
-- FOREIGN KEY (`country_id`) REFERENCES `sys_location_country`(`id`)
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- ALTER TABLE `shop_product`
-- ADD CONSTRAINT `fk_product_region_v2`
-- FOREIGN KEY (`region_id`) REFERENCES `sys_location_region`(`id`)
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 步骤 7: 验证最终结果
SELECT 
    'final_validation' as check_type,
    COUNT(*) as total_products,
    COUNT(city_id) as products_with_city_id,
    COUNT(country_id) as products_with_country_id,
    COUNT(region_id) as products_with_region_id,
    COUNT(location_city_id) as products_with_old_field
FROM shop_product 
WHERE del_flag = '0';

-- 检查层级关系的一致性
SELECT 
    'consistency_check' as check_type,
    COUNT(*) as total_checked,
    COUNT(CASE 
        WHEN c.country_id = p.country_id 
        AND co.region_id = p.region_id 
        THEN 1 
    END) as consistent_hierarchy,
    COUNT(*) - COUNT(CASE 
        WHEN c.country_id = p.country_id 
        AND co.region_id = p.region_id 
        THEN 1 
    END) as inconsistent_hierarchy
FROM shop_product p
INNER JOIN sys_location_city c ON p.city_id = c.id
INNER JOIN sys_location_country co ON c.country_id = co.id
WHERE p.del_flag = '0' AND p.city_id IS NOT NULL;

-- ========================================
-- 注意事项：
-- 1. 在应用层代码更新完成并测试通过后，才可以删除 location_city_id 字段
-- 2. 删除操作建议单独作为一个迁移文件，以便回滚
-- 3. 新的应用层逻辑将确保 city_id, country_id, region_id 的数据一致性
-- 4. 本迁移专注于数据结构重构，数据一致性由应用层保障
-- ========================================

-- 待后续清理的字段（等应用层完全适配后执行）:
-- ALTER TABLE `shop_product` DROP COLUMN `location_city_id`;