# 邮件发送超时问题优化方案

## 问题描述

`/api/shop/auth/send-login-code` 接口出现间歇性超时问题：
- **成功情况**：总耗时约3秒，SMTP发送耗时约3秒
- **失败情况**：前端60秒超时，实际可能超过60秒
- **表现**：有时成功，有时超时，说明是网络波动导致的间歇性问题

## 根本原因分析

1. **SMTP服务器响应不稳定**：Gmail SMTP 服务器在某些时候响应较慢
2. **超时配置不合理**：原始配置超时时间过长，导致用户体验差
3. **缺乏熔断机制**：连续失败时没有保护机制
4. **前端超时不匹配**：前端60秒超时与后端超时不匹配

## 优化方案

### 1. 后端SMTP配置优化

**文件**: `server/src/module/common/mail/mail.service.ts`

```typescript
// 超时配置优化 - 快速失败策略
connectionTimeout: 8000,  // 8秒连接超时（原60秒）
greetingTimeout: 5000,    // 5秒问候超时（原30秒）  
socketTimeout: 12000,     // 12秒套接字超时（原60秒）
dnsTimeout: 5000,         // 5秒DNS超时（原30秒）

// 连接池优化
maxConnections: Math.max(maxConnections, 3), // 至少保持3个连接
rateLimit: false, // 禁用速率限制以提高速度
```

### 2. 熔断器机制

添加邮件发送熔断器，防止连续失败：

```typescript
// 熔断器配置
private circuitBreakerThreshold: number = 5; // 连续失败5次后开启熔断
private readonly circuitBreakerResetTime: number = 300000; // 5分钟后重置
```

**功能**：
- 连续失败5次后开启熔断器
- 熔断期间拒绝发送邮件
- 5分钟后自动重置
- 发送成功时立即重置失败计数

### 3. 前端超时优化

**文件**: `shop-web/src/services/api-client.ts`

```typescript
// 专用邮件客户端，30秒超时
export const mailApiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时（专门用于邮件发送）
  headers: {
    "Content-Type": "application/json",
  },
});
```

**文件**: `shop-web/src/services/mail-api.ts`
- 创建专用邮件API包装函数
- 使用 `mailApiClient` 处理邮件相关请求
- 专门的超时错误处理

### 4. 健康检查增强

更新邮件服务健康检查，包含熔断器状态：

```typescript
async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; message: string; details?: any }> {
  // 检查熔断器状态
  // 检查SMTP连接
  // 返回详细的健康状态和熔断器信息
}
```

## 配置时间线

| 组件 | 原配置 | 新配置 | 说明 |
|------|--------|--------|------|
| 前端超时 | 60秒 | 30秒 | 用户体验优化 |
| SMTP连接超时 | 60秒 | 8秒 | 快速失败 |
| SMTP问候超时 | 30秒 | 5秒 | 快速失败 |
| SMTP套接字超时 | 60秒 | 12秒 | 快速失败 |
| DNS超时 | 30秒 | 5秒 | 快速失败 |

**总体策略**：在30秒内完成邮件发送，或快速失败给用户反馈

## 使用说明

### 前端调用

```typescript
// 使用专用邮件API
import { postShopAuthSendLoginCode } from '@/services/mail-api';

// 而不是使用默认的 generated/api.ts 中的函数
```

### 健康检查

```bash
# 检查邮件服务状态
curl -X GET "http://localhost:32080/api/mail-test/health"
```

### 熔断器状态监控

健康检查响应会包含熔断器状态：
```json
{
  "status": "healthy",
  "details": {
    "circuitBreaker": {
      "isOpen": false,
      "failureCount": 0,
      "threshold": 5
    }
  }
}
```

## 预期效果

1. **用户体验提升**：30秒内给出明确反馈，不再长时间等待
2. **系统稳定性**：熔断器防止雪崩效应
3. **问题快速定位**：详细的日志和健康检查
4. **自动恢复**：熔断器自动重置，服务自愈

## 监控建议

1. **监控熔断器状态**：定期检查熔断器是否频繁触发
2. **SMTP响应时间**：监控邮件发送的平均响应时间
3. **失败率监控**：跟踪邮件发送成功率
4. **用户反馈**：收集用户关于邮件发送体验的反馈

## 备用方案

如果问题仍然持续，可以考虑：
1. **更换SMTP服务商**：使用阿里云、腾讯云等国内邮件服务
2. **异步邮件队列**：将邮件发送放入队列，立即返回成功
3. **多SMTP服务商**：配置主备SMTP服务，自动切换

---

**日期**: 2025-06-26  
**版本**: v1.0  
**状态**: 已实施