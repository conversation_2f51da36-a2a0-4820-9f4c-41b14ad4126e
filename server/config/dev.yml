# 开发环境配置
app:
  prefix: 'api'
  port: 32080
  logger:
    # 项目日志存储路径，相对路径（相对本项目根目录）或绝对路径
    dir: '../logs'
    # 日志级别：error, warn, info, debug
    level: 'debug'
    # 是否启用文件日志
    enableFile: true
    # 日志文件配置
    file:
      # 应用日志文件名
      filename: 'application-%DATE%.log'
      # 错误日志文件名
      errorFilename: 'error-%DATE%.log'
      # 日期格式
      datePattern: 'YYYY-MM-DD'
      # 压缩旧日志
      zippedArchive: true
      # 最大文件大小
      maxSize: '20m'
      # 保留天数
      maxFiles: '14d'
    # 是否启用控制台日志
    enableConsole: true
  # 文件相关
  file:
    # 是否为本地文件服务或cos
    isLocal: true
    # location 文件上传后存储目录，相对路径（相对本项目根目录）或绝对路径
    location: '../upload'
    # 文件服务器地址，这是开发环境的配置 生产环境请自行配置成可访问域名
    domain: 'http://localhost:32080'
    # 文件虚拟路径, 必须以 / 开头， 如 http://localhost:8080/profile/****.jpg  , 如果不需要则 设置 ''
    serveRoot: '/profile'
    # 文件大小限制，单位M
    maxSize: 10
  # 开发环境信任代理（支持 ngrok 等开发工具）
  trustProxy: true

# 腾讯云cos配置
cos:
  secretId: ''
  secretKey: ''
  bucket: ''
  region: ''
  domain: ''
  location: ''

# 数据库配置
db:
  mysql:
    host: '${MYSQL_HOST}'
    username: '${MYSQL_USER}'
    password: '${MYSQL_PASSWORD}'
    database: '${MYSQL_DATABASE}'
    port: ${MYSQL_PORT}
    charset: 'utf8mb4'
    # 优化连接池配置以提高稳定性
    extra:
      connectionLimit: 20          # 增加连接池大小
      queueLimit: 0               # 无限制队列
      waitForConnections: true    # 等待可用连接
      acquireTimeout: 60000       # 获取连接超时时间 60秒
      timeout: 60000              # 查询超时时间 60秒
      reconnect: true             # 自动重连
      idleTimeout: 300000         # 空闲连接超时时间 5分钟
      maxReconnects: 10           # 最大重连次数
      reconnectDelay: 2000        # 重连延迟 2秒
    # 添加超时配置
    connectTimeout: 30000         # 连接超时时间 30秒
    # acquireTimeout 对 MySQL2 无效，已移除
    logger: 'file'
    logging: true
    multipleStatements: true
    dropSchema: false
    synchronize: false
    supportBigNumbers: true
    bigNumberStrings: true
    # 添加重试配置
    retryAttempts: 3
    retryDelay: 3000

# redis 配置
redis:
  host: '${REDIS_HOST}'
  password: '${REDIS_PASSWORD}'
  port: ${REDIS_PORT}
  db: ${REDIS_DB}
  keyPrefix: 'ps_'

# jwt 配置
jwt:
  secretkey: 'vaAaXuEBwlI0pI2EY08E'
  expiresin: '1h'
  refreshExpiresIn: '2h'

# 加密配置
crypto:
  # 用于加密代理凭证的密钥，生产环境请使用环境变量
  proxyCredentialKey: '${PROXY_CREDENTIAL_KEY:DgY3K8xPmR5tN9wL6vQ2JhF7aBcE4sZu}'

# 权限 白名单配置
perm:
  router:
    whitelist:
      [
        { path: '/api/user-management/captchaImage', method: 'GET' },
        { path: '/api/user-management/registerUser', method: 'GET' },
        { path: '/api/user-management/register', method: 'POST' },
        { path: '/api/user-management/login', method: 'POST' },
        { path: '/api/user-management/logout', method: 'POST' },
        { path: '/api/perm/{id}', method: 'GET' },
        { path: '/api/upload', method: 'POST' },
        # Shop routes
        { path: '/api/shop/auth/register', method: 'POST' },
        { path: '/api/shop/auth/login', method: 'POST' },
        { path: '/api/shop/auth/refresh-token', method: 'POST' },
        { path: '/api/shop/auth/reset-password-request', method: 'POST' },
        { path: '/api/shop/auth/reset-password', method: 'POST' },
      ]

# 用户相关
# 初始密码， 重置密码
user:
  initialPassword: '${USER_INITIAL_PASSWORD}'

# 邮件服务配置
email:
  host: '${EMAIL_HOST}'
  port: ${EMAIL_PORT}
  secure: true  # 对于 Gmail 使用 SSL/TLS
  user: '${EMAIL_USER}'
  pass: '${EMAIL_PASS}'
  from: '${EMAIL_FROM}'
  # 重试配置
  maxRetries: 3
  retryDelay: 2000  # 毫秒
  # 连接池配置
  pool: true
  maxConnections: 5
  maxMessages: 100

# 前端应用配置
frontend:
  url: 'http://localhost:3000' # 前端应用的访问地址，用于生成邮件中的链接等
  reset_password_url: 'http://localhost:3000/reset-password'

# 管理员配置
admin:
  # 管理员邮箱列表，使用逗号分隔
  emails_str: '${ADMIN_EMAIL}'

# Zpay 支付配置
zpay:
  # Zpay API 基础地址
  baseUrl: '${ZPAY_BASE_URL}'
  # 商户ID
  pid: '${ZPAY_PID}'
  # 商户密钥
  key: '${ZPAY_KEY}'
  # 请求超时时间（毫秒）
  timeout: 10000
  # 重试次数
  retryCount: 3

