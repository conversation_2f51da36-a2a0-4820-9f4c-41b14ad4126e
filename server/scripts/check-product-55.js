const mysql = require('mysql2/promise');
const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');

// 加载环境变量
require('dotenv').config();

async function checkProduct55() {
  try {
    // 读取配置文件
    const configPath = path.join(__dirname, '../config/dev.yml');
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = yaml.load(configContent);
    
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: '**************',
      user: 'root',
      password: 'UPWEOgIvx9vL9QLvzmJL',
      database: 'proxy_shop',
      port: 34781
    });

    console.log('🔍 查询产品ID为55的状态信息...\n');

    // 执行查询
    const [rows] = await connection.execute(`
      SELECT 
        id,
        name,
        sync_strategy,
        is_price_manual,
        price,
        cost_price,
        profit_rate,
        is_active,
        created_at,
        updated_at,
        external_product_id,
        supplier_id,
        location_city_id
      FROM shop_product 
      WHERE id = 55
    `);

    if (rows.length === 0) {
      console.log('❌ 未找到产品ID为55的记录');
      return;
    }

    const product = rows[0];
    
    console.log('📋 产品ID 55 的当前状态：');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`🆔 产品ID: ${product.id}`);
    console.log(`📝 产品名称: ${product.name}`);
    console.log(`🔄 同步策略: ${product.sync_strategy}`);
    console.log(`💰 手动定价: ${product.is_price_manual ? '是' : '否'}`);
    console.log(`💵 当前价格: ${product.price}`);
    console.log(`💸 成本价格: ${product.cost_price}`);
    console.log(`📊 利润率: ${product.profit_rate}%`);
    console.log(`✅ 是否激活: ${product.is_active ? '是' : '否'}`);
    console.log(`🏭 供应商ID: ${product.supplier_id}`);
    console.log(`🌍 位置城市ID: ${product.location_city_id}`);
    console.log(`🔗 外部产品ID: ${product.external_product_id}`);
    console.log(`📅 创建时间: ${product.created_at}`);
    console.log(`🔄 更新时间: ${product.updated_at}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 分析恢复自动定价操作的结果
    console.log('\n🔍 恢复自动定价操作分析：');
    if (product.is_price_manual === 0) {
      console.log('✅ 成功：is_price_manual 已设置为 0（自动定价）');
    } else {
      console.log('❌ 失败：is_price_manual 仍为 1（手动定价）');
    }

    if (product.sync_strategy === 'full_sync') {
      console.log('✅ 成功：sync_strategy 已设置为 full_sync（全量同步）');
    } else {
      console.log(`⚠️  注意：sync_strategy 为 ${product.sync_strategy}，不是 full_sync`);
    }

    // 关闭连接
    await connection.end();
    
    console.log('\n✅ 查询完成');
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    process.exit(1);
  }
}

// 执行查询
checkProduct55();