#!/usr/bin/env node

/**
 * Shop模块Mock服务测试脚本
 * 验证shop模块Mock功能的正确性和与system模块的一致性
 */

const path = require('path');

// 设置TypeScript路径映射
require('ts-node').register({
  project: path.resolve(__dirname, '../tsconfig.json'),
  transpileOnly: true,
});

// 动态设置环境变量
process.env.MOCK_IPNUX_PROVIDER = 'true';

async function testShopMockService() {
  console.log('🧪 开始测试Shop模块Mock服务...\n');

  try {
    // 导入必要的模块
    const { MockIpnuxService } = await import('../src/module/shop/provider/implementations/mock/mock-ipnux.service');
    const { Provider } = await import('../src/module/system/provider/entities/provider.entity');

    // 创建Mock服务实例
    const shopMockService = new MockIpnuxService();
    
    console.log('✅ Shop MockIpnuxService实例创建成功');

    // 测试初始化
    const mockProvider = new Provider();
    mockProvider.providerCode = 'ipnux';
    
    shopMockService.initialize({}, mockProvider);
    console.log('✅ Shop Mock服务初始化成功');

    // 测试连接
    const connectionResult = await shopMockService.testConnection();
    console.log('✅ 连接测试:', connectionResult);

    // 测试购买静态实例
    console.log('\n📦 测试静态实例购买...');
    const staticPurchaseOptions = {
      productMode: 'STATIC',
      quantity: 2,
      durationDays: 30,
      customerId: 'test_customer_shop',
      orderId: 'test_order_shop_123',
      targetLocation: { country: 'US', city: 'New York' }
    };

    const staticResults = await shopMockService.purchaseInstances(staticPurchaseOptions);
    console.log('静态实例购买结果:', staticResults.length, '个实例');
    staticResults.forEach((result, index) => {
      console.log(`  实例${index + 1}:`, {
        id: result.providerInstanceId,
        ip: result.ipAddress,
        port: result.port,
        username: result.username
      });
    });

    // 验证shop模块特有前缀
    const hasShopPrefix = staticResults.every(r => r.providerInstanceId.startsWith('shop_mock_'));
    console.log('✅ Shop前缀验证:', hasShopPrefix ? '通过' : '失败');

    // 验证端口范围
    const portsInRange = staticResults.every(r => r.port >= 9000 && r.port < 10000);
    console.log('✅ 端口范围验证:', portsInRange ? '通过 (9000+)' : '失败');

    // 测试购买动态通道
    console.log('\n🌐 测试动态通道购买...');
    const dynamicPurchaseOptions = {
      productMode: 'DYNAMIC',
      quantity: 1,
      durationDays: 15,
      customerId: 'test_customer_shop',
      orderId: 'test_order_shop_456',
      channelName: 'ShopTestChannel',
      limitTrafficGb: 20
    };

    const dynamicResults = await shopMockService.purchaseInstances(dynamicPurchaseOptions);
    console.log('动态通道购买结果:', dynamicResults.length, '个通道');
    dynamicResults.forEach((result, index) => {
      console.log(`  通道${index + 1}:`, {
        id: result.providerInstanceId,
        name: result.channelName,
        totalFlowMb: result.totalFlowMb
      });
    });

    // 测试实例详情查询
    console.log('\n📋 测试实例详情查询...');
    const staticInstanceId = staticResults[0].providerInstanceId;
    const instanceDetails = await shopMockService.getInstanceDetails({
      providerInstanceId: staticInstanceId,
      productMode: 'STATIC'
    });
    
    if (instanceDetails) {
      console.log('✅ 静态实例详情查询成功:', {
        id: instanceDetails.providerInstanceId,
        status: instanceDetails.status,
        ip: instanceDetails.ipAddress
      });
    }

    // 测试续费
    console.log('\n🔄 测试实例续费...');
    const renewResult = await shopMockService.renewInstance({
      providerInstanceId: staticInstanceId,
      currentExpiresAt: new Date(),
      durationDays: 15,
      productMode: 'STATIC',
      customerId: 'test_customer_shop'
    });
    
    console.log('✅ 续费测试成功:', {
      newExpiresAt: renewResult.newExpiresAt,
      orderId: renewResult.providerOrderId
    });

    // 测试通道流量查询（动态实例）
    if (dynamicResults.length > 0) {
      console.log('\n📊 测试通道流量查询...');
      const channelId = dynamicResults[0].providerInstanceId;
      const trafficResult = await shopMockService.getChannelTraffic({
        providerInstanceId: channelId
      });
      
      console.log('✅ 流量查询成功:', {
        总流量: `${trafficResult.totalTrafficGb.toFixed(2)}GB`,
        记录数: trafficResult.items.length
      });
    }

    // 测试端点生成
    if (dynamicResults.length > 0) {
      console.log('\n🔗 测试端点生成...');
      const channelId = dynamicResults[0].providerInstanceId;
      const endpoints = await shopMockService.generateEndpoints({
        providerInstanceId: channelId,
        count: 3,
        location: 'US',
        stickySessionTime: 600
      });
      
      console.log('✅ 端点生成成功:', endpoints);
      
      // 验证shop模块特有域名
      const hasShopDomain = endpoints.every(endpoint => endpoint.includes('shop-mock-proxy.example.com'));
      console.log('✅ Shop域名验证:', hasShopDomain ? '通过' : '失败');
      
      // 验证端口范围
      const endpointPorts = endpoints.map(e => parseInt(e.split(':')[1]));
      const endpointsInRange = endpointPorts.every(port => port >= 11000);
      console.log('✅ 端点端口验证:', endpointsInRange ? '通过 (11000+)' : '失败');
    }

    console.log('\n🎉 Shop模块Mock服务测试完成！');
    console.log('\n📊 总结报告:');
    console.log('- ✅ 服务初始化和连接测试');
    console.log('- ✅ 静态实例购买和查询');
    console.log('- ✅ 动态通道购买和管理');
    console.log('- ✅ 实例续费功能');
    console.log('- ✅ 流量查询和端点生成');
    console.log('- ✅ Shop模块特有标识验证');
    console.log('\n🏆 所有测试通过，shop模块Mock功能运行正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
    process.exit(1);
  }
}

// 运行测试
testShopMockService();