/**
 * 产品地理位置映射完整性测试脚本
 * 模拟产品同步过程，验证supplier_location_mapping是否完整
 */

const { DataSource } = require('typeorm');
const path = require('path');

// 数据库配置
const dataSource = new DataSource({
  type: 'mysql',
  host: '**************',
  port: 34781,
  username: 'root',
  password: 'UPWEOgIvx9vL9QLvzmJL',
  database: 'proxy_shop',
  entities: [],
  synchronize: false,
  logging: false,
});

class ProductSyncMappingTester {
  constructor() {
    this.logger = console;
    this.results = {
      totalProducts: 0,
      mappingSuccessCount: 0,
      mappingFailCount: 0,
      missingMappings: [],
      validMappings: [],
      duplicateCities: []
    };
  }

  async initialize() {
    try {
      await dataSource.initialize();
      this.logger.log('✅ 数据库连接成功');
    } catch (error) {
      this.logger.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async testMappingCompleteness() {
    this.logger.log('\n🧪 开始测试产品地理位置映射完整性...\n');

    try {
      // 1. 获取所有产品及其供应商位置信息
      const query = `
        SELECT 
          p.id,
          p.product_name,
          p.location_city_id,
          p.region,
          p.country,
          p.city,
          p.country_code,
          JSON_EXTRACT(p.product_config_details, '$.supplierLocation.cityId') as supplier_city_id,
          JSON_EXTRACT(p.product_config_details, '$.supplierLocation.cityName') as supplier_city_name,
          JSON_EXTRACT(p.product_config_details, '$.supplierLocation.countryCode') as supplier_country_code,
          JSON_EXTRACT(p.product_config_details, '$.supplierLocation.continentName') as supplier_continent_name
        FROM shop_product p 
        WHERE p.del_flag = '0'
        ORDER BY p.id
      `;

      const products = await dataSource.query(query);
      this.results.totalProducts = products.length;

      this.logger.log(`📊 发现 ${products.length} 个有效产品`);
      this.logger.log('');

      // 2. 遍历每个产品，测试映射逻辑
      for (const product of products) {
        await this.testProductMapping(product);
      }

      // 3. 检查重复的供应商城市ID
      await this.checkDuplicateSupplierCities();

      // 4. 生成测试报告
      this.generateReport();

    } catch (error) {
      this.logger.error('❌ 测试执行失败:', error.message);
      throw error;
    }
  }

  async testProductMapping(product) {
    const supplierCityId = product.supplier_city_id ? product.supplier_city_id.replace(/"/g, '') : null;
    const supplierCityName = product.supplier_city_name ? product.supplier_city_name.replace(/"/g, '') : null;

    if (!supplierCityId) {
      this.logger.log(`⚠️  ${product.product_name} (ID: ${product.id}): 无供应商城市ID`);
      return;
    }

    // 模拟产品同步服务的映射查找逻辑
    const mappingResult = await this.findMapping(1, supplierCityId); // providerId = 1

    if (mappingResult.success) {
      this.results.mappingSuccessCount++;
      this.results.validMappings.push({
        productId: product.id,
        productName: product.product_name,
        supplierCityId: supplierCityId,
        supplierCityName: supplierCityName,
        standardCityId: mappingResult.standardCityId,
        standardCityName: mappingResult.standardCityName,
        currentLocationCityId: product.location_city_id,
        consistencyCheck: product.location_city_id === mappingResult.standardCityId ? '✅ 一致' : '⚠️ 不一致'
      });
      
      const consistencyIcon = product.location_city_id === mappingResult.standardCityId ? '✅' : '⚠️';
      this.logger.log(`${consistencyIcon} ${product.product_name}: ${supplierCityId}(${supplierCityName}) → ${mappingResult.standardCityId}(${mappingResult.standardCityName})`);
    } else {
      this.results.mappingFailCount++;
      this.results.missingMappings.push({
        productId: product.id,
        productName: product.product_name,
        supplierCityId: supplierCityId,
        supplierCityName: supplierCityName,
        reason: mappingResult.reason
      });
      
      this.logger.log(`❌ ${product.product_name}: ${supplierCityId}(${supplierCityName}) - ${mappingResult.reason}`);
    }
  }

  async findMapping(providerId, supplierCityId) {
    try {
      // 1. 精确映射查找
      const mappingQuery = `
        SELECT 
          m.standard_city_id,
          c.city_name as standard_city_name,
          co.country_name,
          r.region_name
        FROM supplier_location_mapping m
        LEFT JOIN sys_location_city c ON m.standard_city_id = c.id
        LEFT JOIN sys_location_country co ON c.country_id = co.id
        LEFT JOIN sys_location_region r ON co.region_id = r.id
        WHERE m.provider_id = ? AND m.supplier_native_city_id = ? AND m.del_flag = '0'
      `;

      const mapping = await dataSource.query(mappingQuery, [providerId, supplierCityId]);

      if (mapping.length > 0) {
        return {
          success: true,
          standardCityId: mapping[0].standard_city_id,
          standardCityName: mapping[0].standard_city_name,
          standardCountryName: mapping[0].country_name,
          standardRegionName: mapping[0].region_name
        };
      }

      // 2. 如果没有精确映射，返回失败
      return {
        success: false,
        reason: '未找到对应的供应商位置映射'
      };

    } catch (error) {
      return {
        success: false,
        reason: `映射查找错误: ${error.message}`
      };
    }
  }

  async checkDuplicateSupplierCities() {
    this.logger.log('\n🔍 检查重复的供应商城市ID...');

    const query = `
      SELECT 
        JSON_EXTRACT(product_config_details, '$.supplierLocation.cityId') as supplier_city_id,
        JSON_EXTRACT(product_config_details, '$.supplierLocation.cityName') as supplier_city_name,
        COUNT(*) as count,
        GROUP_CONCAT(id) as product_ids,
        GROUP_CONCAT(product_name) as product_names
      FROM shop_product 
      WHERE del_flag = '0' 
        AND JSON_EXTRACT(product_config_details, '$.supplierLocation.cityId') IS NOT NULL
      GROUP BY JSON_EXTRACT(product_config_details, '$.supplierLocation.cityId'), 
               JSON_EXTRACT(product_config_details, '$.supplierLocation.cityName')
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;

    const duplicates = await dataSource.query(query);
    
    if (duplicates.length > 0) {
      this.logger.log(`⚠️  发现 ${duplicates.length} 个重复的供应商城市ID:`);
      duplicates.forEach(dup => {
        const cityId = dup.supplier_city_id.replace(/"/g, '');
        const cityName = dup.supplier_city_name.replace(/"/g, '');
        this.logger.log(`   ${cityId}(${cityName}): ${dup.count}个产品 [${dup.product_ids}]`);
        this.results.duplicateCities.push({
          supplierCityId: cityId,
          supplierCityName: cityName,
          count: dup.count,
          productIds: dup.product_ids,
          productNames: dup.product_names
        });
      });
    } else {
      this.logger.log('✅ 没有发现重复的供应商城市ID');
    }
  }

  generateReport() {
    const successRate = ((this.results.mappingSuccessCount / this.results.totalProducts) * 100).toFixed(1);
    
    this.logger.log('\n' + '='.repeat(80));
    this.logger.log('📋 测试结果报告');
    this.logger.log('='.repeat(80));
    this.logger.log(`📊 总产品数量: ${this.results.totalProducts}`);
    this.logger.log(`✅ 映射成功: ${this.results.mappingSuccessCount}`);
    this.logger.log(`❌ 映射失败: ${this.results.mappingFailCount}`);
    this.logger.log(`📈 映射成功率: ${successRate}%`);
    this.logger.log(`🔄 重复城市ID: ${this.results.duplicateCities.length}`);

    if (this.results.mappingFailCount > 0) {
      this.logger.log('\n❌ 映射失败的产品:');
      this.results.missingMappings.forEach(item => {
        this.logger.log(`   产品ID ${item.productId}: ${item.productName} - ${item.supplierCityId}(${item.supplierCityName}) - ${item.reason}`);
      });
    }

    if (this.results.validMappings.some(m => m.consistencyCheck.includes('不一致'))) {
      this.logger.log('\n⚠️  数据一致性问题:');
      this.results.validMappings
        .filter(m => m.consistencyCheck.includes('不一致'))
        .forEach(item => {
          this.logger.log(`   产品ID ${item.productId}: 当前location_city_id=${item.currentLocationCityId}, 映射结果=${item.standardCityId}`);
        });
    }

    this.logger.log('\n🎯 结论:');
    if (this.results.mappingFailCount === 0) {
      this.logger.log('✅ 所有产品都能正确找到地理位置映射！重新同步是安全的。');
    } else {
      this.logger.log('❌ 存在映射缺失问题，需要补充映射后才能安全重新同步。');
    }

    this.logger.log('='.repeat(80));
  }

  async close() {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      this.logger.log('\n✅ 数据库连接已关闭');
    }
  }
}

// 执行测试
async function runTest() {
  const tester = new ProductSyncMappingTester();
  
  try {
    await tester.initialize();
    await tester.testMappingCompleteness();
  } catch (error) {
    console.error('测试执行失败:', error);
    process.exit(1);
  } finally {
    await tester.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest();
}

module.exports = { ProductSyncMappingTester };