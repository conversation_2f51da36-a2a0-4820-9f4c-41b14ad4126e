/**
 * 项目 Token 获取工具
 * 
 * 支持获取两个项目的 Token：
 * - admin-web: 管理后台项目 (Vue.js)
 * - shop-web: 商城前端项目 (React)
 * 
 * 使用方法：
 * 1. 直接运行: node get-project-token.js
 * 2. 作为模块: const { fetchToken } = require('./get-project-token');
 * 
 * 环境变量：
 * - SERVER_PORT: API 服务端口 (默认: 3000)
 */

const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');
const readline = require('readline');

// 加载.env.local文件
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// 获取SERVER_PORT
const SERVER_PORT = process.env.SERVER_PORT || '3000';
const BASE_URL = `http://localhost:${SERVER_PORT}`;

// 项目配置
const projectsConfig = {
  'admin-web': {
    name: 'Admin Web (管理后台)',
    url: `${BASE_URL}/api/user-management/login`,
    credentials: { userName: 'admin', password: 'Sz123456' },
    tokenPath: 'data.token',
    description: '管理后台项目 - 用于 admin-web Vue.js 应用'
  },
  'shop-web': {
    name: 'Shop Web (商城前端)',
    url: `${BASE_URL}/api/shop/auth/login`,
    credentials: { email: '<EMAIL>', password: 'Sz123456' },
    tokenPath: 'data.access_token',
    description: '商城前端项目 - 用于 shop-web React 应用'
  }
};

/**
 * 安全获取嵌套对象属性
 * @param {object} obj - 目标对象
 * @param {string} path - 属性路径 (如 'data.token')
 * @returns {*} 属性值或 undefined
 */
function getNestedProperty(obj, path) {
  if (!obj || !path) return undefined;
  const parts = path.split('.');
  let current = obj;
  for (const part of parts) {
    if (current === null || typeof current !== 'object' || !current.hasOwnProperty(part)) {
      return undefined;
    }
    current = current[part];
  }
  return current;
}

/**
 * 获取指定项目的 Token
 * @param {string} projectName - 项目名称 ('admin-web' 或 'shop-web')
 * @returns {Promise<string>} Token字符串
 */
async function fetchToken(projectName) {
  const config = projectsConfig[projectName];
  
  if (!config) {
    throw new Error(`未找到项目配置: "${projectName}". 可用项目: ${Object.keys(projectsConfig).join(', ')}`);
  }

  console.log(`🔐 正在获取 ${config.name} 的令牌...`);
  console.log(`📡 请求地址: ${config.url}`);

  try {
    const response = await axios.post(config.url, config.credentials);

    if (response.status !== 200) {
      throw new Error(`API 请求失败，状态码: ${response.status}`);
    }

    const token = getNestedProperty(response.data, config.tokenPath);

    if (!token) {
      console.error('❌ 未在响应中找到 token');
      console.error('响应数据:', JSON.stringify(response.data, null, 2));
      throw new Error(`Token 提取失败，路径: ${config.tokenPath}`);
    }

    console.log(`✅ ${config.name} 令牌获取成功`);
    return token;

  } catch (error) {
    if (error.response) {
      console.error(`❌ API 错误 (${error.response.status}):`, error.response.data);
    } else if (error.request) {
      console.error('❌ 网络错误:', error.message);
    } else {
      console.error('❌ 请求错误:', error.message);
    }
    throw error;
  }
}

/**
 * 交互式选择项目
 * @returns {Promise<string>} 选择的项目名称
 */
function selectProject() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('\n🚀 项目 Token 获取工具');
    console.log('━'.repeat(40));
    console.log('请选择要获取 Token 的项目:');
    console.log('1. admin-web (管理后台)');
    console.log('2. shop-web (商城前端)');
    console.log('━'.repeat(40));

    rl.question('请输入选项 (1 或 2): ', (answer) => {
      rl.close();
      
      switch (answer.trim()) {
        case '1':
          resolve('admin-web');
          break;
        case '2':
          resolve('shop-web');
          break;
        default:
          console.log('❌ 无效选项，默认选择 admin-web');
          resolve('admin-web');
      }
    });
  });
}

// 主执行函数
async function main() {
  try {
    console.log(`📍 服务器端口: ${SERVER_PORT}`);
    
    const projectName = await selectProject();
    const config = projectsConfig[projectName];
    
    console.log(`\n📋 项目信息:`);
    console.log(`   名称: ${config.name}`);
    console.log(`   描述: ${config.description}`);
    console.log(`   接口: ${config.url}`);
    
    const token = await fetchToken(projectName);
    
    console.log('\n🎉 获取成功!');
    console.log('━'.repeat(60));
    console.log('Token:');
    console.log(token);
    console.log('━'.repeat(60));
    console.log('\n📋 使用方法:');
    console.log('在 HTTP 请求头中添加:');
    console.log(`Authorization: Bearer ${token}`);
    console.log('\n💡 提示:');
    if (projectName === 'admin-web') {
      console.log('   - 用于管理后台 API 请求 (/api/*)');
      console.log('   - 适用于 admin-web Vue.js 项目');
    } else {
      console.log('   - 用于商城前端 API 请求 (/api/shop/*)');
      console.log('   - 适用于 shop-web React 项目');
    }

  } catch (error) {
    console.error('\n💥 操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { fetchToken, projectsConfig };