
### **核心目标**

将 iPNUX 供应商的动态IP产品无缝集成到您现有的代理商城系统中，包括后端服务逻辑、管理后台功能以及用户商城的功能页面。

### **功能实现清单**

以下是为实现此目标所需完成的功能点，分为后端、管理后台和用户商城三个部分。

#### **一、 服务器端 (Backend - `server/`)**

1. **扩展供应商接口 (`ProxyProvider`)**
    * **目的**: 定义动态IP Channel管理所需的新方法，确保所有供应商实现都遵循统一的契est'k'c。
    * **任务**:
        * 在 `server/src/module/system/provider/interfaces/proxy-provider.interface.ts` 中，为 `ProxyProvider` 接口添加以下可选方法：
            * `updateChannel(options: UpdateChannelOptions): Promise<boolean>`: 用于更新Channel的名称、密码、流量限制和启用状态。
            * `getChannelTraffic(options: GetChannelTrafficOptions): Promise<ChannelTrafficResult>`: 用于获取指定Channel的流量使用情况。
            * `generateEndpoints(options: GenerateEndpointOptions): Promise<string[]>`: 用于为指定Channel生成代理端点。
            * `listEndpoints(providerInstanceId: string): Promise<any[]>`: (建议新增) 用于获取已生成的端点列表。
            * `deleteEndpoint(endpointProviderId: string): Promise<boolean>`: (建议新增) 用于删除指定的代理端点。

2. **实现 iPNUX 动态IP接口 (`IpnuxService`)**
    * **目的**: 在现有的 `IpnuxService` 中实现动态IP相关的API调用逻辑。
    * **任务**:
        * **购买逻辑**: 修改 `purchaseInstances` 方法，当检测到产品模式为 `DYNAMIC` 时，调用 iPNUX 的 `OpenApiCreateChannel` 接口创建子Channel，并将返回的 `ChannelId` 作为 `providerInstanceId`，`ChannelPassword` 作为 `password` 存储。
        * **获取详情**: 修改 `getInstanceDetails` 方法，当检测到是动态IP实例时，调用 iPNux 的 `OpenApiSubUsers` 接口获取Channel的详细信息（如状态、已用流量等）。
        * **更新Channel**: 实现新增的 `updateChannel` 方法，调用 iPNux 的 `OpenApiEditChannel` 接口。
        * **获取流量**: 实现新增的 `getChannelTraffic` 方法，调用 iPNux 的 `OpenApiGetUseTraffic` 接口。
        * **生成端点**: 实现新增的 `generateEndpoints` 方法，调用 iPNux 的 `OpenApiGenerateCustomEndpoints` 接口。
        * **(建议)** 实现 `listEndpoints` 和 `deleteEndpoint` 方法，虽然 iPNUX API 文档中没有直接提供列出和删除单个端点的接口，但您需要在这里实现相应的业务逻辑（例如，从您自己的数据库 `shop_proxy_endpoints` 中查询和删除）。

3. **完善订单与代理实例逻辑**
    * **目的**: 使系统能够处理动态IP产品的购买、激活和管理。
    * **任务**:
        * **订单履行 (`OrderFulfillmentService`)**: 确保 `triggerFulfillment` 方法能够识别动态IP产品，并调用 `IpnuxService` 的动态IP购买逻辑。
        * **数据实体 (`ProxyInstance`)**: 现有 `shop_proxy_instances` 表和 `ProxyInstance` 实体已包含 `channel_name`, `total_flow`, `used_flow` 等字段，无需重大修改。确保在创建和更新动态IP实例时，正确填充这些字段。
        * **数据实体 (`ProxyEndpoint`)**: `shop_proxy_endpoints` 表用于存储生成的代理端点，确保在调用 `generateEndpoints` 后，将结果存入此表，并与 `ProxyInstance` 关联。

4. **开发后端API接口 (Controllers)**
    * **目的**: 为前端提供管理和使用动态IP功能所需的API。
    * **任务**:
        * **管理后台API (`system/proxy/controllers/dynamic/index.controller.ts`)**:
            * `GET /:id/traffic`: 新增接口，用于获取指定Channel的流量使用情况。
            * `GET /:id/endpoints`: 新增接口，用于获取指定Channel已生成的端点列表。
            * `POST /:id/endpoints`: 新增接口，用于为指定Channel生成新的端点。
            * `DELETE /endpoints/:endpointId`: 新增接口，用于删除指定的端点。
            * `PUT /:id`: 修改现有接口，以支持更新Channel的名称、密码、流量限制和状态。
        * **用户商城API (`shop/proxy/controllers/dynamic-proxy.controller.ts`)**:
            * 参照管理后台API，为用户提供类似的功能，但要确保权限隔离，用户只能操作自己的资源。

#### **二、 管理后台 (Admin-Web)**

1. **动态代理管理页面 (`admin-web/src/views/shop/proxy/dynamic/index.vue`)**
    * **目的**: 增强动态代理管理页面的功能，使其支持完整的Channel生命周期管理。
    * **任务**:
        * 在操作列中为每一行动态代理实例添加 "管理端点" 和 "流量使用" 按钮。
        * **管理端点功能**:
            * 创建一个新的对话框（Dialog），包含两个标签页： "生成新端点" 和 "现有端点列表"。
            * "生成新端点" 标签页包含表单，用于输入位置、会话保持时间、生成数量等参数，并调用后端API。
            * "现有端点列表" 标签页使用表格展示已生成的端点，并提供删除操作。
        * **流量使用功能**:
            * 创建一个新的对话框，内嵌ECharts图表，用于可视化展示流量使用趋势。
            * 提供时间范围（今日、近7天、近30天、自定义）筛选器。
        * **编辑功能**:
            * 修改现有的 "编辑" 功能对话框，允许管理员修改Channel的名称、密码、流量限制和状态。

2. **API客户端 (`admin-web/src/api/shop/proxy/index.js`)**
    * **目的**: 添加调用新后端API的函数。
    * **任务**:
        * 添加 `getDynamicProxyChannelTraffic`, `generateDynamicProxyEndpoints`, `listDynamicProxyEndpoints`, `deleteDynamicProxyEndpoint`, `updateDynamicProxyChannel` 等函数。

#### **三、 用户商城 (Shop-Web)**

1. **产品购买流程**
    * **目的**: 允许用户在商城中购买动态IP产品。
    * **任务**:
        * 确保产品列表页能够正确展示动态IP产品。
        * 修改或创建新的结账页面（类似于 `StaticIPCheckout.tsx`），以适应动态IP产品的购买逻辑（例如，动态IP可能没有具体的地理位置选择，而是直接购买流量包）。

2. **用户中心 - 代理管理页面**
    * **目的**: 为用户提供管理自己购买的动态IP Channel的功能。
    * **任务**:
        * 在 `PurchasedIPList.tsx` 或创建一个新组件中，为动态IP产品提供一个专门的管理界面。
        * **功能点**:
            * **Channel信息展示**: 显示Channel名称、密码（可隐藏/显示）、总流量、已用流量、到期时间等。
            * **端点生成与管理**: 提供一个与管理后台类似的界面，允许用户按需生成代理端点，并可以复制、查看和删除已生成的端点。
            * **流量查询**: 以图表或列表形式展示流量使用情况。
            * **认证信息修改**: 允许用户修改Channel的密码。

3. **API服务 (`shop-web/src/services/`)**
    * **目的**: 添加调用商城端动态IP相关API的函数。
    * **任务**:
        * 在 `shop-web/src/services/generated/api.ts` 中或创建新的服务文件，添加与 `DynamicProxyController` 交互的函数。

### **设计修改文件清单**

以下是预计需要修改或创建的文件列表：

#### **服务器端 (`server/`)**

* **接口定义**
    * `src/module/system/provider/interfaces/proxy-provider.interface.ts` (修改): 添加 `updateChannel`, `getChannelTraffic`, `generateEndpoints` 等新接口定义。
* **供应商实现**
    * `src/module/system/provider/implementations/ipnux/ipnux.service.ts` (修改): 实现上述新接口，调用iPNUX的API。
    * `src/module/shop/provider/implementations/ipnux/ipnux.service.ts` (修改/同步): 如果shop模块有独立实现，也需同步修改。
* **核心服务**
    * `src/module/shop/proxy/services/order-fulfillment.service.ts` (修改): 调整订单履行逻辑，以支持动态IP产品的创建。
    * `src/module/shop/proxy/services/dynamic-proxy.service.ts` (修改): 增加处理流量查询、端点管理等业务逻辑。
    * `src/module/system/proxy/proxy.service.ts` (修改): 增加管理后台所需的动态代理管理逻辑。
* **控制器**
    * `src/module/system/proxy/controllers/dynamic/index.controller.ts` (修改): 新增和修改API端点，供管理后台调用。
    * `src/module/shop/proxy/controllers/dynamic-proxy.controller.ts` (修改): 新增和修改API端点，供用户商城调用。
* **数据实体 (检查即可，可能无需修改)**
    * `src/module/shop/proxy/entities/proxy-instance.entity.ts`
    * `src/module/shop/proxy/entities/proxy-endpoint.entity.ts`
    * `src/module/shop/proxy/entities/proxy-flow-usage.entity.ts`
* **DTOs**
    * `src/module/system/proxy/dto/index.ts` (可能新增): 为端点生成、流量查询等创建新的数据传输对象。

#### **管理后台 (`admin-web/`)**

* **API层**
    * `src/api/shop/proxy/index.js` (修改): 添加新API的调用函数。
* **视图层**
    * `src/views/shop/proxy/dynamic/index.vue` (修改): 增加新的按钮和操作逻辑，引入新的对话框组件。
* **组件层 (可能新增)**
    * `src/views/shop/proxy/dynamic/components/EndpointManagerDialog.vue` (新增): 管理端点的对话框组件。
    * `src/views/shop/proxy/dynamic/components/TrafficChartDialog.vue` (新增): 展示流量图表的对话框组件。

#### **用户商城 (`shop-web/`)**

* **API服务层**
    * `src/services/generated/api.ts` (修改或新增服务文件): 添加与商城后端动态代理API交互的函数。
* **页面/组件层**
    * `src/pages/products/static-residential-proxies/PurchasedIPList.tsx` (重大修改或复制为新组件): 增加动态IP的管理UI，或者创建一个全新的 `PurchasedDynamicIPList.tsx` 组件。
    * `src/pages/orders/StaticIPCheckout.tsx` (可能修改或复制为新组件): 调整以支持动态IP产品的购买流程。
* **状态管理 (Zustand)**
    * `src/store/order-creation.ts` (可能修改): 如果购买流程复杂，可能需要调整全局状态。

---

**总结建议**:

1. **从后端开始**: 优先完成后端接口的定义和实现，这是所有前端功能的基础。
2. **先管理后台，后用户商城**: 先实现管理后台的全部功能，可以方便地进行功能测试和数据验证。
3. **复用与抽象**: 在前端开发中，尽可能复用现有组件（如Dialog、Table），并考虑将动态IP和静态IP的管理UI进行抽象，以减少代码重复。
4. **数据模型**: 您的数据库设计已经比较完善，`shop_proxy_instances` 和 `shop_proxy_endpoints` 这两个表是实现动态IP功能的关键，请确保在开发过程中正确地使用它们。

这份清单涵盖了从后端接口到底层实现，再到前后端UI交互的完整链路。按照此清单逐步实施，可以确保您系统、全面地完成此次功能迭代。

---

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/create-channel.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#create-a-new-sub-channel>)
Create a new sub channel
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiCreateChannel`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#request-body>)
Request Body
Name
Type
Description
ChannelName*
String
ChannelLimitTrafficGb
Integer
Available flow values. If not filled or filled with 0, the traffic used is not limited.
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": {
    "ChannelId": id,
    "ChannelStatus": 1,
    "ChannelName": "name",
    "ChannelPassword": "password",
    "UsedTraffic": 0.0,
    "ChannelLimitTrafficGb": 0.0,
    "CreateTime": "2023-07-29T02:58:17.4265414+00:00"
  }
}
```

[PreviousGet channel traffic](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/get-channel-traffic>)[NextUpdate channel](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/update-channel>)
Last updated 1 year ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/get-channel-traffic.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#get-traffic-usage-of-specified-channel>)
Get traffic usage of specified channel
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiGetUseTraffic`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#request-body>)
Request Body
Name
Type
Description
ChannelId
Integer
If not filled, query the traffic used by all channels.
DateType*
String
Available parameters: 1=today 2=last 7 days 3=last 30 days 4=custom. If custom type is selected you can provide StartTime and EndTime parameters or leave them empty and then they will have default values, sub-user creation date, and current date respectively.
StartTime
date
Use date format yyyy-mm-dd
EndTime
date
Use date format yyyy-mm-dd
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": {
    "Items": [
      {
        "ChannelId": 8,
        "UseTraffic": 0.00000,
        "UseTime": "2023-07-29T00:00:00"
      }
    ],
    "UseTrafficGb": {
      "TotalTrafficGb": 0.00000
    }
  }
}

```

[PreviousGet channels](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/get-channels>)[NextCreate channel](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/create-channel>)
Last updated 1 year ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/get-channels.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#get-a-list-of-active-sub-channels>)
Get a list of active sub channels
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiSubUsers`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#request-body>)
Request Body
Name
Type
Description
ChannelId
Integer
Fill in 0 to query all channels
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": [
    {
      "ChannelId": 6,
      "ChannelStatus": 2,
      "ChannelName": "name",
      "ChannelPassword": password",
      "UseTrafficGb": 3.14,
      "LimieTrafficGb": 2.00,
      "CreateTime": "2023-06-13T10:08:41"
    }
  ]
}
```

[PreviousGet subscriptions](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/get-subscriptions>)[NextGet channel traffic](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/get-channel-traffic>)
Last updated 1 year ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/get-subscriptions.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#detailed-information-about-your-current-subscription>)
Detailed information about your current subscription
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiGetPlanInfo`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
"Code":1000,
"Message":"Successful",
"Data": {
"PlanName":"Starter",
"PlanTrafficGb":10.00,
"TrafficGb":10.00000,
"ExpirationDate":"2023-08-25T07:03:21",
"Expired":false,
"Price":80.00,
"PricePerGB":8.00,
"PlanType":3,
"PlanresidueTrafficGb":10.00000
  }
}
```

[PreviousIPNux Residential](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential>)[NextGet channels](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/get-channels>)
Last updated 1 year ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/location-settings/country.md

```markdown
You can choose global random, continent random, specified country：
Country
Country code
Global random
Global
North American random
NArandom
South american random
SArandom
European random
EUrandom
Asian random
ASrandom
Oceania Random
OArandom
African random
AFrandom
Brazil
BR
India
IN
Saudi Arabia
SA
Spain
ES
Russia
RU
Morocco
MA
Philippines
PH
Mexico
MX
Indonesia
ID
Iraq
IQ
Iran
IR
South Africa
ZA
UAE
AE
Vietnam
VN
Pakistan
PK
Bangladesh
BD
Colombia
CO
Malaysia
MY
Jordan
JO
Thailand
TH
Argentina
AR
Syria
SY
Algeria
DZ
Ecuador
EC
Turkey
TR
Tunisia
TN
Kazakhstan
KZ
France
FR
Italy
IT
Serbia
RS
Peru
PE
Venezuela
VE
Ukraine
UA
Egypt
EG
Dominica
DO
Uzbekistan
UZ
Azerbaijan
AZ
Costa rica
CR
Israel
IL
Kenya
KE
Chile
CL
Oman
OM
Uruguay
UY
Myanmar
MM
Lebanon
LB
United States
US
El Salvador
SV
Guatemala
GT
Honduras
HN
Senegal
SN
Paraguay
PY
Bahrain
BH
Panama
PA
Kuwait
KW
Nigeria
NG
Bosnia and Herzegovina
BA
Moldova
MD
Nepal
NP
Kyrgyzstan
KG
Belarus
BY
Angola
AO
United Kingdom
GB
Germany
DE
Greece
GR
Palestine
PS
Ethiopia
ET
Albania
AL
Portugal
PT
Nicaragua
NI
Bolivia
BO
Romania
RO
Jamaica
JM
Qatar
QA
Former yurt macedonia
MK
Canada
CA
Poland
PL
Tai Wan
TW
Gabon
GA
Georgia
GE
Libya
LY
Australia
AU
Singapore
SG
Mongolia
MN
Hungary
HU
Mauritius
MU
Sri Lanka
LK
Bulgaria
BG
Armenia
AM
Croatia
HR
Reunion
RE
Mali
ML
Hong Kong
HK
Cote d'Ivoire
CI
Togo
TG
Mozambique
MZ
Ireland
IE
Netherlands
NL
Belgium
BE
Republic of Congo)
CG
Rwanda
RW
Guyana
GY
Zambia
ZM
Uganda
UG
Somalia
SO
New Zealand
NZ
Cameroon
CM
Suriname
SR
Burkina Faso
BF
Ghana
GH
Laos
LA
Cyprus
CY
Tajikistan
TJ
Montenegro
ME
Trinidad and Tobago
TT
Congo (Kinshasa)
CD
Japan
JP
Yemen
YE
Tanzania
TZ
Cambodia
KH
Barbados
BB
Madagascar
MG
Mauritania
MR
Slovenia
SI
Zimbabwe
ZW
Czech republic
CZ
South Korea
KR
Benin
BJ
Slovakia
SK
Botswana
BW
Fiji
FJ
Malta
MT
Haiti
HT
Brunei
BN
Switzerland
CH
Latvia
LV
Sweden
SE
Austria
AT
Mayotte
YT
Puerto rico
PR
Gambia
GM
Curacao
CW
French Guiana
GF
Lithuania
LT
New caledonia
NC
Malawi
MW
Sierra Leone
SL
Namibia
NA
Martinique
MQ
Cape verde
CV
Estonia
EE
Djibouti
DJ
Guinea Bissau
GW
Seychelles
SC
Saint lucia
LC
Luxembourg
LU
Maldives
MV
Norway
NO
Antigua and Barbuda
AG
Grenada
GD
Denmark
DK
Bhutan
BT
Finland
FI
Andorra
AD
Belize
BZ
Liberia
LR
Guadeloupe
GP
Guam
GU
Saint Vincent and the Grenadines
VC
Jersey
JE
Lesotho
LS
Sao tome and principe
ST
Dominica
DM
Bahamas
BS
Equatorial Guinea
GQ
Sudan
SD
Aruba
AW
Bonaire
BQ
Saint martin
SX
Guinea
GN
Chad
TD
Iceland
IS
Saint Kitts and Nevis
KN
Guernsey
GG
St. Maarten
MF
Gibraltar
GI
The British Virgin Islands
VG
Northern mariana
MP
United States Virgin Islands
VI
San marino
SM
Monaco
MC
Turks and Caicos Islands
TC
Tonga
TO
Cayman Islands
KY
Greenland
GL
Isle of Man
IM
French Polynesia
PF
Liechtenstein
LI
Wallis and futuna
WF
[PreviousLocation Settings](https://help.ipnux.com/proxies/ipnux-residential/location-settings/</proxies/ipnux-residential/location-settings>)[NextState and City](https://help.ipnux.com/proxies/ipnux-residential/location-settings/</proxies/ipnux-residential/location-settings/state-and-city>)
Last updated 1 year ago

```

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/location-settings/state-and-city.md

```markdown
You can choose to specify state, city targeting：
[PreviousCountry](https://help.ipnux.com/proxies/ipnux-residential/location-settings/</proxies/ipnux-residential/location-settings/country>)[NextIPNux Static Residential](https://help.ipnux.com/proxies/ipnux-residential/location-settings/</proxies/ipnux-static-residential>)
Last updated 1 year ago

```

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/location-settings.md

```markdown
You can find more information on selecting country, city or state parameters in the pages listed below:
[PreviousUpdate proxy](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/update-proxy>)[NextCountry](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/location-settings/country>)
Last updated 1 year ago

```

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/proxy-generation.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#generate-endpoints-you-want-from-any-country-state-and-city>)
Generate endpoints you want from any country,state and city
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiGenerateCustomEndpoints`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#request-body>)
Request Body
Name
Type
Description
ChannelId*
Integer
Location*
String
Capitalized country alpha-2 code, continental random, or global random.[view all available countries](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/location-settings/country>).
StickySessionTime*
Integer
Session time in minutes, available values:`5，10，15，30，60，90.`
Fill in `**0**`, which means that a new IP is changed for each request
Count*
Integer
Count of routes that will be generated and returned. Min value is 1, max value depends on param page and specific location.
Domain*
String
Default is `Global`.
This means you can use it anywhere in the world, at the fastest speed.
State
String
View allavailable [states and cities](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/location-settings/state-and-city>).
City
String
View allavailable [states and cities](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/location-settings/state-and-city>).
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": [
    accessdomain:port:customer_US_032_0226_5_ss-SoPzCGvB:password"
  ]
}
```

[PreviousUpdate channel](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/update-channel>)[NextUpdate proxy](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/update-proxy>)
Last updated 8 months ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/update-channel.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#update-password-or-traffic-limit-of-specified-sub-channel>)
Update password or traffic limit of specified sub channel
`POST` `https://api.ipnux.com/V1/OpenApi/OpenApiEditChannel`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#request-body>)
Request Body
Name
Type
Description
ChannelId*
Integer
ChannelName
String
ChannelLimitTrafficGb
Integer
Enable
Boolean
true=open channel，false=close channel
ChannelPassword
String
Password must be 6 characters, a combination of numbers and letters.
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": true
}
```

[PreviousCreate channel](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/create-channel>)[NextProxy generation](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/proxy-generation>)
Last updated 1 year ago

````

.\脚本\crawl4ai\整理好的文档\ipnux-static-residential-docs-all\proxies\ipnux-residential/update-proxy.md

````markdown
##
[](https://help.ipnux.com/proxies/ipnux-residential/<#update-proxy>)
Update proxy
`GET` `https://api.ipnux.com/V1/OpenApi/OpenApiUpdateProxy`
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#query-parameters>)
Query Parameters
Name
Type
Description
username*
String
username
####
[](https://help.ipnux.com/proxies/ipnux-residential/<#headers>)
Headers
Name
Type
Description
UserId*
String
"Authentication" generated userId
Token*
String
"Authentication" generated token
Content-Type*
String
application/json
200: OK "Code":1000,"Message":"Successful"
[](https://help.ipnux.com/proxies/ipnux-residential/<#tab-id-200-ok-code-1000-message-successful>)
Copy```
{
  "Code": 1000,
  "Message": "Successful",
  "Data": true
}
```

[PreviousProxy generation](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/proxy-generation>)[NextLocation Settings](https://help.ipnux.com/proxies/ipnux-residential/</proxies/ipnux-residential/location-settings>)
Last updated 1 year ago

````
