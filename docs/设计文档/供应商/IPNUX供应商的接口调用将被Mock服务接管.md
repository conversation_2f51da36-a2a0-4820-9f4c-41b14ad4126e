
 📊 IPNUX供应商Mock实现全面分析

  🎯 总体情况

- 已实现Mock: 10个方法 ✅
- 未实现Mock: 3个方法 ❌
- 完成度: 77% (10/13)

  ✅ 已实现Mock的接口

  | API路径                           | 方法名                       | Shop模块 |
  System模块 | 功能描述     |
  |---------------------------------|---------------------------|--------|----------|---
  -------|
  | /OpenApiGetUseTraffic           | testConnection            | ✅      | ✅        |
  测试连接     |
  | /OpenApiPurchaseNewOrder   | purchaseStaticInstances   | ✅      | ✅        |
  购买静态代理   |
  | /OpenApiCreateChannel           | purchaseDynamicChannel    | ✅      | ✅        |
  创建动态通道   |
  | /OpenApiNoneRenew               | renewInstance             | ✅      | ✅        |
  续费实例     |
  | /OpenApiNodeList                | getInstanceDetails        | ✅      | ✅        |
  获取实例详情   |
  | /OpenApiSubUsers                | getInstanceDetails        | ✅      | ✅        |
  获取动态实例详情 |
  | /OpenApiSwitchNode              | replaceInstance           | ✅      | ✅        |
  替换实例     |
  | /OpenApiEditNodeUserAndPass     | updateInstanceCredentials | ✅      | ✅        |
  更新实例凭证   |
  | /OpenApiEditNodeIpWhiteList     | updateInstanceWhitelist   | ✅      | ✅        |
  更新IP白名单  |
  | /OpenApiEditChannel             | updateChannel             | ✅      | ✅        |
  更新通道设置   |
  | /OpenApiGetUseTraffic           | getChannelTraffic         | ✅      | ✅        |
  获取通道流量   |
  | /OpenApiGenerateCustomEndpoints | generateEndpoints         | ✅      | ✅        |
  生成端点     |

  ❌ 未实现Mock的接口 (关键缺失)

  | API路径                          | 方法名                   | 影响程度 | 功能描述
     |
  |--------------------------------|-----------------------|------|-----------|
  | /OpenApiGetNodeInventory       | getAvailableInventory | 🔴 高 | 获取商品信息和库存
  |
  | /OpenApiGetCurrentVersionPrice | calculatePrice        | 🔴 高 | 获取成本价格    |
  | /OpenApiNodeList               | getPurchasedIPList    | 🟡 中 | 获取已购买IP列表 |

  📁 Mock实现文件位置

  Shop模块Mock:
  server/src/module/shop/provider/implementations/mock/mock-ipnux.service.ts

  System模块Mock:
  server/src/module/system/provider/implementations/mock/mock-ipnux.service.ts

  🚨 关键影响分析

  缺失接口影响:

  1. 库存查询缺失 → 无法显示产品可用性
  2. 价格计算缺失 → 订单价格无法正确计算
  3. 已购IP列表缺失 → 用户无法查看已购买的代理

  建议立即补充这3个Mock方法以确保系统完整性。

### **IPNUX供应商接口Mock方案 (已调研更新)**

好的，这里为你整理一个最简单、入侵性最小的Mock功能方案，主要依赖环境变量来控制，并列出需要改动的文件清单。

**核心目标：** 在开发/测试时，当特定环境变量被设置，IPNUX供应商的接口调用将被Mock服务接管，返回预定义的或简单生成的假数据，以避免真实API调用产生费用。

**功能方案：**

1. **环境变量控制：**
    - 引入一个环境变量，例如 `MOCK_IPNUX_PROVIDER=true`。
    - 当此环境变量为 `true` 时，所有对IPNUX供应商的API调用将由Mock服务处理。
    - 当此环境变量为 `false` 或未设置时，系统将使用真实的IPNUX服务。

2. **Mock服务 (`MockIpnuxService`)：**
    - 创建一个 `MockIpnuxService` 类，它必须实现 `ProxyProvider` 接口 (定义于 `server/src/module/system/provider/interfaces/proxy-provider.interface.ts`)。
    - **`purchaseInstances` 方法：**
        - 接收开通参数 (数量、时长、位置等)。
        - 生成指定数量的伪造实例数据，例如 `providerInstanceId` 使用 `mock_inst_` 前缀以方便识别。
        - 记录日志表明正在使用Mock数据。
    - **`getInstanceDetails` 方法：**
        - 如果 `providerInstanceId` 以 `mock_inst_` 开头，则生成一个通用的Mock实例详情。
    - **`renewInstance` 方法：**
        - 简单计算新的过期时间并返回一个成功的模拟响应。
    - **`testConnection` 方法：**
        - 直接返回 `{ success: true, message: 'Mock connection successful.' }`。
    - **其他 `ProxyProvider` 接口方法：**
        - 对于暂时不需要详细Mock的接口，可以简单地返回一个表示成功的Promise（例如 `Promise.resolve(true)`），并记录一条警告日志。

3. **Provider工厂服务 (`ProviderFactoryService`) 修改：**
    - **调研确认**：`server/src/module/system/provider/services/provider-factory.service.ts` 中的 `getProvider` 方法是实现此逻辑的核心。它使用 `this.moduleRef.get('IpnuxService', ...)` 来获取服务实例，这种方式非常适合进行条件替换。
    - 在 `getProvider` 方法中，当 `providerEntity.providerCode` 为 "ipnux" 时：
        - 注入并使用 NestJS 的 `ConfigService` 来检查环境变量 `MOCK_IPNUX_PROVIDER`。
        - 如果为 `true`，则通过 `this.moduleRef.get(MockIpnuxService, ...)` 实例化并返回 `MockIpnuxService`。
        - 否则，保持现有逻辑，返回真实的 `IpnuxService`。
    - `initialize` 方法会被调用，无论是真实服务还是Mock服务，`configDetails` 都会被传递。

4. **模块注册：**
    - `MockIpnuxService` 需要在 `SystemProviderModule` (`server/src/module/system/provider/provider.module.ts`) 中被注册为一个Provider。

**优点：**

- **最小侵入：** 核心服务 (`IpnuxService`) 代码保持不变。
- **简单切换：** 通过一个环境变量即可切换真实/Mock模式。
- **明确的Mock标识：** Mock数据中包含"mock"前缀，易于识别。

**缺点：**

- **不够灵活：** Mock行为对所有IPNUX供应商实例是一致的。
- **Mock数据固定：** 返回的Mock数据模式比较固定。

**立即可实施的改进**

由于发现shop模块已有Mock基础，建议：

1. 先调研是否扩展现有Mock逻辑,还是全新实现
2. 复用BigIntJsonUtil等现有工具
3. 保持与订单履行流程的集成

---

### **改动文件清单 (已确认)**

1. **新增文件：**
    - `server/src/module/system/provider/implementations/mock/mock-ipnux.service.ts`
        - **说明**：Mock服务实现，必须 `implements ProxyProvider`。

2. **修改文件：**
    - `server/src/module/system/provider/services/provider-factory.service.ts`
        - **说明**：注入 `ConfigService` 和 `MockIpnuxService`。在 `getProvider` 方法中添加基于环境变量 `MOCK_IPNUX_PROVIDER` 的逻辑判断，以决定实例化 `IpnuxService` 还是 `MockIpnuxService`。
    - `server/src/module/system/provider/provider.module.ts`
        - **说明**：在 `providers` 数组中添加 `MockIpnuxService`。确保 `ConfigModule` 已被导入（通常在 `AppModule` 全局导入即可）。
    - *(条件修改)* `server/src/module/shop/provider/provider.module.ts` 和 `services/provider-factory.service.ts`
        - **说明**：`shop` 模块下也存在独立的工厂和模块。如果业务逻辑会调用到 `shop` 模块的工厂，则需要进行类似的修改。初步调研显示核心逻辑在 `system` 模块。

3. **配置文件 (新增环境变量)：**
    - `.env` 或 `.env.development`
        - 添加 `MOCK_IPNUX_PROVIDER=true`

### **不需要改动的文件 (已确认)**

- `server/src/module/system/provider/entities/provider.entity.ts` (Provider数据库实体)
- `server/src/module/system/provider/interfaces/proxy-provider.interface.ts` (接口定义本身)
- `admin-web/**` (Admin Panel前端页面)
- `server/src/module/system/provider/implementations/ipnux/ipnux.service.ts` (真实的IPNUX服务代码)
