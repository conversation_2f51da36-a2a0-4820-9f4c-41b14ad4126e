  批量重算价格 (handleRecalculateAllPrices)

- 操作范围: 对所有商品中使用自动定价的商品进行价格重算
- 触发方式: 手动点击按钮触发
- 功能: 重新计算价格，根据最新成本价、利润率、汇率等重新计算
- 影响商品: 所有自动定价商品
- 使用场景: 成本价批量调整、利润率策略变更、汇率波动时

  恢复自动定价 (handleBatchRevertToAutoPricing)

- 操作范围: 仅对选中的商品进行操作
- 前提条件: 只处理手动定价的商品，自动定价商品会被跳过
- 功能: 将手动定价模式改回自动定价模式
- 影响商品: 选中的手动定价商品
- 使用场景: 特殊活动结束、需要统一价格管理策略、手动调价后发现不合适

  核心区别

  | 功能   | 批量重算价格   | 恢复自动定价    |
  |------|----------|-----------|
  | 目标商品 | 全部自动定价商品 | 选中的手动定价商品 |
  | 操作类型 | 重新计算价格   | 改变定价模式    |
  | 选择范围 | 全局操作     | 批量选择操作    |
  | 前置条件 | 无        | 必须先选中商品   |
  | 处理对象 | 自动定价商品   | 手动定价商品    |

  简单说：

- 批量重算价格 = 让所有自动定价商品重新算一遍价格
- 恢复自动定价 = 把手动定价的商品改回自动定价模式
