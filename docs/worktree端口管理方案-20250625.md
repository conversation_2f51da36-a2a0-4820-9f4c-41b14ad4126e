# Worktree 端口管理方案

## 概述

本方案解决了使用 Git Worktree 时多个工作区端口冲突的问题。通过创建独立的配置文件（`.env.worktree-*`），每个 worktree 可以使用不同的端口配置，避免了分支合并时端口设置被覆盖的问题。

## 实施内容

### 1. 更新 .gitignore
添加了 worktree 特定配置文件的忽略规则：
```
.env.worktree-*
.env.local.worktree-*
*.worktree.env
```

### 2. 创建通用启动脚本
- 文件：`scripts/dev-worktree.js`
- 功能：加载 worktree 特定配置并启动开发服务器

### 3. 更新 package.json
在各项目中添加了 `dev:worktree` 脚本：
- server/package.json
- admin-web/package.json
- shop-web/package.json

### 4. 改进端口设置脚本
- 文件：`scripts/setup-worktree-ports.js`
- 功能：
  - 创建独立的 `.env.worktree-<id>` 配置文件
  - 自动检查并安装项目依赖（如果未安装）
  - 优先使用 pnpm，否则使用 npm

### 5. VSCode 集成
- 文件：`.vscode/tasks.json`
- 功能：支持在 VSCode 中使用多个终端标签启动服务

## 使用方法

### 初始设置（每个 worktree 只需一次）
```bash
# 在 worktree 根目录运行
node scripts/setup-worktree-ports.js <端口偏移量>

# 例如：
node scripts/setup-worktree-ports.js 100  # Server: 32180, Admin: 8988, Shop: 32170
node scripts/setup-worktree-ports.js 200  # Server: 32280, Admin: 9088, Shop: 32270
```

**自动依赖安装功能**：
脚本会自动为您处理依赖安装：
1. 检测可用的包管理器（优先使用 pnpm，否则使用 npm）
2. 检查每个项目（server、admin-web、shop-web）的 node_modules 目录
3. 如果依赖未安装，自动运行安装命令
4. 显示安装进度和结果

这意味着新创建的 worktree 可以一步完成配置和依赖安装！

### 日常开发

#### 方式1：单独启动各服务
```bash
# 启动 Server
cd server && npm run dev:worktree

# 启动 Admin
cd admin-web && npm run dev:worktree

# 启动 Shop
cd shop-web && npm run dev:worktree
```

#### 方式2：在 VSCode 中一键启动
1. 使用快捷键 `Ctrl+Shift+B`
2. 选择 "🚀 Start All Services (Worktree)"
3. VSCode 会自动打开 3 个终端标签，每个运行一个服务

#### 方式3：使用 VSCode 任务面板
1. 使用快捷键 `Ctrl+Shift+P`
2. 输入 "Tasks: Run Task"
3. 选择 "🚀 Start All Services (Worktree)"

### 验证配置
```bash
# 运行测试脚本
node scripts/test-worktree-config.js
```

## 配置文件结构

每个 worktree 会生成以下配置文件：
```
.env.worktree-<worktree-id>          # 根目录
server/.env.worktree-<worktree-id>   # Server 项目
admin-web/.env.worktree-<worktree-id> # Admin 项目
shop-web/.env.worktree-<worktree-id>  # Shop 项目
```

配置文件内容示例：
```env
# Server ports
SERVER_PORT=32180
APP_PORT=32180

# Admin web port
VITE_ADMIN_WEB_PORT=8988

# Shop web port  
VITE_SHOP_WEB_PORT=32170

# API URLs
VITE_API_BASE_URL=http://localhost:32180
VITE_API_URL=http://localhost:32180
```

## 优势

1. **零合并冲突**：worktree 配置不进入版本控制
2. **保留默认值**：.env.local 仍在版本控制中，新开发者可使用默认配置
3. **灵活性高**：每个 worktree 独立配置
4. **易于维护**：一次设置，永久解决
5. **向后兼容**：不影响现有的 `npm run dev` 命令

## 注意事项

1. `.env.worktree-*` 文件已被 gitignore，不会被提交
2. 每个 worktree 需要单独运行 setup 脚本设置端口
3. 生产环境不受影响，仍使用原有配置方式
4. 如果不使用 worktree 配置，可以继续使用 `npm run dev`