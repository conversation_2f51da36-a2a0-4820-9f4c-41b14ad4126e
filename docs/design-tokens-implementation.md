# 设计令牌系统实施记录

## 概述

为了解决 `DashboardLayout` 和 `PricingV2` 页面之间的设计不一致问题，我们实施了基于 Tailwind CSS `@apply` 的设计令牌系统。

## 背景问题

- `DashboardLayout` 侧边栏使用响应式字体系统：`text-sm sm:text-base lg:text-lg`
- `PricingV2` 页面的字体大小、间距与侧边栏不一致
- 缺乏统一的设计标准，导致在不同屏幕尺寸下体验不协调

## 解决方案

### 1. 设计令牌定义 (`src/index.css`)

#### Typography 令牌
```css
.text-h1          /* text-xl sm:text-2xl lg:text-3xl font-bold */
.text-h2          /* text-lg sm:text-xl lg:text-2xl font-semibold */
.text-nav-main    /* text-sm sm:text-base lg:text-lg */
.text-nav-sub     /* text-xs sm:text-sm lg:text-base */
.text-body        /* text-sm sm:text-base */
.text-caption     /* text-xs sm:text-sm */
```

#### Spacing 令牌
```css
.padding-container  /* px-4 sm:px-6 lg:px-8 */
.padding-card       /* p-3 sm:p-4 lg:p-6 */
.padding-control    /* px-3 sm:px-4 lg:px-5 py-2 sm:py-2.5 lg:py-3 */
```

#### Layout 令牌
```css
.gap-grid      /* gap-2 sm:gap-3 lg:gap-4 */
.gap-layout    /* gap-4 sm:gap-6 lg:gap-8 */
```

### 2. ESLint 强制使用

配置了 `eslint-plugin-tailwindcss` 的 `no-custom-classname` 规则，强制开发者使用设计令牌而不是原生工具类：

```javascript
"tailwindcss/no-custom-classname": ["error", {
  "whitelist": [
    "text-h1", "text-h2", "text-nav-main", "text-nav-sub", "text-body", "text-caption",
    "padding-container", "padding-card", "padding-control",
    "gap-grid", "gap-layout", "interactive-base"
  ]
}]
```

### 3. PricingV2 页面修改

将所有字体大小统一为设计令牌标准：
- 主标题：`text-nav-main`
- 子标题和按钮文字：`text-nav-sub`
- 容器间距：`padding-container`
- 卡片间距：`padding-card`
- 网格间距：`gap-grid`

## 使用指南

### 正确用法
```tsx
// ✅ 使用设计令牌
<h2 className="text-nav-main">选择地理位置</h2>
<div className="padding-container gap-layout">

// ✅ 结合其他 Tailwind 类
<button className="text-nav-sub bg-purple-600 hover:bg-purple-700">
```

### 错误用法（ESLint 会报错）
```tsx
// ❌ 直接使用工具类
<h2 className="text-sm sm:text-base lg:text-lg">标题</h2>

// ❌ 自定义组合
<div className="px-4 sm:px-6 lg:px-8">
```

## 安装依赖

由于当前环境 npm 安装遇到问题，需要手动安装：

```bash
npm install --save-dev eslint-plugin-tailwindcss
```

## 长期规划：迁移到 CVA

当前的 `@apply` 方案是阶段性解决方案。未来计划迁移到 `class-variance-authority` (cva) 组件系统：

```tsx
// 未来目标
<Typography variant="nav-main">选择地理位置</Typography>
<Typography variant="nav-sub">产品信息</Typography>
```

### 迁移优势
- 类型安全的 variant 系统
- IDE 自动补全支持
- 更好的组件封装
- 运行时错误检查

## 守护措施

1. **ESLint 强制执行**：防止开发者使用原生工具类
2. **命名约定**：使用清晰的 `.text-*`, `.padding-*`, `.gap-*` 前缀
3. **最小组合令牌**：避免过度组合，保持灵活性
4. **文档化**：详细记录每个令牌的用途和使用场景

## 效果验证

- ✅ TypeScript 编译通过
- ✅ 构建成功无错误
- ✅ 字体大小响应式一致性
- ✅ 间距和布局协调统一
- ✅ 可维护性显著提升

## 系统演进指南

### 何时创建新令牌？

**创建新令牌的标准**：
- 特定的工具类组合在 3 个或更多不同组件中重复出现
- 该组合具有清晰的语义意义
- 符合现有的命名约定（`type-name` 格式）

**示例**：
```css
/* ✅ 合理的新令牌 - 在多个表单组件中重复使用 */
.padding-form-input { @apply px-3 py-2 border border-gray-300 rounded-md; }

/* ❌ 不合理 - 过于具体，只在一个地方使用 */
.pricing-card-header { @apply text-lg font-bold text-purple-600 mb-4; }
```

### 新令牌添加流程

1. **提案阶段**：在 PR 中提出新令牌及其使用理由
2. **命名检查**：确保遵循 `type-name` 约定（如 `text-body`, `gap-grid`）
3. **代码实施**：
   - 在 `src/index.css` 中添加令牌定义
   - 在 `.eslintrc.cjs` 的白名单中添加新类名
   - 在相关组件中应用新令牌
4. **文档更新**：更新本文档中的令牌列表

### CVA 迁移触发条件

**明确的迁移信号**：当我们需要为原子级组件创建**样式变体（variants）**时，立即开始 CVA 迁移。

**触发示例**：
```tsx
// 🚨 触发信号：Button 需要多个变体
<Button variant="primary">主要按钮</Button>
<Button variant="secondary">次要按钮</Button>
<Button variant="destructive">危险按钮</Button>

// 此时应该创建 CVA 组件，而不是创建更多 CSS 令牌
// ❌ 避免: .btn-primary, .btn-secondary, .btn-destructive
// ✅ 推荐: CVA Button 组件与 variant prop
```

**迁移优先级**：
1. **Button 组件**：最常见的变体需求
2. **Typography 组件**：统一文本样式管理
3. **Card/Badge 组件**：状态和类型变体

## 令牌单一职责原则

每个令牌应该只负责一个设计方面：

- **Typography 令牌**：仅处理字体大小、粗细、行高
- **Spacing 令牌**：仅处理内外边距
- **Layout 令牌**：仅处理间距和布局
- **Behavioral 令牌**：仅处理交互行为（如 `.interactive-base`），包含可访问性焦点状态

**组合使用示例**：
```tsx
// ✅ 单一职责令牌的组合
<div className="text-nav-main padding-control interactive-base bg-purple-600">
  按钮内容
</div>
```

## 系统健康检查

### 季度设计系统健康检查清单

**令牌审计**：
- [ ] **扫描"魔法字符串"**：手动搜索代码库中常见的工具类字符串（如搜索 `py-2 px-4`），是否遗漏了令牌化机会？
- [ ] **审查令牌使用情况**：是否有令牌未被使用或仅使用一次？是否可以废弃？
- [ ] **检查样式覆盖**：搜索使用 `@apply` 覆盖现有令牌样式的情况，这可能表明需要令牌变体或新令牌

**ESLint 规则审查**：
- [ ] `tailwindcss/no-custom-classname` 白名单是否包含所有当前令牌？
- [ ] 是否有新的有用的 Tailwind ESLint 规则值得采用？

**CVA 迁移状态**：
- [ ] 最近是否构建了有变体的组件（如主要和次要按钮）但没有迁移到 `cva`？如有，需安排迁移
- [ ] 迁移优先级列表是否仍然相关？

**可访问性检查**：
- [ ] 所有交互元素是否正确使用了 `.interactive-base` 令牌？
- [ ] 键盘导航的焦点状态是否在所有组件中一致可见？

**性能影响评估**：
- [ ] 令牌的增加是否导致了不必要的 CSS 膨胀？
- [ ] 是否有令牌被过度拆分，影响了可读性？

## 下一步

1. 在其他页面组件中应用设计令牌
2. 建立定期的设计令牌审查机制
3. 监控重复样式模式，准备新令牌创建
4. 观察组件变体需求，准备 CVA 迁移时机