-- 直接SQL修复被错误同步的价格
-- 事故时间: 2025-06-27 01:30:00

-- 第1步: 查看受影响的产品（预览）
SELECT 
    p.id,
    p.product_name,
    p.price AS current_price,
    p.is_price_manual,
    p.sync_strategy,
    p.update_time,
    h.new_price AS last_manual_price,
    h.change_time AS last_manual_time
FROM shop_product p
LEFT JOIN (
    SELECT 
        product_id,
        new_price,
        change_time,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
WHERE 
    (p.is_price_manual = 1 OR p.sync_strategy IN ('MANUAL_OVERRIDE', 'DISABLED'))
    AND p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND h.new_price IS NOT NULL
    AND ABS(p.price - h.new_price) > 0.01;

-- 第2步: 执行修复（确认上面的查询结果正确后，执行这个）
-- 注意：执行前请先备份数据库！

/*
UPDATE shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
SET 
    p.price = h.new_price,
    p.update_time = NOW()
WHERE 
    (p.is_price_manual = 1 OR p.sync_strategy IN ('MANUAL_OVERRIDE', 'DISABLED'))
    AND p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND h.new_price IS NOT NULL
    AND ABS(p.price - h.new_price) > 0.01;

-- 第3步: 记录恢复操作到历史表
INSERT INTO shop_product_price_history 
(product_id, old_price, new_price, currency, change_type, change_reason, change_by, change_time)
SELECT 
    p.id,
    p.price,
    h.new_price,
    COALESCE(p.currency, 'CNY'),
    'manual',
    '系统恢复：修复2025-06-27 01:30同步任务事故',
    'system:manual_fix',
    NOW()
FROM shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
WHERE 
    (p.is_price_manual = 1 OR p.sync_strategy IN ('MANUAL_OVERRIDE', 'DISABLED'))
    AND p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND h.new_price IS NOT NULL
    AND ABS(p.price - h.new_price) > 0.01;
*/