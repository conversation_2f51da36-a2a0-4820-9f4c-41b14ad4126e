# 价格同步事故修复指南

## 事故概述

定时任务 `syncAllProductPrices` 错误地覆盖了所有商品价格，包括那些标记为手动定价的产品。该问题的根本原因是：

1. **缺少保护检查**：定时任务没有检查 `isPriceManual` 和 `syncStrategy` 字段
2. **缺少历史记录**：价格变更没有被记录到 `shop_product_price_history` 表中
3. **权限绕过**：没有在方法级别强制执行业务规则

## 已实施的修复

### 1. 安全防护机制

**文件**: `server/src/module/system/products/services/product-sync.service.ts`

#### 添加的保护措施

- **强制性上下文参数**：所有价格同步操作必须提供 `SyncContext`（包含 syncId 和 source）
- **权威性检查**：在 `syncProductPrice` 方法开头添加强制检查：

  ```typescript
  if (product.isPriceManual ||
      product.syncStrategy === SyncStrategy.MANUAL_OVERRIDE ||
      product.syncStrategy === SyncStrategy.DISABLED) {
    // 跳过同步，记录日志
    return;
  }
  ```

- **数据库级过滤**：定时任务在查询时就排除不应同步的产品
- **强制历史记录**：所有价格变更都会被记录，不再依赖可选参数

### 2. 改进的定时任务

定时任务现在：

- 生成唯一的 `syncId` 用于追踪
- 在数据库级别过滤产品（只选择 `FULL_SYNC` 和 `STOCK_AND_COST_ONLY` 策略）
- 排除 `isPriceManual = true` 的产品
- 为每个操作提供完整的审计上下文

## 数据恢复

### 恢复脚本

**文件**: `server/src/commands/restore-prices.command.ts`

### 使用方法

1. **Dry Run 模式（安全预览）**：

   ```bash
   npm run command db:restore-manual-prices --incident-time="2024-06-27T10:00:00Z"
   ```

2. **实际恢复**：

   ```bash
   npm run command db:restore-manual-prices --incident-time="2024-06-27T10:00:00Z" --dry-run=false
   ```

### 恢复逻辑

1. 识别受影响的产品（`isPriceManual = true` 或 `syncStrategy = MANUAL_OVERRIDE/DISABLED`）
2. **智能跳过**：检查事故后是否已有手动修复，避免覆盖正确的价格
3. 为每个产品查找事故前最后一次手动价格记录
4. **事务安全**：使用数据库事务确保价格更新和历史记录的原子性
5. 将当前价格恢复到历史记录中的价格
6. 在价格历史表中记录恢复操作

### 安全特性

- **事务保护**：每个产品的恢复操作都在独立事务中，失败时自动回滚
- **边缘情况处理**：自动检测事故后的手动修复，避免覆盖
- **Dry Run优先**：默认为预览模式，确保安全
- **完整审计**：所有恢复操作都会在价格历史表中留下记录

## 执行步骤

### 第一步：立即措施

- [x] 已从系统层面禁用定时任务
- [x] 已实施代码修复

### 第二步：部署修复

```bash
cd server
npm run build
npm run start:prod  # 或重启生产服务
```

### 第三步：数据恢复

```bash
# 1. 先运行 dry-run 查看影响范围
npm run command db:restore-manual-prices --incident-time="事故发生时间"

# 2. 确认无误后执行实际恢复
npm run command db:restore-manual-prices --incident-time="事故发生时间" --dry-run=false
```

### 第四步：重新启用定时任务

确认数据恢复成功后，从系统层面重新启用定时任务。

## 验证步骤

1. **检查修复效果**：

   ```sql
   -- 查看手动价格产品的同步策略
   SELECT id, product_name, is_price_manual, sync_strategy, price
   FROM shop_product
   WHERE is_price_manual = 1;
   ```

2. **验证历史记录**：

   ```sql
   -- 查看最近的价格变更记录
   SELECT * FROM shop_product_price_history
   WHERE change_time > '事故时间'
   ORDER BY change_time DESC;
   ```

3. **测试定时任务**：重新启用后观察日志，确保只同步了预期的产品

## 长期改进建议

1. **统一字段语义**：考虑弃用 `isPriceManual`，统一使用 `syncStrategy`
2. **添加监控告警**：当定时任务更新超过预期数量的产品时发送告警
3. **增加单元测试**：覆盖价格同步的各种场景
4. **数据库触发器**：考虑添加数据库级别的价格变更记录触发器

## 紧急联系

如果恢复过程中遇到问题：

1. 立即停止所有操作
2. 检查数据库备份状态
3. 联系技术负责人

---

**重要提醒**：始终先在测试环境验证修复和恢复流程！
