-- 精确修复2025-06-27 01:30事故中被错误同步的价格
-- 基于实际数据分析的修复方案

-- 第1步: 预览将要修复的产品价格对比（执行这个确认数据正确）
SELECT 
    p.id,
    p.product_name,
    p.price AS 当前错误价格,
    h.new_price AS 应该恢复到的价格,
    h.change_by AS 最后手动修改人,
    h.change_time AS 最后手动修改时间,
    ROUND(p.price - h.new_price, 2) AS 价格差异
FROM shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        change_by,
        change_time,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
WHERE 
    p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND p.is_price_manual = 1
    AND ABS(p.price - h.new_price) > 0.01
ORDER BY p.id;

-- 第2步: 查看具体的修复计划
SELECT 
    CONCAT('UPDATE shop_product SET price = ', h.new_price, ' WHERE id = ', p.id, '; -- ', p.product_name, ': ', p.price, ' -> ', h.new_price) AS 修复SQL
FROM shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
WHERE 
    p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND p.is_price_manual = 1
    AND ABS(p.price - h.new_price) > 0.01
ORDER BY p.id;

-- 第3步: 实际执行修复（确认第1、2步结果正确后取消注释执行）
/*
-- 开始事务
START TRANSACTION;

-- 执行价格修复
UPDATE shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
SET 
    p.price = h.new_price,
    p.update_time = NOW()
WHERE 
    p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND p.is_price_manual = 1
    AND ABS(p.price - h.new_price) > 0.01;

-- 记录修复操作到历史表
INSERT INTO shop_product_price_history 
(product_id, old_price, new_price, currency, change_type, change_reason, change_by, change_time)
SELECT 
    p.id,
    p.price,
    h.new_price,
    COALESCE(p.currency, 'CNY'),
    'manual',
    '系统恢复：修复2025-06-27 01:30同步任务事故影响',
    'system:sql_fix',
    NOW()
FROM shop_product p
JOIN (
    SELECT 
        product_id,
        new_price,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY change_time DESC) as rn
    FROM shop_product_price_history 
    WHERE change_type = 'manual' 
        AND change_time < '2025-06-27 01:30:00'
) h ON p.id = h.product_id AND h.rn = 1
WHERE 
    p.update_time >= '2025-06-27 01:30:00' 
    AND p.update_time <= '2025-06-27 01:40:00'
    AND p.is_price_manual = 1
    AND ABS(p.price - h.new_price) > 0.01;

-- 查看修复结果
SELECT 
    CONCAT('修复了产品 ', id, ': ', product_name, ', 价格: ', price) AS 修复结果
FROM shop_product 
WHERE update_time >= NOW() - INTERVAL 1 MINUTE;

-- 确认无误后提交事务
COMMIT;
-- 如有问题可以回滚: ROLLBACK;
*/