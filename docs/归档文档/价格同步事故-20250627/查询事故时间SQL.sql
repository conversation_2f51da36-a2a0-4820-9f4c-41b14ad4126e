-- 查询同步产品价格任务的最近执行记录
-- 用于确定事故发生的具体时间

-- 1. 首先确认任务配置
SELECT 
    job_id,
    job_name,
    invoke_target,
    cron_expression,
    status,
    create_time
FROM sys_job 
WHERE invoke_target = 'task.syncAllProductPrices()'
   OR job_name LIKE '%同步产品价格%'
   OR job_name LIKE '%sync%price%';

-- 2. 查询最近20次的执行记录
SELECT
    l.job_log_id,
    l.create_time AS execution_start_time,
    l.status AS execution_status, -- '0'=成功, '1'=失败
    l.job_message,
    l.exception_info,
    j.job_name
FROM
    sys_job_log l
JOIN
    sys_job j ON l.job_id = j.job_id
WHERE
    j.invoke_target = 'task.syncAllProductPrices()'
ORDER BY
    l.create_time DESC
LIMIT 20;

-- 3. 分析执行模式 - 按日期分组查看执行频率
SELECT
    DATE(l.create_time) AS execution_date,
    TIME(l.create_time) AS execution_time,
    l.status AS execution_status,
    l.job_message,
    CASE 
        WHEN l.status = '0' THEN '成功'
        WHEN l.status = '1' THEN '失败'
        ELSE '未知'
    END AS status_text
FROM
    sys_job_log l
JOIN
    sys_job j ON l.job_id = j.job_id
WHERE
    j.invoke_target = 'task.syncAllProductPrices()'
    AND l.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) -- 最近7天
ORDER BY
    l.create_time DESC;

-- 4. 查找可能的异常执行（执行失败或耗时异常）
SELECT
    l.create_time AS execution_start_time,
    l.status,
    l.job_message,
    l.exception_info,
    CASE 
        WHEN l.job_message LIKE '%ms%' THEN 
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(l.job_message, '耗时 ', -1), 'ms', 1) AS UNSIGNED)
        ELSE NULL
    END AS duration_ms
FROM
    sys_job_log l
JOIN
    sys_job j ON l.job_id = j.job_id
WHERE
    j.invoke_target = 'task.syncAllProductPrices()'
    AND l.create_time >= DATE_SUB(NOW(), INTERVAL 3 DAY) -- 最近3天
    AND (
        l.status = '1' -- 失败的执行
        OR l.exception_info IS NOT NULL AND l.exception_info != '' -- 有异常信息
    )
ORDER BY
    l.create_time DESC;