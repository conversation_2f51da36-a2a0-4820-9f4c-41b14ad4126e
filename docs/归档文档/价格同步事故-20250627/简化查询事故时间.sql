-- 简化版查询 - 直接查看任务日志
-- 避免复杂的表关联，先找到相关记录

-- 1. 查看所有任务日志，寻找价格同步相关的记录
SELECT 
    job_log_id,
    job_name,
    job_group,
    invoke_target,
    create_time,
    status,
    job_message,
    exception_info
FROM sys_job_log 
WHERE 
    job_name LIKE '%价格%' 
    OR job_name LIKE '%sync%price%'
    OR job_name LIKE '%同步产品%'
    OR invoke_target LIKE '%syncAllProductPrices%'
ORDER BY create_time DESC
LIMIT 20;

-- 2. 如果上面没找到，查看最近的所有任务执行记录
SELECT 
    job_log_id,
    job_name,
    job_group, 
    invoke_target,
    create_time,
    status,
    job_message
FROM sys_job_log 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 3 DAY)
ORDER BY create_time DESC
LIMIT 50;

-- 3. 查看 sys_job 表中的任务配置
SELECT 
    job_id,
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    status,
    create_time
FROM sys_job
WHERE 
    job_name LIKE '%价格%'
    OR job_name LIKE '%sync%'
    OR job_name LIKE '%同步%'
    OR invoke_target LIKE '%syncAllProductPrices%';