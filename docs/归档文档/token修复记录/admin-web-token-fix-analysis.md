# Admin-Web Token过期处理问题分析报告

## 问题现象
用户反馈当token过期时（后端返回code: 10001），前端没有自动跳转到登录页面。

## 根本原因分析

### 1. 错误码使用不一致

**后端实际行为：**
- JWT认证失败时，`auth.strategy.ts`第38行抛出：`UnauthorizedException('登录已过期，请重新登录')`
- 经过异常过滤器处理，被映射为 `ErrorCode.UNAUTHORIZED` (401)

**前端期待行为：**
- 前端 `request.js` 中有处理 code === 10001 的逻辑
- 但后端实际返回的是 401，不是 10001

**错误码定义混乱：**
- 后端 `ErrorCode.VALIDATION_ERROR = 10001` （参数验证错误）
- 后端 `ErrorCode.TOKEN_EXPIRED = 10006` （Token过期）
- 后端 `ErrorCode.UNAUTHORIZED = 401` （未授权）

### 2. 前端10001处理逻辑问题

即使后端返回10001，前端的处理逻辑也有问题：

```javascript
// request.js 第82-83行
if (msg && (msg.includes('登录') || msg.includes('会话') || msg.includes('授权'))) {
    // 显示重新登录对话框
} else {
    // 只显示错误提示，不跳转登录页
}
```

这种基于消息内容的判断不可靠，容易出现漏判。

## 解决方案

### 方案一：修复前端逻辑（推荐）

保持后端不变，修复前端的错误处理逻辑：

1. **删除10001的错误处理**（因为后端实际不会返回这个码用于token过期）
2. **确保401处理逻辑正确**（已经存在且正确）
3. **如果需要处理10006**，添加对应逻辑

### 方案二：统一后端错误码

修改后端使用专门的token过期错误码：

1. 修改 `auth.strategy.ts` 抛出 `BusinessException(ErrorCode.TOKEN_EXPIRED)`
2. 前端添加对10006的处理

## 当前的正确处理逻辑

前端 `request.js` 第109-125行对401的处理是正确的：

```javascript
if (code === 401) {
  if (!isRelogin.show) {
    isRelogin.show = true
    ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', { 
      confirmButtonText: '重新登录', 
      cancelButtonText: '取消', 
      type: 'warning' 
    })
    .then(() => {
      isRelogin.show = false
      useUserStore().logOut().then(() => {
        location.href = '/index'
      })
    })
    .catch(() => {
      isRelogin.show = false
    })
  }
  return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
}
```

## 推荐的立即修复方案

删除前端 `request.js` 中第81-108行的10001处理逻辑，因为：

1. 后端实际不会为token过期返回10001
2. 10001的处理逻辑有缺陷（基于消息内容判断）
3. 401的处理逻辑已经完整且正确

## 验证方法

1. 让token过期（等待或手动清除Redis中的token）
2. 发起需要认证的请求
3. 检查前端是否正确显示重新登录对话框并跳转

## 涉及的文件

- `/admin-web/src/utils/request.js` - HTTP拦截器和错误处理
- `/server/src/module/system/auth/auth.strategy.ts` - JWT认证策略
- `/server/src/common/filters/http-exceptions-filter.ts` - 异常过滤器
- `/server/src/common/constant/error-code.constant.ts` - 错误码定义