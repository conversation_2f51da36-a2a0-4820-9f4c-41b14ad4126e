# Token过期处理修复验证方案

## 修复内容

已删除前端 `src/utils/request.js` 中对错误码 10001 的错误处理逻辑，因为：

1. **后端实际行为**：JWT token过期时，后端返回的是401错误码，而不是10001
2. **10001的实际用途**：在后端用于参数验证错误（VALIDATION_ERROR），不是token过期
3. **401处理逻辑**：前端已有的401处理逻辑是正确和完整的

## 修复后的处理流程

1. **Token过期时**：
   - 后端 `auth.strategy.ts` 检测到Redis中无对应用户信息
   - 抛出 `UnauthorizedException('登录已过期，请重新登录')`
   - 异常过滤器将其映射为401错误码

2. **前端接收401错误**：
   - 响应拦截器检测到 `code === 401`
   - 显示重新登录确认对话框
   - 用户确认后调用 `useUserStore().logOut()`
   - 清除token并跳转到 `/index` 页面

## 验证方法

### 方法一：手动清除Redis Token

1. 登录admin-web管理后台
2. 使用Redis客户端或管理工具找到对应的token key
3. 删除该key：`DEL login_tokens:{uuid}`
4. 在前端发起任何需要认证的请求（如刷新页面、点击菜单等）
5. 应该看到重新登录确认对话框

### 方法二：等待Token自然过期

1. 登录admin-web管理后台
2. 等待token过期（根据JWT配置的过期时间）
3. 发起任何需要认证的请求
4. 应该看到重新登录确认对话框

### 方法三：模拟401响应（开发测试）

在浏览器开发者工具中：

```javascript
// 模拟401响应来测试前端处理逻辑
const testResponse = {
  data: {
    code: 401,
    message: '登录状态已过期'
  }
};

// 这应该触发重新登录流程
```

## 预期结果

✅ **成功场景**：
- 显示确认对话框："登录状态已过期，您可以继续留在该页面，或者重新登录"
- 用户点击"重新登录"后跳转到登录页面
- 用户状态被正确清除（token、roles、permissions等）

❌ **失败场景**：
- 只显示错误提示，没有重新登录对话框
- 页面停留在当前位置，没有跳转

## 相关文件

- ✅ `admin-web/src/utils/request.js` - 已修复
- ✅ `admin-web/src/store/modules/user.js` - logOut方法正常
- ✅ `admin-web/src/permission.js` - 路由守卫正常
- ✅ `admin-web/src/utils/auth.js` - token管理正常

## 如果问题仍然存在

如果修复后问题依然存在，可能的原因：

1. **后端返回的不是401**：检查实际的响应内容
2. **前端缓存问题**：清除浏览器缓存，重新加载页面
3. **其他拦截器干扰**：检查是否有其他HTTP拦截器处理了错误

使用浏览器开发者工具的Network面板查看实际的响应内容来确定问题。