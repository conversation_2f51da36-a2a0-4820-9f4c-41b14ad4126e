  阶段二：清理地理位置冗余（HIGH）

  -- 1. 确定标准：使用 standard_location_city_id 作为权威引用
  -- 2. 删除冗余字段
  ALTER TABLE `shop_product`
    DROP COLUMN `region`,
    DROP COLUMN `country`,
    DROP COLUMN `city`,
    DROP COLUMN `location_city_id`;

  -- 3. 重命名标准字段
  ALTER TABLE `shop_product`
    CHANGE `standard_location_city_id` `location_city_id` INT,
    CHANGE `standard_city_name` `city_name` VARCHAR(100),
    CHANGE `standard_country_name` `country_name` VARCHAR(100),
    CHANGE `standard_country_code` `country_code` VARCHAR(10),
    CHANGE `standard_region_name` `region_name` VARCHAR(100);

---

好的，根据我们讨论的商品同步功能优化方案，以下是预估需要修改的文件清单（相对路径）：

**后端 (NestJS - `server/` 目录):**

1. **数据库实体定义 (Entities):**
    * `server/src/module/shop/products/entities/product.entity.ts`
        * 需要添加 `sync_strategy` 字段。
        * （可选）添加 `supplier_data_snapshot` 字段。

2. **服务层 (Services):**
    * `server/src/module/system/products/services/product-sync.service.ts`
        * 这是核心修改文件，需要实现新的同步逻辑，根据 `sync_strategy` 和 `is_price_manual` 来决定更新哪些字段。
        * 修改 `createOrUpdateProductFromInventory` (或类似的方法) 的逻辑。
    * `server/src/module/system/products/services/products.service.ts` (管理端的产品服务)
        * 可能需要调整创建和更新产品的DTO和逻辑，以支持新的 `sync_strategy` 字段。
    * `server/src/module/shop/products/services/products.service.ts` (店铺端的产品服务)
        * 如果店铺端也允许修改产品信息（虽然可能性不大），也需要考虑。

3. **数据传输对象 (DTOs):**
    * `server/src/module/system/products/dto/create-product.dto.ts`
    * `server/src/module/system/products/dto/update-product.dto.ts`
    * `server/src/module/system/products/dto/product-management.dto.ts` (如果其中有涉及产品全量更新的DTO)
        * 需要加入 `sync_strategy` 字段。

4. **控制器 (Controllers):**
    * `server/src/module/system/products/products.controller.ts`
        * 如果新增了API来修改 `sync_strategy`，或者批量操作涉及到该字段，则需要修改。

5. **（可能）订单履行服务:**
    * `server/src/module/shop/proxy/services/order-fulfillment.service.ts`
        * 确认在开通或续费实例时，是从 `product.product_config_details` 中正确读取 `purposeWeb` 等技术规格。

**前端 (Vue - `admin-web/` 目录):**

1. **产品编辑页面:**
    * `admin-web/src/views/shop/product/edit.vue`
        * 表单中增加 “同步策略” (`sync_strategy`) 的选择器。
        * （可选）当运营人员修改受保护字段时，给出提示或交互逻辑。

2. **产品管理列表页面:**
    * `admin-web/src/views/shop/product/management.vue`
        * 表格中增加 “同步策略” 列的显示。
        * 实现批量修改产品 “同步策略” 的功能。
        * （可选）提供“强制从供应商同步”的按钮及对应逻辑。

3. **API 定义文件:**
    * `admin-web/src/api/shop/product.js`
        * 如果后端控制器新增或修改了API（例如更新 `sync_strategy`），这里需要同步更新。

4. **（可能）字典文件或常量文件:**
    * 如果 `sync_strategy` 的可选值比较固定，可能会在前端定义成常量或字典。
        * 例如 `admin-web/src/utils/dict.js` 或新建一个常量文件。

这个清单是基于当前讨论的方案预估的。在实际开发过程中，根据具体实现细节，可能还会涉及到其他相关文件，例如测试文件、数据库迁移脚本等。

请注意，`product_config_details` 的处理主要集中在 `ProductSyncService` 中，确保在不同同步策略下正确地合并或保留其中的技术规格字段。
