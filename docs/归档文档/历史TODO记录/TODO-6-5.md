  阶段二：清理地理位置冗余（HIGH）

  -- 1. 确定标准：使用 standard_location_city_id 作为权威引用
  -- 2. 删除冗余字段
  ALTER TABLE `shop_product`
    DROP COLUMN `region`,
    DROP COLUMN `country`,
    DROP COLUMN `city`,
    DROP COLUMN `location_city_id`;

  -- 3. 重命名标准字段
  ALTER TABLE `shop_product`
    CHANGE `standard_location_city_id` `location_city_id` INT,
    CHANGE `standard_city_name` `city_name` VARCHAR(100),
    CHANGE `standard_country_name` `country_name` VARCHAR(100),
    CHANGE `standard_country_code` `country_code` VARCHAR(10),
    CHANGE `standard_region_name` `region_name` VARCHAR(100);