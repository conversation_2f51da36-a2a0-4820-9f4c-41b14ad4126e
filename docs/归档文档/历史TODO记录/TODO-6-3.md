我检查了您提供的数据和截图，确实发现了价格显示的问题。让我分析一下：

## 问题分析

1. 数据库中的产品信息：
   - 成本价：2.00 USD
   - 销售价：43.14 CNY
   - 折扣价：1.00 CNY
   - 加价百分比配置：2.0（表示200%加价）

2. 价格计算逻辑应该是：
   - 成本价 2.00 USD
   - 加价200%后应为 6.00 USD
   - 按汇率（约7.19）转换为人民币应为约 43.14 CNY
   - 折扣价设为 1.00 CNY

3. 前端显示问题：
   - 显示折扣价 ¥1.00 是正确的
   - 显示原价 ¥43.14 也是正确的
   - 显示节省98%折扣是正确的计算：(43.14-1.00)/43.14 ≈ 0.98 = 98%

## 问题所在

从您提供的截图来看，前端显示的价格信息基本是正确的。主要问题可能在于：

1. 订单详情页面显示单价为 $0.002 (¥0.01)，这与产品的折扣价 ¥1.00 不符
2. 可能是测试订单使用了特殊价格，或者是订单创建时的价格计算逻辑有问题

## 修复建议

让我检查一下前端代码中的价格显示逻辑：

````tsx path=shop-web\src\pages\products\static-residential-proxies\PricingV2.tsx mode=EXCERPT
// 价格显示组件
const PriceDisplay = ({ product }: { product: shopProduct }) => {
  const originalPrice = parseFloat(product.price);
  const discountPrice = product.discountPrice ? parseFloat(product.discountPrice) : null;

  // 计算折扣百分比
  const discountPercentage = discountPrice
    ? Math.round(((originalPrice - discountPrice) / originalPrice) * 100)
    : 0;

  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-2">
        <span className="text-2xl font-bold text-primary">
          ¥{discountPrice?.toFixed(2) || originalPrice.toFixed(2)}
        </span>
        {discountPrice && discountPrice < originalPrice && (
          <Badge className="bg-destructive hover:bg-destructive">-{discountPercentage}%</Badge>
        )}
      </div>
      {discountPrice && discountPrice < originalPrice && (
        <span className="text-sm text-muted-foreground line-through">
          原价: ¥{originalPrice.toFixed(2)}
        </span>
      )}
    </div>
  );
};
````

````tsx path=shop-web\src\components\products\Cart-v2.tsx mode=EXCERPT
// 购物车价格计算
const CartSummary = ({ cart }: { cart: CartItem[] }) => {
  const totalPrice = cart.reduce((sum, item) => {
    const price = item.product.discountPrice
      ? parseFloat(item.product.discountPrice)
      : parseFloat(item.product.price);
    return sum + price * item.quantity;
  }, 0);

  const originalTotal = cart.reduce((sum, item) => {
    return sum + parseFloat(item.product.price) * item.quantity;
  }, 0);

  const savings = originalTotal - totalPrice;
  const savingsPercentage = originalTotal > 0
    ? Math.round((savings / originalTotal) * 100)
    : 0;

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <span>总计</span>
        <span className="font-bold">¥{totalPrice.toFixed(2)}</span>
      </div>
      {savings > 0 && (
        <div className="flex justify-between text-sm text-green-600">
          <span>节省 ({savingsPercentage}% 折扣)</span>
          <span>¥{savings.toFixed(2)}</span>
        </div>
      )}
      <div className="text-xs text-muted-foreground text-right">
        价格已包含税费 · 货币: CNY
      </div>
    </div>
  );
};
````

````tsx path=shop-web\src\pages\orders\OrderDetail.tsx mode=EXCERPT
// 订单详情页面价格显示
const PriceInfo = ({ order }: { order: OrderDetailDto }) => {
  return (
    <div className="space-y-2">
      <div className="flex justify-between">
        <span>单价</span>
        <span>
          ${(order.unitPrice || 0).toFixed(3)} (¥{(order.unitPriceCNY || 0).toFixed(2)})
        </span>
      </div>
      {order.exchangeRate && (
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>汇率</span>
          <span>{order.exchangeRate}</span>
        </div>
      )}
    </div>
  );
};
````

根据代码分析，前端价格显示逻辑基本正确。问题可能出在订单创建时的价格计算上。建议修改以下内容：

1. 检查订单服务中的价格计算逻辑，确保使用正确的折扣价：

````typescript path=server\src\module\shop\orders\orders.service.ts mode=EDIT
// 在创建订单时，确保使用正确的产品价格
const orderPrice = product.discountPrice
  ? parseFloat(product.discountPrice.toString())
  : parseFloat(product.price.toString());

// 确保单价计算正确
const unitPrice = orderPrice / quantity;
````

2. 确保订单详情页面正确显示价格：

````tsx path=shop-web\src\pages\orders\OrderDetail.tsx mode=EDIT
// 修改订单详情页面价格显示
const PriceInfo = ({ order }: { order: OrderDetailDto }) => {
  return (
    <div className="space-y-2">
      <div className="flex justify-between">
        <span>单价</span>
        <span>
          {order.currency === 'USD'
            ? `$${(order.unitPrice || 0).toFixed(2)} (¥${(order.unitPriceCNY || 0).toFixed(2)})`
            : `¥${(order.unitPrice || 0).toFixed(2)}`
          }
        </span>
      </div>
      {order.exchangeRate && (
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>汇率</span>
          <span>{order.exchangeRate}</span>
        </div>
      )}
    </div>
  );
};
````

总结：

1. 产品价格显示基本正确（原价43.14元，折扣价1.00元，折扣98%）
2. 订单详情页面显示的单价有问题（显示$0.002而非¥1.00）
3. 建议检查订单创建时的价格计算逻辑，确保使用正确的折扣价格

您需要我进一步检查订单服务中的价格计算逻辑吗？
