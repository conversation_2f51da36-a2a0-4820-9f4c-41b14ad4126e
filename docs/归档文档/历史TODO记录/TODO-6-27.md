我的定时问题有设计问题,现在把我的商品价格全部被修改了,

task.syncAllProductPrices()

我有一个/price-history的接口,里面有上一次用户修改的价格,我想把所有价格都设置回去, 为我调查
curl ^"<https://prod-api.gomaskip.com/api/system/products/55/price-history?page=1^&limit=20^>" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: zh-CN,zh;q=0.9,en;q=0.8^" ^
  -H ^"authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1dWlkIjoiY2U4ZTNkNDAzNWNiNDI1MDliZTc5Zjc3YjIwYmFmMDUiLCJ1c2VySWQiOjEwMCwiaWF0IjoxNzUwODM2MDE5fQ.kZECYab_vW2zkdtgqM60PQZK-SCUWWeLOua9YgiorHk^" ^
  -H ^"cache-control: no-cache^" ^
  -H ^"origin: <https://admin.gomaskip.com^>" ^
  -H ^"pragma: no-cache^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: <https://admin.gomaskip.com/^>" ^
  -H ^"sec-ch-ua: ^\^"Google Chrome^\^";v=^\^"137^\^", ^\^"Chromium^\^";v=^\^"137^\^", ^\^"Not/A)Brand^\^";v=^\^"24^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?0^" ^
  -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-site^" ^
  -H ^"user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36^"

MYSQL_HOST=**************
MYSQL_PORT=37443
MYSQL_USER=root
MYSQL_PASSWORD=UPWEOgIvx9vL9QLvzmJL
MYSQL_DATABASE=proxy_shop

CREATE TABLE `shop_product_price_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `old_price` decimal(10,2) NOT NULL,
  `new_price` decimal(10,2) NOT NULL,
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'CNY',
  `change_type` enum('manual','auto','sync') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'manual',
  `change_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `change_percentage` decimal(8,2) DEFAULT NULL,
  `change_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `change_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`product_id`) USING BTREE,
  KEY `idx_change_time` (`change_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=321 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品价格变更历史表';

CREATE TABLE `shop_product` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品编号/SKU',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
  `product_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '产品描述',
  `product_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品类型 ''1'': 海外静态住宅 ''2'': 海外动态代理 ''3'': 海外静态数据中心',
  `price` decimal(10,2) NOT NULL COMMENT '产品价格',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣价格',
  `flow_amount` int NOT NULL COMMENT '流量大小，单位MB，-1表示无限流量',
  `validity_period` int NOT NULL COMMENT '有效期(天)',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品图片URL',
  `sales_count` int DEFAULT '0' COMMENT '销售数量',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `provider_id` int DEFAULT NULL COMMENT '关联到 shop_provider 表的 provider_id',
  `external_product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商处的产品标识/SKU - 新格式: ${providerId}-${city_id}-${proxyType}-${proxiesFormat}-${purposeWeb}',
  `product_config_details` json DEFAULT NULL COMMENT '产品详细配置 - 包含: inventoryId, proxyType, proxiesFormat, purposeWeb, supplierLocation 等',
  `product_proxy_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'STATIC' COMMENT '产品代理分类',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（0正常 1下架）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标志',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `cost_price` decimal(10,2) NOT NULL COMMENT '成本价',
  `cost_price_currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'CNY' COMMENT '成本价币种',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'CNY' COMMENT '产品货币',
  `is_price_manual` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否手动设置价格',
  `sync_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'manual' COMMENT '同步状态：auto/manual/syncing/error',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `min_quantity` int DEFAULT '1' COMMENT '最小购买数量',
  `max_quantity` int DEFAULT NULL COMMENT '最大购买数量',
  `inventory_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'available' COMMENT '库存状态：available/low/out_of_stock',
  `available_count` int DEFAULT NULL COMMENT '可用数量',
  `location_city_id` int DEFAULT NULL COMMENT '标准化城市ID，关联 sys_location_city.id',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区名称',
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国家名称',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市名称',
  `country_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国家代码',
  `ip_segments` json DEFAULT NULL COMMENT 'IP段信息（JSON格式，包含IP段列表和统计）',
  `last_sync_id` int DEFAULT NULL COMMENT '最后同步ID',
  `config_proxy_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`product_config_details`,_utf8mb4'$.proxyType'))) VIRTUAL,
  `config_proxies_format` int GENERATED ALWAYS AS (json_unquote(json_extract(`product_config_details`,_utf8mb4'$.proxiesFormat'))) VIRTUAL,
  `config_purpose_web` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`product_config_details`,_utf8mb4'$.purposeWeb'))) VIRTUAL,
  `sync_strategy` enum('FULL_SYNC','STOCK_AND_COST_ONLY','MANUAL_OVERRIDE','DISABLED') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'FULL_SYNC' COMMENT '同步策略: FULL_SYNC-全量同步, STOCK_AND_COST_ONLY-仅同步库存成本, MANUAL_OVERRIDE-手动控制, DISABLED-禁用同步',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_product_code` (`product_code`) USING BTREE,
  KEY `idx_product_code` (`product_code`) USING BTREE,
  KEY `idx_sync_status` (`sync_status`) USING BTREE,
  KEY `idx_inventory_status` (`inventory_status`) USING BTREE,
  KEY `idx_location_city_id` (`location_city_id`) USING BTREE,
  KEY `idx_is_price_manual` (`is_price_manual`) USING BTREE,
  KEY `idx_last_sync_id` (`last_sync_id`) USING BTREE,
  KEY `idx_provider_external_product` (`provider_id`,`external_product_id`) USING BTREE,
  KEY `idx_external_product_id_prefix` (`external_product_id`(50)) USING BTREE,
  KEY `idx_product_proxy_category` (`product_proxy_category`) USING BTREE,
  KEY `idx_sync_strategy` (`sync_strategy`) USING BTREE,
  KEY `idx_provider_sync_strategy` (`provider_id`,`sync_strategy`) USING BTREE,
  KEY `idx_region_country_city` (`region`,`country`,`city`) USING BTREE,
  KEY `idx_config_proxy_type` (`config_proxy_type`) USING BTREE,
  KEY `idx_config_proxies_format` (`config_proxies_format`) USING BTREE,
  KEY `idx_config_purpose_web` (`config_purpose_web`) USING BTREE,
  KEY `idx_config_composite` (`provider_id`,`config_proxy_type`,`config_proxies_format`,`config_purpose_web`) USING BTREE,
  CONSTRAINT `fk_shop_product_provider` FOREIGN KEY (`provider_id`) REFERENCES `shop_provider` (`provider_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

1. 设计个修复方案
2. shop_product的sync_strategy字段会有什么影响
3. 为什么我的商品之前已经定价类型是手动(isPriceManual), 它的价格还会被syncAllProductPrices任务设置的价格覆盖了
4. 为什么syncAllProductPrices执行的任务没有在shop_product_price_history有记录存在,还有多少种同步涉及价格的,没有在shop_product_price_history中处理
5. 怎么避免发生同类问题
