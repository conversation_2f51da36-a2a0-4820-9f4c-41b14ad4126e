好的！非常高兴我们达成了一致，并且排除了所有疑点。现在，我将整合我们之前所有的讨论和决策，为您提供一份全面且更新的技术方案。

**项目：GoMaskIP - 产品地理位置标准化与价格同步优化方案 (最终版)**

**I. 核心目标**

1. **地理位置标准化**: `shop_product` 表存储和展示的地理位置信息，必须基于系统内部的地理位置基础库 (`sys_location_city`, `sys_location_country`, `sys_location_region`) 进行标准化。
2. **价格准确性与一致性**: `shop_product.price` (销售价) 应由后端在产品同步时，根据准确的 `shop_product.cost_price` (供应商成本价) 和集中的定价规则计算并存储。前端直接使用此价格。
3. **便捷的调价机制**: 提供全局规则调整、批量价格重算以及单个产品手动调价的机制，并妥善处理自动计算与手动设定的冲突，适合人手有限的管理模式。

**II. 数据库与实体层修改**

1. **`ShopProviderIpInventory` 实体 (`server/src/module/shop/provider/entities/provider-ip-inventory.entity.ts`)**
    * **作用**: 存储从供应商API同步的原始库存/可售单元信息。
    * **关键字段**: `provider_id`, `city_id` (供应商原始城市ID), `city_name`, `country_code`, `continents_name`, `proxies_type`, `proxies_format`, `stock_count`, `raw_data_snapshot` (或 `rawInventoryData`)。
    * **价格字段 (`unit_price`, `currency`)**: 对于IPNUX这类不直接在库存列表API中返回准确单价的供应商，这些字段在此表将**不被 `IpnuxInventoryService` 填充**或仅作参考。准确成本价将在 `ProductSyncService` 中获取。

2. **`Product` 实体 (`server/src/module/shop/products/entities/product.entity.ts`)**
    * **移除/明确旧地理字段用途**: 移除或标记不再用于前端展示的旧的、存储供应商原始地理文本的字段。
    * **添加标准化地理位置ID**:
        * `standard_location_city_id: number` (BIGINT, nullable, FK to `sys_location_city.id`, ON DELETE SET NULL)
        * `@ManyToOne(() => LocationCity)` 关联。
    * **添加冗余的标准化地理名称 (优化前端)**:
        * `standard_city_name: string` (VARCHAR, nullable)
        * `standard_country_name: string` (VARCHAR, nullable)
        * `standard_country_code: string` (VARCHAR(10), nullable)
        * `standard_region_name: string` (VARCHAR, nullable)
    * **价格相关字段**:
        * `cost_price: number` (DECIMAL(10,2) or DECIMAL(12,4), nullable, 供应商成本价)
        * `cost_price_currency: string` (VARCHAR(10), nullable, 成本价货币，如 'USD')
        * `price: number` (DECIMAL(10,2), nullable, 商城最终销售价)
        * `currency: string` (VARCHAR(10), nullable, 销售价货币，如 'CNY')
        * `discount_price: number` (DECIMAL(10,2), nullable)
    * **手动价格标记**:
        * `is_price_manual: boolean` (BOOLEAN, `NOT NULL`, `DEFAULT FALSE`)

3. **数据库迁移脚本 (`server/db/migrations/YYYYMMDDHHMM_refactor_product_location_and_price.sql`)**
    * **备份 `shop_product` 表。**
    * (可选) `ALTER TABLE shop_product DROP COLUMN ...` (移除旧的原始地理字段)。
    * `ALTER TABLE shop_product ADD COLUMN standard_location_city_id ...`, `ADD COLUMN standard_city_name ...`, etc.
    * `ALTER TABLE shop_product ADD COLUMN cost_price ...`, `ADD COLUMN cost_price_currency ...` (如果尚不存在或类型不符)。
    * `ALTER TABLE shop_product MODIFY COLUMN price ...`, `MODIFY COLUMN currency ...` (确保类型和可空性正确)。
    * `ALTER TABLE shop_product ADD COLUMN is_price_manual BOOLEAN NOT NULL DEFAULT FALSE;`
    * 添加 `standard_location_city_id` 的外键约束。

**III. 服务层修改 (Backend - `server/`)**

1. **`IpnuxInventoryService.fetchAndStoreInventory` (或相应供应商的库存同步服务)**
    * **职责**: 从供应商API同步原始库存条目到 `ShopProviderIpInventory`。
    * **价格处理**: **不**从此服务的API调用结果中填充 `ShopProviderIpInventory.unit_price` 和 `currency` (因为IPNUX的 `getAvailableInventory` API不直接提供准确的、区分地区的单价)。这些字段将由后续流程处理或保持为空。

2. **`ProductSyncService.createOrUpdateProductFromInventory` (核心改造)**
    * **数据源**: 遍历 `ShopProviderIpInventory` 中的记录 (`inventoryItem`)。
    * **地理位置标准化**:
        * 从 `inventoryItem` 提取原始地理信息 (`provider_id`, `city_id` - 供应商的, `city_name`, `country_code` 等)。
        * 调用 `SupplierLocationMappingService.findByProviderAndNativeCityId` 获取 `standard_location_city_id`。
        * 根据 `standard_location_city_id` 从 `LocationCityService` (及关联服务) 获取 `standard_city_name`, `standard_country_name`, `standard_country_code`, `standard_region_name`。
    * **供应商成本价获取**:
        * 调用 `this.inventoryService.calculatePrice(payload)` (内部调用 `ipnuxService.calculatePrice`)。
            * `payload` 包含 `inventoryItem.proxies_type`, `inventoryItem.proxies_format`，标准 `time_period` (如30天，可配置)，和成本货币 (如 'USD')。
        * 从响应的 `proxies_count_discount_tiers` 中，根据 `inventoryItem.country_code` (或映射后的国家名) 找到对应的 `per_proxy_price`。此为 `supplierCostPriceBaseCurrency` (例如 `supplierCostPriceUSD`)。
        * 记录 `supplierCostCurrency` (例如 `'USD'`)。
    * **商城销售价计算**:
        1. 读取 `sys_config` 中的定价规则：
            * `pricing.default_markup_percentage` (例如 `2.0` 代表200%加价)。
            * `pricing.target_currency` (例如 `'CNY'`)。
            * 汇率 (根据 `pricing.use_manual_exchange_rate` 决定是使用 `pricing.manual_usd_to_cny_exchange_rate` 还是调用 `this.inventoryService.getUsdToCnyRate()`)。
        2. `markup = configService.get('pricing.default_markup_percentage', 2.0);`
        3. `sellingPriceBase = supplierCostPriceBaseCurrency * (1 + markup);`
        4. 如果需要，进行汇率转换得到 `sellingPriceTarget`.
        5. `finalSellingPrice = parseFloat(sellingPriceTarget.toFixed(2));`
        6. `finalSellingCurrency = configService.get('pricing.target_currency');`
    * **填充 `Product` 实体 (`productData`)**:
        * `cost_price = supplierCostPriceBaseCurrency;`
        * `cost_price_currency = supplierCostCurrency;`
        * `price = finalSellingPrice;`
        * `currency = finalSellingCurrency;`
        * `standard_location_city_id`, `standard_city_name`, 等标准地理信息。
        * `is_price_manual = false;` (因为这是规则计算的)
        * 其他字段如 `product_code` (通过 `ProductCodeService` 生成，可包含标准化地理因素), `product_name` (包含标准化地理名称), `status`, `provider_id` 等。
    * **保存/更新 `shop_product`**: 根据 `product_code` 判断是创建还是更新。
    * **清除缓存**: `this.clearProductCache([product.id])`。

3. **`ProductSyncService.recalculateAllProductSellingPrices()` (新增或重构)**
    * **作为 `@Task` 实现**，由 `admin-web` 异步触发。
    * **逻辑**:
        1. 获取当前 `sys_config` 中的定价规则。
        2. 查询所有 `shop_product` 记录，条件为 `is_price_manual = false`。
        3. 分批处理。
        4. 对每个产品，使用其已存储的 `cost_price` 和 `cost_price_currency`，结合当前定价规则，重新计算 `price` 和 `currency`。
        5. 如果计算出的新售价与当前售价不同，则更新产品的 `price`, `currency`, `update_time`。
        6. 批量保存更新。
        7. 记录操作日志。
        8. 任务完成后，清除所有产品缓存 (`this.clearAllProductCache()`)。

4. **`ProductsService` (system - `server/src/module/system/products/products.service.ts`)**
    * **`update(id, updateProductDto)` 方法**:
        * 如果 `updateProductDto` 中包含对 `price` 字段的修改 (即管理员手动修改价格):
            * 将对应产品的 `is_price_manual` 设为 `true`。
        * 保存产品后，调用 `this.clearProductCache([id])`。

5. **`ProductsController` (system - `server/src/module/system/products/products.controller.ts`)**
    * **新增端点**: `POST /products/tasks/recalculate-prices` (触发 `recalculateAllProductSellingPrices` 任务)。
    * **新增端点**: `POST /products/{id}/actions/revert-to-auto-pricing`
        * 调用服务层方法，将指定产品的 `is_price_manual` 设为 `false`。
        * 立即根据该产品的 `cost_price` 和当前全局规则重新计算并更新其 `price`。
        * 返回更新后的产品。

6. **`InventoryService.getUsdToCnyRate()` (`server/src/module/shop/inventory/inventory.service.ts`)**
    * 已确认其实现：缓存 -> API -> 默认值。此机制可继续使用。
    * 考虑在 `sys_config` 中增加 `pricing.manual_usd_to_cny_exchange_rate` 和 `pricing.use_manual_exchange_rate` 配置，让价格计算逻辑优先使用手动配置的汇率（如果启用）。

7. **`sys_config` 配置项**:
    * `pricing.default_markup_percentage` (值为 `"2.0"`)
    * `pricing.target_currency` (值为 `"CNY"`)
    * `pricing.use_manual_exchange_rate` (布尔字符串, e.g., `"true"` 或 `"false"`)
    * `pricing.manual_usd_to_cny_exchange_rate` (数字字符串, e.g., `"7.10"`)

**IV. 前端修改 (`shop-web/`)**

1. **类型定义 (`services/generated/types.d.ts`, `types/shop-product.d.ts`)**:
    * 更新 `ApiTypes.Product` (或 `shopProduct`) 类型，移除旧的原始地理字段，添加新的标准化地理字段 (`standard_location_city_id`, `standard_city_name`, etc.) 和价格字段 (`cost_price`, `cost_price_currency`, `price`, `currency`, `is_price_manual`)。

2. **产品展示**:
    * 所有显示产品地理位置的地方（列表、详情、购物车、订单），使用新的标准化地理名称字段 (`standard_city_name`, `standard_country_name`, `standard_country_code`, `standard_region_name`)。
    * 所有显示价格的地方，直接使用 `product.price` 和 `product.currency`。

3. **价格相关API调用**:
    * **移除**: 前端对 `postShopOpenApiGetCurrentVersionPrice` 的实时调用以获取或计算显示价格。
    * **`getShopOpenApiGetNodeInventory` 的作用**: 主要用于在产品选择/定价页面 (`Pricing.tsx`) 展示特定产品类型有哪些**可用供应商原始地点及其库存**。

4. **产品选择与购物车逻辑 (`Pricing.tsx`, `Cart.tsx`, `productStore.ts`)**:
    * 当用户在 `Pricing.tsx` 选择一个供应商提供的地点 (基于 `getShopOpenApiGetNodeInventory` 的返回)：
        * 前端需要将这个选择（包含供应商原始 `city_id`, `proxies_type` 等）转换为我们系统中的一个具体 `shop_product`。
        * **策略1 (推荐)**: 后端提供一个查找接口 `GET /api/shop/products/resolve?provider_code=X&supplier_city_id=Y&proxies_type=Z`，该接口返回匹配的单个 `shop_product` 对象（包含其预计算好的 `price`）。前端调用此接口获取完整的 `shop_product` 信息后加入购物车。
        * **策略2**: 如果 `shop_product.product_code` 或 `shop_product.external_product_id` (例如存储 `ShopProviderIpInventory.id`) 能够唯一地从前端选择反向定位，则前端可以直接请求 `/api/shop/products/{product_code_or_id}`。
    * 购物车中的 `CartItem` 应存储 `shop_product.id` (或 `product_code`) 和数量，价格直接从关联的 `shop_product` 对象获取。

5. **地理位置筛选**:
    * 筛选器的数据源应来自后端提供的标准化地理位置选项API (e.g., `/api/shop/locations/cities`).
    * 筛选时传递标准化的ID (e.g., `standardLocationCityId`) 给 `/api/shop/products`。

**V. 后台管理系统修改 (`admin-web/`)**

**IV. 后台管理系统修改 (`admin-web/`)**

* **审查**:
    * 产品列表、编辑、批量操作和系统配置的修改点是合理的。
* **扩展与细化**:
    1. **产品管理 (`admin-web/src/views/shop/product/management.vue` & `edit.vue`)**:
        * **列表**:
            * 增加 "最后成本同步时间" 列。
            * `is_price_manual` 列可以使用标签（Tag）组件，用不同颜色区分 "是" (例如橙色) / "否" (例如绿色)。
        * **编辑**:
            * 当 `is_price_manual` 为 `true` 时，"恢复按规则定价" 按钮旁边可以显示一个 Tooltip，解释点击后会发生什么。
            * **价格历史查看**: 如果实现了价格历史表，在产品编辑页面增加一个按钮或标签页，点击后弹出一个对话框，展示该产品的价格变动历史列表。
        * **批量操作**:
            * "刷新所有自动定价产品售价" 按钮：点击后应有二次确认，并告知管理员这是一个后台任务，可能需要一些时间完成。可以考虑提供一个跳转到任务监控页面的链接。
    2. **系统配置 (`admin-web/src/views/system/config/index.vue`)**:
        * **定价配置分组**: 将所有 `pricing.*` 相关的配置项归类到一个 "定价配置" 或 "商城财务设置" 的分组/标签页下，方便管理。
        * **输入校验**: 对 `pricing.default_markup_percentage` 和 `pricing.manual_usd_to_cny_exchange_rate` 进行输入校验（例如，必须是数字，百分比不能为负等）。
    3. **供应商城市映射管理 (`admin-web/src/views/system/location/mapping/index.vue`)**:
        * 当创建或更新一个映射后，可以给管理员一个提示，询问是否需要立即为使用此供应商城市的现有产品更新地理位置信息和价格。如果选择是，则可以触发一个后台任务。
    4. **任务监控页面 (新增)**:
        * 一个简单的页面，列出最近的后台任务（如价格批量重算、库存同步），显示任务ID、名称、状态、开始/结束时间、结果摘要。
        * 提供手动触发某些任务的按钮（如果适用）。

2. **系统配置**:
    * 提供界面管理 `pricing.*` 相关的配置项，特别是 `pricing.default_markup_percentage` 和汇率相关配置。

**VI. 测试**
    *全面测试产品同步、价格计算、前端展示、购物车、订单、后台调价等所有相关流程。
    *   特别关注手动调价与批量重算之间的交互。

这个方案详细、全面，并且考虑了您提出的所有要点和之前的决策。它旨在建立一个数据准确、易于维护且对管理员友好的价格管理系统。
