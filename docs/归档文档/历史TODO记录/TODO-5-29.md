**产品需求文档 (PRD) - 产品列表管理页面**

**2. 产品概述**

本页面旨在为系统管理员或运营人员提供一个集中管理和查看所有产品信息的平台。用户可以通过此页面进行产品信息的筛选、查看、编辑以及执行其他相关操作。

**4. 功能需求**

**4.1 筛选与搜索区**

* **4.1.1 产品编号输入框:**
    * 描述：允许用户输入产品编号进行精确搜索。
    * 类型：文本输入框。
    * 交互：输入后，需点击"搜索"按钮触发。
* **4.1.2 产品名称输入框:**
    * 描述：允许用户输入产品名称进行模糊或精确搜索。
    * 类型：文本输入框。
    * 交互：输入后，需点击"搜索"按钮触发。
* **4.1.3 区域/国家/城市选择器 (级联选择):**
    * 描述：允许用户通过选择区域、国家、城市来筛选产品。
    * 类型：级联下拉选择器。
    * 默认值/占位符：如图所示，可能为"北美洲-美国-纽约"或"请选择区域/国家/城市"。
    * 交互：选择后，需点击"搜索"按钮触发。
* **4.1.4 产品类型选择器:**
    * 描述：允许用户根据产品类型进行筛选。
    * 类型：下拉选择器。
    * 选项：根据系统中定义的产品类型动态加载（例如：海外静态住宅、海外动态住宅、海外静态数据中心等）。
    * 默认值/占位符："产品类型"或"全部类型"。
    * 交互：选择后，需点击"搜索"按钮触发。
* **4.1.5 供应商选择器:**
    * 描述：允许用户根据供应商进行筛选。
    * 类型：下拉选择器。
    * 选项：根据系统中定义的供应商动态加载（例如：IPNUX, 亚马逊云, IPID等）。
    * 默认值/占位符："供应商"或"全部供应商"。
    * 交互：选择后，需点击"搜索"按钮触发。
* **4.1.6 状态选择器:**
    * 描述：允许用户根据产品状态（如：已上架、已下架、待审核等）进行筛选。
    * 类型：下拉选择器。
    * 选项：根据系统中定义的产品状态动态加载。
    * 默认值/占位符："状态"或"全部状态"。
    * 交互：选择后，需点击"搜索"按钮触发。
* **4.1.7 搜索按钮:**
    * 描述：点击后，根据当前所有筛选条件刷新产品列表。
    * 类型：按钮。
* **4.1.8 重置按钮:**
    * 描述：点击后，清除所有筛选条件，并刷新产品列表至默认状态。
    * 类型：按钮。

**4.2 产品列表区**

* 描述：以表格形式展示符合筛选条件的产品信息。
* **4.2.1 列表字段 (列):**
    * **产品id:** 产品的系统内部唯一标识符 (通常为数字)。
    * **产品编号:** 用户可见的产品编号/SKU。
    * **产品名称:** 产品的名称。
    * **供应商:** 提供该产品的供应商。
    * **产品类型:** 产品的分类。
    * **成本价:** 产品的成本价格，需注明货币单位（如：元, 美元）。
    * **售价:** 产品的销售价格，需注明货币单位（如：元）。
    * **折扣价:** 产品的折扣后价格，可能为空（显示为 "—"）。
    * **流量(MB):** 产品包含的数据流量，可能是具体数值或"无限"。
    * **区域:** 产品适用的地理区域。
    * **国家:** 产品适用的国家。
    * **城市:** 产品适用的城市。
    * **有效期(天):** 产品的有效天数。
    * **操作:** 针对单条产品可执行的操作集合。

* **4.2.2 行操作 (操作列中的功能):**
    * **编辑:** 点击后，跳转到产品编辑页面或弹出编辑框，允许修改该产品信息。
    * **下架/上架:** (图中显示"下架") 点击后，更改产品状态。若为"下架"，则变为"上架"按钮，反之亦然。
    * **授权:** 点击后，进行产品授权相关操作（具体流程需进一步定义）。
    * **已授权:** (可能是状态显示，或点击后取消授权的按钮) 具体功能需进一步定义。
    * **同步:** 点击后，执行产品信息同步操作（具体同步目标和内容需进一步定义）。
    * **IP段:** 点击后，跳转到管理或查看该产品关联的IP段信息的页面/弹窗。

**4.3 分页功能**

* 描述：当产品列表数据过多时，进行分页展示。
* 组件：
    * 上一页按钮
    * 页码列表 (可点击跳转到指定页)
    * 省略号 (当页码过多时)
    * 下一页按钮
    * (可选) 每页显示条数选择器
    * (可选) 跳转到指定页输入框
* 交互：
    * 默认显示第一页数据。
    * 点击页码或"上一页"/"下一页"按钮可切换页面内容。
    * 当在第一页时，"上一页"按钮应禁用；当在最后一页时，"下一页"按钮应禁用。
    * 图中显示当前在第1页，总共有23页。

**5. 字段详细说明 (补充与确认)**

| 序号 | 字段名称（原型） | 字段含义             | 数据类型      | 备注/示例                                       |
| :--- | :--------------- | :------------------- | :------------ | :---------------------------------------------- |
| 1    | 产品id           | 产品系统唯一ID       | Number/Integer | 如：1, 2, 9                                     |
| 2    | 产品编号         | 产品SKU或对外编号    | String        | 如：IPNUX_01, NUX_d01, AMZ_01, IPID_01          |
| 3    | 产品名称         | 产品对外显示名称     | String        | 如：住宅1, 亚马逊云, 住宅2                        |
| 4    | 供应商           | 产品提供方           | String        | 如：IPNUX, 亚马逊云, IPID                         |
| 5    | 产品类型         | 产品分类             | String        | 如：海外静态住宅, 海外动态住宅, 海外静态数据中心  |
| 6    | 成本价           | 产品成本             | Decimal/String | 如：12元, 3元, 3美元 (注意货币单位)               |
| 7    | 售价             | 产品销售价           | Decimal/String | 如：60元, 40元, 50元 (注意货币单位)               |
| 8    | 折扣价           | 折扣后价格           | Decimal/String | 可能为空，显示为 "—"                             |
| 9    | 流量(MB)         | 数据流量额度         | Integer/String | 如：1024, 无限                                  |
| 10   | 区域             | 地理区域             | String        | 如：亚洲, 北美洲                                |
| 11   | 国家             | 所属国家             | String        | 如：中国, 美国                                  |
| 12   | 城市             | 所属城市             | String        | 如：香港, 纽约                                  |
| 13   | 有效期(天)       | 产品有效时间（天）   | Integer       | 如：30                                          |
| 14   | (筛选) 状态      | 产品当前状态         | Enum/String   | 具体状态值需定义 (如：上架、下架、审核中等)       |

**7. 待确认项/疑问点**

* "授权"和"已授权"的具体业务逻辑是什么？是状态切换还是不同的操作入口？
* "同步"操作的具体含义和目标系统是什么？
* "IP段"点击后是跳转新页面还是弹窗？展示哪些信息？是否可编辑？
* 状态选择器中的具体状态有哪些？
* 价格字段是否需要支持多种货币？如何处理汇率？（原型中已出现"元"和"美元"）
* "折扣价"为空时，具体业务含义是什么？

---

## 实施进度

### 已完成功能

1. **数据库扩展**
   - ✅ 扩展 shop_product 表，添加产品管理所需字段
   - ✅ 创建 shop_product_ip_segments 表用于IP段管理
   - ✅ 添加地理位置表 (continents, countries, cities)

2. **后端API实现**
   - ✅ 产品管理查询接口 (GET /api/system/products/management)
   - ✅ 批量上下架接口 (PUT /api/system/products/batch-status)
   - ✅ 批量同步接口 (POST /api/system/products/batch-sync)
   - ✅ IP段分配接口 (POST /api/system/products/ip-segment/allocate)
   - ✅ IP段查询接口 (GET /api/system/products/ip-segment/:productId)
   - ✅ 库存更新接口 (PUT /api/system/products/inventory/update)

3. **前端页面实现**
   - ✅ 产品管理列表页面
   - ✅ 高级筛选功能（产品编号、名称、地区、类型、供应商、状态）
   - ✅ 批量操作功能（批量上下架、批量同步）
   - ✅ IP段查看弹窗
   - ✅ 分页功能

4. **其他**
   - ✅ Redis原子操作支持（产品编号生成）
   - ✅ 修复了Swagger循环依赖错误
   - ✅ 移除了授权功能（改为未来的代理商功能）

### 待完成功能

1. **第二阶段开发**
   - 异步任务处理（同步操作）
   - 地理位置管理（区域/国家/城市数据维护）
   - 产品编辑功能
   - 更完善的错误处理

2. **业务逻辑确认**
   - 同步操作的具体实现细节
   - 库存状态的自动更新机制
   - 多币种价格处理

这份PRD是基于您提供的原型图生成的，实际开发中可能需要根据更详细的业务逻辑进行调整和补充。希望对您有帮助！