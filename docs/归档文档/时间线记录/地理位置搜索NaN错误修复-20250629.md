# 地理位置搜索 NaN 错误修复记录

**日期**: 2025-06-29  
**问题**: Unknown column 'NaN' in 'where clause' 错误  
**影响接口**: 
- `/api/location/region/search`
- `/api/location/country/search` 
- `/api/location/city/search`
- `/api/location/unified-search`

## 问题描述

在地理位置搜索接口中出现了 `Unknown column 'NaN' in 'where clause'` 的数据库错误。错误日志显示：

```
{"context":"请求路径: GET /api/location/region/search?pageNum=1&pageSize=50","level":"error","message":"[132b1836-9a6f-433e-bdd0-546f69e81b3b] 系统异常: Unknown column 'NaN' in 'where clause'"}
```

## 根本原因分析

1. **参数类型转换问题**: 当前端传递的 `pageNum` 或 `pageSize` 参数为空、undefined 或无效值时，`Number()` 转换会产生 `NaN`
2. **TypeORM 处理问题**: TypeORM 的 `skip()` 和 `take()` 方法接收到 `NaN` 值时，会直接将其插入到 SQL 查询中
3. **DTO 验证不足**: 虽然有 `@Transform` 装饰器，但在某些情况下没有正确处理边界情况

## 修复方案

### 1. 创建安全的参数解析方法

在 `LocationSearchService` 中添加了 `safeParseInt` 方法：

```typescript
private safeParseInt(value: any, defaultValue: number): number {
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }
  
  const parsed = Number(value);
  if (Number.isNaN(parsed) || !Number.isInteger(parsed) || parsed < 1) {
    return defaultValue;
  }
  
  return parsed;
}
```

### 2. 修复所有搜索方法

#### searchRegions 方法
- 使用 `safeParseInt` 处理 `pageNum` 和 `pageSize`
- 添加分页参数有效性检查
- 移除了调试日志

#### searchCountries 方法  
- 同样使用 `safeParseInt` 处理分页参数
- 更新缓存键生成逻辑
- 添加参数验证

#### searchCities 方法
- 应用相同的安全参数处理
- 保持原有的智能排序逻辑
- 确保分页参数有效性

#### performSearch 和 searchAll 方法
- 统一使用安全的参数处理
- 确保传递给子方法的参数都是有效的

### 3. 简化 DTO 验证

移除了 DTO 中复杂的 `@Transform` 装饰器，改为依赖服务层的安全处理：

```typescript
// 修复前
@Transform(({ value }) => {
  if (value === undefined || value === null || value === '') return undefined;
  const num = Number(value);
  return isNaN(num) ? undefined : num;
})

// 修复后 - 移除复杂的 Transform，依赖服务层处理
@Type(() => Number)
@IsNumber()
@Min(1)
```

## 修复的文件

1. **server/src/module/system/location/services/location-search.service.ts**
   - 添加 `safeParseInt` 方法
   - 修复所有搜索方法的参数处理
   - 添加分页参数验证

2. **server/src/module/system/location/dto/search.dto.ts**
   - 简化 DTO 验证装饰器
   - 移除可能导致问题的 `@Transform` 装饰器

## 测试验证

创建了测试脚本 `server/test-location-search.js` 来验证修复效果，测试用例包括：

- 正常参数请求
- 缺少分页参数的请求  
- 无效分页参数的请求
- 各种边界情况

## 预期效果

1. **消除 NaN 错误**: 所有地理位置搜索接口不再出现 `Unknown column 'NaN'` 错误
2. **参数容错性**: 接口能够正确处理缺失、无效或边界情况的分页参数
3. **向后兼容**: 现有的正常请求不受影响
4. **性能优化**: 移除了调试日志，提高了执行效率

## 后续建议

1. **监控日志**: 持续监控错误日志，确认修复效果
2. **前端优化**: 建议前端在发送请求时也进行参数验证
3. **统一处理**: 考虑在其他类似的分页接口中应用相同的安全处理模式
4. **单元测试**: 为 `safeParseInt` 方法添加单元测试

## 影响评估

- **风险等级**: 低
- **影响范围**: 仅限地理位置搜索相关接口
- **兼容性**: 完全向后兼容
- **性能影响**: 轻微提升（移除调试日志）
