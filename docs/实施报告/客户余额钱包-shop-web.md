好的，我们来更新开发文档，并融入您提出的充值流程优化建议和API路径修正。

---

## 客户余额功能开发文档 (shop-web) - 修订版

### 1. 功能概述

本次需求旨在为 `shop-web` 前端项目添加完整的客户余额功能，包括余额展示、钱包管理页面、充值功能以及使用余额支付订单的功能。

**核心需求点：**

1. **余额展示：**
    * 在用户仪表盘（Dashboard）或首页显著位置展示用户当前总余额、充值余额、赠送余额。
    * 在导航栏或用户菜单中展示当前可用总余额。
2. **钱包管理页面：**
    * 创建一个新的页面，例如 `/dashboard/wallet`。
    * 展示余额明细（充值余额、赠送余额、冻结金额）。
    * 展示交易记录（充值、消费、退款等）。
    * 提供充值入口/表单。
3. **充值功能：**
    * 用户可以选择或输入充值金额。
    * **优化：复用现有的订单支付流程处理充值。**
    * 充值成功后，余额实时更新。
    * **优化：在余额不足时，应有明确的引导和快捷入口进行充值。**
4. **余额支付：**
    * 在订单支付环节，增加“余额支付”选项。
    * 若余额充足，用户可以直接使用余额完成支付。
    * 若余额不足，提示用户并提供快捷充值入口。

### 2. 后端依赖分析

根据提供的 `server-report.md` 和数据库 schema，后端已经提供了相关的余额和交易管理的表结构和API接口。前端主要需要对接以下实体和API：

**相关后端实体（参考数据库 schema）：**

* `shop_user_wallet`: 存储用户钱包信息，包括 `balance`, `recharge_balance`, `bonus_balance`, `frozen_amount`。
* `shop_transaction`: 记录用户交易，包括类型（充值、消费、退款）、金额、状态等。
* `shop_order`: 充值可以被视为一种特殊的订单。
* `shop_wallet_change_log`: 记录钱包余额（充值/赠送）的详细变动。

**相关后端API (参考 `server/src/module/shop/wallet/wallet.controller.ts`, `server/src/module/shop/orders/orders.controller.ts` 和 `server/src/module/payment/payment.controller.ts`):**

* **获取用户钱包信息:**
    * `GET /api/shop/wallet` (获取当前登录用户的钱包信息)
* **获取用户交易记录:**
    * `GET /api/shop/wallet/transactions` (分页获取用户交易记录)
* **发起充值（本质是创建充值订单）:**
    * `POST /api/shop/wallet/recharge` (用户发起充值请求，后端会创建一个特殊的充值订单，并返回此订单信息，前端根据此信息引导支付)
    * 此接口返回的 `transactionId` 或一个特定的订单ID（充值订单ID）将用于后续支付。
* **获取支付参数/链接 (用于充值订单的支付):**
    * `GET /api/shop/orders/:orderId/payment-url` (获取特定订单的支付链接，这里的 `:orderId` 就是充值请求返回的订单ID)
    * 或者，`POST /api/shop/payment/create` (如果后端设计为前端传递充值订单ID和支付方式来获取支付详情)
* **检查订单支付状态 (用于充值订单):**
    * `GET /api/shop/orders/:orderId/payment-status`
* **使用余额支付订单:**
    * `POST /api/shop/wallet/pay` (修正后的路径，需要 `orderId` 和 `amount`作为参数，注意此处的 `amount` 应该是订单金额)
* **获取可用支付方式列表 (用于充值):**
    * `GET /api/shop/payment-methods`

### 3. 前端设计与实现

#### 3.1. 数据模型 (Types)

在 `shop-web/src/types/` 目录下或特定功能模块下定义相关类型：

```typescript
// shop-web/src/types/wallet.d.ts (或类似文件)

export interface UserWallet {
  walletId: number;
  customerId: number;
  balance: string;
  rechargeBalance: string;
  bonusBalance: string;
  frozenAmount: string;
  points?: number;
  status: '0' | '1'; // 0正常 1冻结
  version?: number;
  createTime?: string;
  updateTime?: string;
}

export enum TransactionTypeEnum {
  RECHARGE = '1',
  PAYMENT = '2', // 消费/订单支付
  REFUND = '3',
  ADJUSTMENT = '4', // 后台调整
}

export enum TransactionStatusEnum {
  PENDING = '0', // 处理中/待支付
  COMPLETED = '1', // 成功/已支付
  FAILED = '2', // 失败
}

export interface WalletTransaction {
  transactionId: string;
  customerId: number;
  transactionType: TransactionTypeEnum;
  amount: string;
  rechargeComponentAmount?: string;
  bonusComponentAmount?: string;
  paymentMethod?: string; // 支付方式名称 (通常从关联订单获取)
  transactionStatus: TransactionStatusEnum;
  externalReference?: string; // 支付平台交易号
  relatedOrder?: string; // 关联的商品订单ID或充值订单ID
  balanceBefore?: string;
  balanceAfter?: string;
  description?: string;
  createTime: string;
  remark?: string;
}

// 充值请求，由 /api/shop/wallet/recharge 处理
export interface InitiateRechargePayload extends ApiTypes.RechargeDto {
  // amount: number; (来自 ApiTypes.RechargeDto)
  // paymentMethod: string; (来自 ApiTypes.RechargeDto, 支付方式的标识，如 'alipay', 'wechat')
  // remark?: string; (来自 ApiTypes.RechargeDto)
}

// 充值请求成功后，后端返回的可能是 WalletTransaction (状态为PENDING) 或一个特殊的订单对象
export interface InitiateRechargeResponse extends WalletTransaction {
  // 或者包含一个 orderId 用于后续的支付流程
  rechargeOrderId?: string; // 假设后端返回一个充值专用的订单ID
}


export interface PayWithBalancePayload {
  orderId: string; // 商品订单ID
  // amount: number; // 后端 /api/shop/wallet/pay 不需要此参数，会根据orderId自行查询
}
```

#### 3.2. API Services

在 `shop-web/src/services/` 目录下创建或修改服务文件，例如 `walletService.ts`。

```typescript
// shop-web/src/services/walletService.ts
import apiClient from "./api-client";
import type { ResultData } from "@/types/global";
import type { UserWallet, WalletTransaction, InitiateRechargePayload, InitiateRechargeResponse, PayWithBalancePayload } from "@/types/wallet";
import type { ApiTypes } from "./generated/types";

const WALLET_BASE_URL = "/api/shop/wallet";

export const getUserWallet = async (): Promise<ResultData<UserWallet>> => {
  const response = await apiClient.get<ResultData<UserWallet>>(`${WALLET_BASE_URL}`);
  return response.data;
};

export const getWalletTransactions = async (params?: { page?: number; limit?: number }): Promise<ResultData<{ list: WalletTransaction[]; total: number }>> => {
  const response = await apiClient.get<ResultData<{ list: WalletTransaction[]; total: number }>>(`${WALLET_BASE_URL}/transactions`, { params });
  return response.data;
};

// 封装发起充值的动作
export const initiateRecharge = async (data: InitiateRechargePayload): Promise<ResultData<InitiateRechargeResponse>> => {
  // 后端 /api/shop/wallet/recharge 预计会创建一个类型为 RECHARGE 的 Transaction (状态为 PENDING)
  // 并可能返回这个 Transaction 对象，或者一个专门的充值订单对象。
  // 我们假设它返回一个包含 `transactionId` (或 `rechargeOrderId`) 的对象，这个ID将用于支付。
  const response = await apiClient.post<ResultData<InitiateRechargeResponse>>(`${WALLET_BASE_URL}/recharge`, data);
  return response.data;
};

// 余额支付
export const payOrderWithBalance = async (data: PayWithBalancePayload): Promise<ResultData<WalletTransaction>> => {
  // API路径修正为 /api/shop/wallet/pay
  // 后端接口 /api/shop/wallet/pay(orderId: string, amount: number)
  // 从前端类型 PayWithBalancePayload 中移除 amount，因为后端会根据 orderId 查询。
  // 但需要确认后端实现是否真的不需要 amount。如果后端需要，则前端类型和调用处保持。
  // 假设后端 /api/shop/wallet/pay 只需要 orderId。
  const { orderId } = data;
  const response = await apiClient.post<ResultData<WalletTransaction>>(`${WALLET_BASE_URL}/pay`, { orderId });
  return response.data;
};
```

对于充值订单的支付，复用 `shop-web/src/services/payment.ts` 中的 `createPayment` 和 `shop-web/src/services/orders.ts` 中的 `checkPaymentStatus`。

```typescript
// shop-web/src/services/payment.ts
// ... (现有 createPayment) ...
// 可以直接使用，将充值订单ID作为 orderId 传入

// shop-web/src/services/orders.ts
// ... (现有 checkPaymentStatus) ...
// 可以直接使用，将充值订单ID作为 orderId 传入
```

#### 3.3. Zustand Store (可选, 用于全局余额状态)

同上一版，`fetchUserWallet` 在用户登录和鉴权状态检查后调用。

```typescript
// shop-web/src/store/user.ts (扩展现有 userStore)
// ... (结构同上一版，确保 fetchUserWallet 和 clearWallet 的调用时机正确) ...

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        // ...
        wallet: null,
        fetchUserWallet: async () => {
          if (!get().isAuthenticated || !get().user) { // 确保用户已认证且用户信息存在
            console.log("User not authenticated or user data not loaded, skipping wallet fetch.");
            return;
          }
          set({ isLoading: true }); // 可以增加一个钱包专用的加载状态
          try {
            const walletData = await getUserWallet(); // API返回的是ResultData<UserWallet>
            set({ wallet: walletData, isLoading: false });
          } catch (error) {
            console.error("Failed to fetch user wallet:", error);
            set({ wallet: null, isLoading: false });
          }
        },
        // ...
        // 在 login 成功后，获取用户 profile 后，再调用 fetchUserWallet
        // 在 checkAuth 中，如果用户已认证且 userStore.user 已有数据，则调用 fetchUserWallet
      }),
      // ...
    )
  )
);
```

#### 3.4. React Query Hooks

```typescript
// shop-web/src/hooks/useWallet.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getUserWallet,
  getWalletTransactions,
  initiateRecharge,
  payOrderWithBalance,
} from "@/services/walletService";
import type { InitiateRechargePayload, PayWithBalancePayload, InitiateRechargeResponse } from "@/types/wallet";
import type { ApiTypes } from "@/services/generated/types"; // 确保这个路径和内容正确

export const RQ_KEY_USER_WALLET = "userWallet";
export const RQ_KEY_WALLET_TRANSACTIONS = "walletTransactions";

export function useUserWallet() {
  return useQuery({
    queryKey: [RQ_KEY_USER_WALLET],
    queryFn: getUserWallet,
  });
}

export function useWalletTransactions(page: number = 1, limit: number = 10) {
  return useQuery({
    queryKey: [RQ_KEY_WALLET_TRANSACTIONS, page, limit],
    queryFn: () => getWalletTransactions({ page, limit }),
    keepPreviousData: true,
  });
}

export function useInitiateRecharge() {
  const queryClient = useQueryClient();
  return useMutation<InitiateRechargeResponse, Error, InitiateRechargePayload>({ // 明确泛型参数
    mutationFn: (data: InitiateRechargePayload) => initiateRecharge(data),
    onSuccess: (data) => {
      // 充值请求发起后，后端生成了一个待支付的交易/订单
      // data 中应该包含用于支付的订单ID (e.g., data.transactionId 或 data.rechargeOrderId)
      queryClient.invalidateQueries({ queryKey: [RQ_KEY_WALLET_TRANSACTIONS] }); // 刷新交易记录可以看到待支付的充值
      // 余额此时不会变化，直到支付成功
    },
  });
}

export function usePayWithBalance() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: PayWithBalancePayload) => payOrderWithBalance(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [RQ_KEY_USER_WALLET] });
      queryClient.invalidateQueries({ queryKey: [RQ_KEY_WALLET_TRANSACTIONS] });
      // 可能还需要刷新订单列表，因为订单状态会改变
      queryClient.invalidateQueries({ queryKey: ['orders'] }); // 假设订单列表的 queryKey 是 'orders'
       queryClient.invalidateQueries({ queryKey: ['orderDetails'] }); // 如果有订单详情页
    },
  });
}
```

#### 3.5. UI 组件

1. **余额展示组件 (`BalanceDisplay.tsx`)**: 同上一版。
2. **导航栏余额 (`HeaderBalance.tsx`)**: 同上一版。
3. **钱包管理页面 (`WalletPage.tsx`)**: 同上一版。
4. **交易记录组件 (`TransactionHistory.tsx`)**: 同上一版。
    * 确保正确显示充值类型 (RECHARGE) 和其状态 (PENDING, COMPLETED, FAILED)。
5. **充值区域组件 (`RechargeSection.tsx`)**
    * 优化充值流程：
        * 用户输入金额，选择支付方式。
        * 点击“确认充值”后，调用 `useInitiateRecharge`。
        * 成功后，后端返回一个充值订单ID（例如 `rechargeOrderId` 或 `transactionId`）。
        * **复用支付流程**：拿到此充值订单ID后，调用 `createPayment({ orderId: rechargeOrderId, paymentMethod: selectedActualPaymentGateway })` (类似 `shop-web/src/pages/orders/OrderDetail.tsx` 中的支付逻辑)，获取支付URL/二维码，引导用户支付。
        * 可以提供一个按钮或链接，在充值订单创建后，允许用户“立即支付”。
        * 支付状态的轮询或回调将更新充值订单的状态，并最终更新用户钱包余额。

    ```tsx
    // shop-web/src/pages/dashboard/wallet/RechargeSection.tsx
    import { useState } from "react";
    import { useInitiateRecharge } from "@/hooks/useWallet";
    import { useQuery, useQueryClient } from "@tanstack/react-query";
    import { getPaymentMethods, PaymentMethod } from "@/services/payment-methods";
    import { Button } from "@/components/ui/button";
    import { Input } from "@/components/ui/input";
    import { Label } from "@/components/ui/label";
    import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
    import { toast } from "sonner";
    import { createPayment } from "@/services/payment"; // 通用支付创建服务
    import { useNavigate } from "react-router-dom";
    import { RQ_KEY_USER_WALLET, RQ_KEY_WALLET_TRANSACTIONS } from "@/hooks/useWallet";

    export function RechargeSection() {
      const [amount, setAmount] = useState<number | string>(10);
      const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string | undefined>(undefined); // 存支付方式的ID，如"1" for Alipay, "2" for WeChat
      const initiateRechargeMutation = useInitiateRecharge();
      const createPaymentMutation = useMutation(createPayment); // 用于获取实际支付链接
      const navigate = useNavigate();
      const queryClient = useQueryClient();

      const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useQuery({
        queryKey: ['paymentMethods'],
        queryFn: getPaymentMethods,
      });

      const predefinedAmounts = [10, 50, 100, 200, 500];

      const handleRecharge = async () => {
        const numericAmount = Number(amount);
        if (isNaN(numericAmount) || numericAmount <= 0) {
          toast.error("请输入有效的充值金额");
          return;
        }
        if (!selectedPaymentMethodId) {
          toast.error("请选择支付方式");
          return;
        }

        try {
          // 1. 调用后端 initiateRecharge 创建充值记录/订单
          const rechargeResponse = await initiateRechargeMutation.mutateAsync({
            amount: numericAmount,
            paymentMethod: selectedPaymentMethodId, // 这个是给后端记录的支付方式类型
          });

          // 2. 拿到充值记录的ID (假设为 transactionId 或一个特定的 rechargeOrderId)
          //    并使用这个ID去调用通用的创建支付接口
          const orderIdForPayment = rechargeResponse.transactionId || rechargeResponse.rechargeOrderId;

          if (!orderIdForPayment) {
            toast.error("创建充值请求失败，请重试。");
            return;
          }

          // 3. 调用 createPayment 获取实际支付链接或参数
          const paymentDetails = await createPaymentMutation.mutateAsync({
            orderId: orderIdForPayment,
            paymentMethod: selectedPaymentMethodId, // 这个是支付网关需要的支付方式 (e.g., "1", "2")
          });

          if (paymentDetails.paymentUrl) {
            // 对于在线支付，通常是跳转或打开新窗口
            // 对于扫码支付，可能需要显示二维码
            // 这里假设是跳转
            window.open(paymentDetails.paymentUrl, "_blank");
            toast.info("已打开支付页面，请完成支付。支付成功后余额会自动更新。");
            // 刷新钱包和交易记录，使得待支付的充值显示出来
            queryClient.invalidateQueries({ queryKey: [RQ_KEY_USER_WALLET] });
            queryClient.invalidateQueries({ queryKey: [RQ_KEY_WALLET_TRANSACTIONS] });
             // 可以导航到订单详情页或特定支付结果页，并传递充值订单ID
            navigate(`/orders/${orderIdForPayment}?type=recharge`);
          } else {
            // TODO: 处理其他支付方式，例如显示二维码，或线下支付指引
            if (selectedPaymentMethodId === "3") { // 假设 "3" 是线下支付
                 toast.info("线下充值订单已创建，请按指引完成支付。");
                 navigate(`/orders/${orderIdForPayment}?type=recharge&offline=true`); // 跳转到线下支付指引页
            } else {
                toast.error("无法获取支付信息，请稍后重试。");
            }
          }

        } catch (error: any) {
          toast.error(`充值操作失败: ${error.response?.data?.message || error.message || "未知错误"}`);
        }
      };
      // ... UI (同上一版, 确保 RadioGroupItem 的 value 是支付方式的ID，如 "1", "2", "3") ...
       return (
        <Card className="max-w-lg mx-auto">
          {/* ... CardHeader ... */}
          <CardContent className="space-y-6">
            {/* ... 金额输入和预设金额按钮 ... */}
            <div className="space-y-2">
              <Label>选择支付方式</Label>
              {isLoadingPaymentMethods ? <p>加载支付方式...</p> : (
                <RadioGroup
                  value={selectedPaymentMethodId}
                  onValueChange={setSelectedPaymentMethodId}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  {paymentMethods?.filter(pm => pm.isEnabled).map((pm) => (
                    <Label
                      key={pm.id} // pm.id 应该是 "1", "2", "3" 等
                      htmlFor={`payment-${pm.id}`}
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer"
                    >
                       <RadioGroupItem value={pm.id} id={`payment-${pm.id}`} className="sr-only" />
                      <div className="flex items-center gap-2 mb-1">
                        {typeof pm.icon === 'string' && pm.icon.startsWith('http') ? (
                          <img src={pm.icon} alt={pm.name} className="h-6 w-6" />
                        ) : pm.icon /* 这里可以放SVG图标组件 */ }
                        <span className="font-semibold">{pm.name}</span>
                      </div>
                      {pm.description && <span className="text-xs text-muted-foreground">{pm.description}</span>}
                    </Label>
                  ))}
                </RadioGroup>
              )}
            </div>
            <Button
              onClick={handleRecharge}
              disabled={initiateRechargeMutation.isPending || createPaymentMutation.isPending || isLoadingPaymentMethods}
              className="w-full"
              size="lg"
            >
              {initiateRechargeMutation.isPending || createPaymentMutation.isPending ? "处理中..." : `确认充值 $${Number(amount).toFixed(2)}`}
            </Button>
             {/* ... */}
          </CardContent>
        </Card>
       );
    }
    ```

#### 3.6. 路由配置

同上一版，确保钱包页面路由 `/dashboard/wallet` 可访问。

#### 3.7. 余额支付集成

在订单确认和支付页面 (例如 `CompleteOrder.tsx` 或 `StaticIPCheckout.tsx`)：

1. **获取用户余额**：使用 `useUserWallet` hook。
2. **显示余额支付选项**：
    * 如果余额足够支付订单总额，显示 "使用余额支付 (余额: $X.XX)" 按钮。
    * **优化：** 如果余额不足，除了提示外，可以提供一个**快捷充值按钮/链接**，点击后可以弹出一个简化的充值模态框，或者直接跳转到钱包充值页面并预填一个推荐的充值金额（例如订单差额）。
3. **处理余额支付**：
    * 点击余额支付后，调用 `usePayWithBalance` mutation，使用修正后的API路径 `/api/shop/wallet/pay`。
    * 成功后，订单状态变为已支付，并跳转到订单详情或成功页面。

```tsx
// 示例: shop-web/src/pages/orders/CompleteOrder.tsx (或类似支付页面)
// ... (其他 imports)
import { useUserWallet, usePayWithBalance } from "@/hooks/useWallet";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom"; // For navigation
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"; // For quick recharge modal
// import { RechargeSection } from "@/pages/dashboard/wallet/RechargeSection"; // If using a modal with the component

// 在组件内部
const { data: wallet, isLoading: isLoadingWallet } = useUserWallet();
const payWithBalanceMutation = usePayWithBalance();
const navigate = useNavigate();
// const orderTotalPrice = /* ... 从订单数据中获取订单总价 ... */;
// const orderId = /* ... 从订单数据中获取订单ID ... */;

const handlePayWithBalance = async () => {
  if (!wallet || !orderId || typeof orderTotalPrice === 'undefined') return;

  // 确保 orderId 是 string 类型
  const currentOrderId = String(orderId);

  if (parseFloat(wallet.balance) < orderTotalPrice) {
    toast.error("账户余额不足以支付此订单。");
    return;
  }

  try {
    // API路径已修正为 /api/shop/wallet/pay，通过 payOrderWithBalance 调用
    // 假设 payOrderWithBalance 内部已适配后端，只需要 orderId
    await payWithBalanceMutation.mutateAsync({ orderId: currentOrderId });
    toast.success("订单已使用余额支付成功！");
    navigate(`/orders/${currentOrderId}?payment_status=success`); // 跳转到订单详情或支付成功页
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || "未知错误";
    toast.error(`余额支付失败: ${errorMessage}`);
  }
};

// 在支付方式选择区域添加:
// {isLoadingWallet && <p>正在加载钱包信息...</p>}
// {wallet && (
//   <div className="my-4 p-4 border rounded-md">
//     <h3 className="font-semibold mb-2">余额支付</h3>
//     <p className="text-sm mb-3">
//       当前总余额: <span className="font-bold">${parseFloat(wallet.balance).toFixed(2)}</span>
//     </p>
//     {parseFloat(wallet.balance) >= orderTotalPrice ? (
//       <Button
//         onClick={handlePayWithBalance}
//         disabled={payWithBalanceMutation.isPending}
//         className="w-full bg-green-500 hover:bg-green-600"
//       >
//         {payWithBalanceMutation.isPending ? "处理中..." : `使用余额支付 $${orderTotalPrice.toFixed(2)}`}
//       </Button>
//     ) : (
//       <div>
//         <p className="text-sm text-orange-500 mb-2">
//           余额不足，还需充值 $${(orderTotalPrice - parseFloat(wallet.balance)).toFixed(2)}。
//         </p>
//         <Button
//           onClick={() => navigate(`/dashboard/wallet?tab=recharge&amount=${(orderTotalPrice - parseFloat(wallet.balance)).toFixed(2)}`)} // 跳转并预填金额
//           variant="outline"
//           className="w-full"
//         >
//           前往充值
//         </Button>
//         {/* 或者使用 Dialog 实现快捷充值
//         <Dialog>
//           <DialogTrigger asChild>
//             <Button variant="outline" className="w-full">快捷充值</Button>
//           </DialogTrigger>
//           <DialogContent>
//             <DialogHeader><DialogTitle>快速充值</DialogTitle></DialogHeader>
//             <RechargeSection /> // 嵌入充值组件
//           </DialogContent>
//         </Dialog>
//         */}
//       </div>
//     )}
//   </div>
// )}
```

### 4. 总结与注意事项 - 修订版

* **后端API确认：**
    * **充值流程API (`/api/shop/wallet/recharge`)**：确认此接口的确是创建了一个“充值订单”或“待支付交易”，并返回一个 `orderId` (或 `transactionId`) 给前端，用于后续通过标准支付流程（如调用 `/api/shop/orders/:orderId/payment-url` 或 `/api/shop/payment/create`）完成支付。
    * **余额支付API (`/api/shop/wallet/pay`)**：确认此接口仅需 `orderId`。如果还需要 `amount`，前端需相应传递。
* **充值复用支付流程：** 充值操作在前端发起后，应该得到一个充值订单ID。然后，前端使用这个ID和用户选择的支付方式，调用现有的 `createPayment` 服务来获取支付链接/参数，并引导用户完成支付。支付成功后的回调或轮询机制会更新这个充值订单的状态，进而触发后端逻辑更新用户钱包余额。
* **余额不足引导：** 当用户在商品订单支付环节余额不足时，应清晰提示差额，并提供便捷的充值入口（例如直接跳转到预填金额的充值页面，或弹出充值模态框）。
* **错误处理与加载状态：** 保持健壮的错误处理和清晰的加载状态反馈。
* **状态同步：** 充值或支付成功后，务必使用 `queryClient.invalidateQueries` 刷新钱包、交易记录、订单列表等相关数据，确保用户界面实时更新。
* **安全性、货币格式化、组件复用、响应式设计：** 这些方面的重要性不变。

---

以上是修订后的开发文档。关键在于理解充值也是一种需要支付的“交易”或“订单”，因此可以最大限度地复用现有的支付基础设施，而不是为充值单独实现一套支付逻辑。
