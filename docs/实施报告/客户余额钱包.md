# 客户余额余额

**文档版本**: 1.0
**更新日期**: 2025-06-13
**状态**: 待实施

## 一、核心目标

为平台引入用户余额系统，支持操作员为用户进行多种方式的充值（包括赠送），并允许用户使用余额进行消费。本版本采用数据库事务 + 乐观锁版本号的简化并发控制方案，提高系统的可维护性和扩展性。

## 二、数据库层面调整

### 2.1 `shop_user_wallet` (用户余额表) 修改

```sql
-- 第一步：添加新字段
ALTER TABLE `shop_user_wallet`
ADD COLUMN `recharge_balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '充值余额（用户实际支付的金额）' AFTER `balance`,
ADD COLUMN `bonus_balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '赠送余额（活动、补偿、代理商协商等获得的金额）' AFTER `recharge_balance`,
ADD COLUMN `version` INT NOT NULL DEFAULT 0 COMMENT '版本号（乐观锁）' AFTER `remark`,
ADD UNIQUE INDEX `uk_customer_id` (`customer_id`) COMMENT '确保每个客户只有一个余额';

-- 第二步：创建触发器确保余额一致性
DELIMITER $$
CREATE TRIGGER `trg_wallet_balance_insert` BEFORE INSERT ON `shop_user_wallet`
FOR EACH ROW
BEGIN
    SET NEW.balance = NEW.recharge_balance + NEW.bonus_balance;
END$$

CREATE TRIGGER `trg_wallet_balance_update` BEFORE UPDATE ON `shop_user_wallet`
FOR EACH ROW
BEGIN
    SET NEW.balance = NEW.recharge_balance + NEW.bonus_balance;
    -- 乐观锁版本号由应用层控制
END$$
DELIMITER ;

-- 第三步：数据迁移（需要根据实际业务情况调整）
-- UPDATE `shop_user_wallet` SET `recharge_balance` = `balance`, `bonus_balance` = 0.00 WHERE `recharge_balance` = 0;
```

### 2.2 `shop_transaction` (交易记录表) 修改

```sql
ALTER TABLE `shop_transaction`
ADD COLUMN `recharge_component_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '交易中的充值部分金额' AFTER `amount`,
ADD COLUMN `bonus_component_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '交易中的赠送部分金额' AFTER `recharge_component_amount`,
ADD COLUMN `operator_user_id` INT NULL COMMENT '操作员ID（可能是管理员、代理商或其他角色）' AFTER `related_order`,
ADD COLUMN `balance_before` DECIMAL(10,2) NULL COMMENT '交易前余额快照' AFTER `operator_user_id`,
ADD COLUMN `balance_after` DECIMAL(10,2) NULL COMMENT '交易后余额快照' AFTER `balance_before`,
ADD INDEX `idx_customer_create_time` (`customer_id`, `create_time`),
ADD INDEX `idx_operator_user` (`operator_user_id`),
ADD INDEX `idx_transaction_status` (`transaction_status`);

-- 添加外键约束（可选，根据实际情况决定）
-- ALTER TABLE `shop_transaction`
-- ADD CONSTRAINT `fk_transaction_operator`
-- FOREIGN KEY (`operator_user_id`) REFERENCES `sys_user`(`user_id`)
-- ON DELETE SET NULL ON UPDATE CASCADE;
```

### 2.3 新增余额变动日志表（审计用途）

```sql
CREATE TABLE `shop_wallet_change_log` (
  `log_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `wallet_id` INT NOT NULL COMMENT '余额ID',
  `customer_id` INT NOT NULL COMMENT '客户ID',
  `transaction_id` VARCHAR(64) NULL COMMENT '关联交易ID',
  `change_type` CHAR(1) NOT NULL COMMENT '变动类型（1增加 2减少）',
  `balance_type` VARCHAR(20) NOT NULL COMMENT '余额类型（recharge/bonus）',
  `change_amount` DECIMAL(10,2) NOT NULL COMMENT '变动金额',
  `before_balance` DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
  `after_balance` DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
  `operator_id` INT NULL COMMENT '操作者ID',
  `ip_address` VARCHAR(45) NULL COMMENT '操作IP地址',
  `user_agent` VARCHAR(255) NULL COMMENT '用户代理',
  `create_time` DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_wallet_id` (`wallet_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额余额变动日志表';
```

## 三、后端 API 及服务层调整

### 3.1 系统模块 - 操作员操作

#### Controller 接口设计

```typescript
// server/src/module/system/wallet/wallet.controller.ts

@Controller('wallet')
@ApiTags('系统-余额管理')
export class SystemWalletController {

  /**
   * 操作员为客户充值
   */
  @Post('customer/:customerId/recharge')
  @RequirePermission('system:wallet:recharge')
  @OperLog({ title: '操作员充值' })
  async operatorRecharge(
    @Param('customerId') customerId: number,
    @Body() dto: OperatorRechargeDto,
    @User() user: UserInfoDto
  ) {
    return await this.walletService.operatorRecharge(user.userId, customerId, dto);
  }

  /**
   * 查询客户余额信息
   */
  @Get('customer/:customerId')
  @RequirePermission('customer:wallet:query')
  async getCustomerWallet(@Param('customerId') customerId: number) {
    return await this.walletService.getCustomerWallet(customerId);
  }

  /**
   * 查询充值记录列表
   */
  @Get('recharge-list')
  @RequirePermission('customer:wallet:query')
  async getRechargeList(@Query() query: RechargeListDto) {
    return await this.walletService.getRechargeList(query);
  }

  /**
   * 获取余额统计数据
   */
  @Get('statistics')
  @RequirePermission('customer:wallet:query')
  async getStatistics(@Query() query: StatisticsQueryDto) {
    return await this.walletService.getStatistics(query);
  }
}
```

#### DTO 定义

```typescript
// server/src/module/system/wallet/dto/operator-recharge.dto.ts

export class OperatorRechargeDto {
  @IsNumber()
  @Min(0.01)
  @ApiProperty({ description: '充值金额' })
  rechargeAmount: number;

  @IsNumber()
  @Min(0)
  @ApiProperty({ description: '赠送金额' })
  bonusAmount: number;

  @IsEnum(['OPERATOR_DIRECT', 'OPERATOR_OFFLINE_CONFIRMED', 'OPERATOR_OFFLINE_PRIORITY', 'OPERATOR_ONLINE_QR'])
  @ApiProperty({ description: '支付方式' })
  paymentMethod: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  @ApiProperty({ description: '备注', required: false })
  remarks?: string;
}
```

#### Service 实现（核心逻辑）

```typescript
// server/src/module/system/wallet/wallet.service.ts

@Injectable()
export class SystemWalletService {
  constructor(
    @InjectRepository(ShopUserWallet)
    private walletRepository: Repository<ShopUserWallet>,
    @InjectRepository(ShopTransaction)
    private transactionRepository: Repository<ShopTransaction>,
    @InjectRepository(ShopWalletChangeLog)
    private changeLogRepository: Repository<ShopWalletChangeLog>,
    private dataSource: DataSource,
  ) {}

  /**
   * 操作员充值（使用数据库事务 + 乐观锁版本号）
   */
  async operatorRecharge(operatorUserId: number, customerId: number, dto: OperatorRechargeDto) {
    const maxRetries = 3; // 最大重试次数
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        return await this.dataSource.transaction(async manager => {
          // 1. 获取或创建余额
          let wallet = await manager.findOne(ShopUserWallet, {
            where: { customerId }
          });

          if (!wallet) {
            wallet = manager.create(ShopUserWallet, {
              customerId,
              rechargeBalance: 0,
              bonusBalance: 0,
              balance: 0,
              frozenAmount: 0,
              status: '0',
              version: 0
            });
            await manager.save(wallet);
          }

          // 2. 记录当前版本号和变动前余额
          const currentVersion = wallet.version;
          const beforeBalance = {
            total: wallet.balance,
            recharge: wallet.rechargeBalance,
            bonus: wallet.bonusBalance
          };

          // 3. 计算新余额
          const newRechargeBalance = Number(wallet.rechargeBalance) + Number(dto.rechargeAmount);
          const newBonusBalance = Number(wallet.bonusBalance) + Number(dto.bonusAmount);
          const newVersion = currentVersion + 1;

          // 4. 使用乐观锁更新余额（WHERE 条件包含版本号）
          const updateResult = await manager.update(ShopUserWallet,
            { walletId: wallet.walletId, version: currentVersion },
            {
              rechargeBalance: newRechargeBalance,
              bonusBalance: newBonusBalance,
              version: newVersion,
              updateTime: new Date()
            }
          );

          // 检查是否更新成功（乐观锁冲突检测）
          if (updateResult.affected === 0) {
            throw new Error('OPTIMISTIC_LOCK_CONFLICT');
          }

          // 5. 获取更新后的余额信息（触发器已自动计算 balance）
          const updatedWallet = await manager.findOne(ShopUserWallet, {
            where: { walletId: wallet.walletId }
          });

          // 6. 创建交易记录
          const transactionId = this.generateTransactionId();
          const transaction = manager.create(ShopTransaction, {
            transactionId,
            customerId,
            walletId: wallet.walletId,
            transactionType: '1', // 充值
            amount: dto.rechargeAmount + dto.bonusAmount,
            rechargeComponentAmount: dto.rechargeAmount,
            bonusComponentAmount: dto.bonusAmount,
            paymentMethod: dto.paymentMethod,
            transactionStatus: '1', // 成功
            operatorUserId: operatorUserId,
            balanceBefore: beforeBalance.total,
            balanceAfter: updatedWallet.balance,
            description: `操作员充值：充值${dto.rechargeAmount}元，赠送${dto.bonusAmount}元`,
            remark: dto.remarks
          });
          await manager.save(transaction);

          // 7. 记录变动日志
          if (dto.rechargeAmount > 0) {
            await this.saveChangeLog(manager, {
              wallet: updatedWallet,
              transactionId,
              changeType: '1',
              balanceType: 'recharge',
              changeAmount: dto.rechargeAmount,
              beforeBalance: beforeBalance.recharge,
              afterBalance: newRechargeBalance,
              operatorId: operatorUserId
            });
          }

          if (dto.bonusAmount > 0) {
            await this.saveChangeLog(manager, {
              wallet: updatedWallet,
              transactionId,
              changeType: '1',
              balanceType: 'bonus',
              changeAmount: dto.bonusAmount,
              beforeBalance: beforeBalance.bonus,
              afterBalance: newBonusBalance,
              operatorId: operatorUserId
            });
          }

          return {
            transactionId,
            customerId,
            totalAmount: dto.rechargeAmount + dto.bonusAmount,
            currentBalance: updatedWallet.balance
          };
        });
      } catch (error) {
        if (error.message === 'OPTIMISTIC_LOCK_CONFLICT') {
          attempt++;
          if (attempt >= maxRetries) {
            throw new BusinessException('系统繁忙，请稍后再试');
          }
          // 随机延迟后重试
          await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
          continue;
        }
        throw error;
      }
    }
  }

  /**
   * 保存余额变动日志
   */
  private async saveChangeLog(manager: EntityManager, logData: any) {
    const log = manager.create(ShopWalletChangeLog, {
      walletId: logData.wallet.walletId,
      customerId: logData.wallet.customerId,
      transactionId: logData.transactionId,
      changeType: logData.changeType,
      balanceType: logData.balanceType,
      changeAmount: logData.changeAmount,
      beforeBalance: logData.beforeBalance,
      afterBalance: logData.afterBalance,
      operatorId: logData.operatorId
    });
    await manager.save(log);
  }

  /**
   * 生成唯一交易ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    return `TXN${timestamp}${random}`;
  }
}
```

### 3.2 商城模块 - 用户操作

#### 余额支付服务（优先扣除赠送余额）

```typescript
// server/src/module/shop/wallet/wallet.service.ts

/**
 * 使用余额支付订单
 */
async payWithBalance(customerId: number, orderId: string, amount: number): Promise<PaymentResult> {
  const maxRetries = 3;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      return await this.dataSource.transaction(async manager => {
        // 1. 获取余额
        const wallet = await manager.findOne(ShopUserWallet, {
          where: { customerId }
        });

        if (!wallet) {
          throw new BusinessException('余额不存在');
        }

        if (wallet.status !== '0') {
          throw new BusinessException('余额已冻结');
        }

        const totalBalance = Number(wallet.balance);
        if (totalBalance < amount) {
          throw new BusinessException('余额不足');
        }

        // 2. 计算扣款顺序（优先扣除赠送余额）
        let deductBonus = 0;
        let deductRecharge = 0;

        if (wallet.bonusBalance >= amount) {
          deductBonus = amount;
        } else {
          deductBonus = Number(wallet.bonusBalance);
          deductRecharge = amount - deductBonus;
        }

        // 3. 记录当前版本号和扣款前余额
        const currentVersion = wallet.version;
        const beforeBalance = {
          total: wallet.balance,
          recharge: wallet.rechargeBalance,
          bonus: wallet.bonusBalance
        };

        // 4. 计算新余额
        const newBonusBalance = Number(wallet.bonusBalance) - deductBonus;
        const newRechargeBalance = Number(wallet.rechargeBalance) - deductRecharge;
        const newVersion = currentVersion + 1;

        // 5. 使用乐观锁更新余额
        const updateResult = await manager.update(ShopUserWallet,
          { walletId: wallet.walletId, version: currentVersion },
          {
            bonusBalance: newBonusBalance,
            rechargeBalance: newRechargeBalance,
            version: newVersion,
            updateTime: new Date()
          }
        );

        if (updateResult.affected === 0) {
          throw new Error('OPTIMISTIC_LOCK_CONFLICT');
        }

        // 6. 获取更新后的余额信息
        const updatedWallet = await manager.findOne(ShopUserWallet, {
          where: { walletId: wallet.walletId }
        });

        // 7. 创建交易记录
        const transactionId = this.generateTransactionId();
        const transaction = manager.create(ShopTransaction, {
          transactionId,
          customerId,
          walletId: wallet.walletId,
          transactionType: '2', // 消费
          amount: amount,
          rechargeComponentAmount: deductRecharge,
          bonusComponentAmount: deductBonus,
          paymentMethod: 'BALANCE',
          transactionStatus: '1',
          relatedOrder: orderId,
          balanceBefore: beforeBalance.total,
          balanceAfter: updatedWallet.balance,
          description: `订单支付：${orderId}`
        });
        await manager.save(transaction);

        // 8. 记录变动日志
        if (deductBonus > 0) {
          await this.saveChangeLog(manager, {
            wallet: updatedWallet,
            transactionId,
            changeType: '2',
            balanceType: 'bonus',
            changeAmount: deductBonus,
            beforeBalance: beforeBalance.bonus,
            afterBalance: newBonusBalance,
            operatorId: customerId
          });
        }

        if (deductRecharge > 0) {
          await this.saveChangeLog(manager, {
            wallet: updatedWallet,
            transactionId,
            changeType: '2',
            balanceType: 'recharge',
            changeAmount: deductRecharge,
            beforeBalance: beforeBalance.recharge,
            afterBalance: newRechargeBalance,
            operatorId: customerId
          });
        }

        return {
          success: true,
          transactionId,
          paidAmount: amount,
          remainingBalance: updatedWallet.balance
        };
      });
    } catch (error) {
      if (error.message === 'OPTIMISTIC_LOCK_CONFLICT') {
        attempt++;
        if (attempt >= maxRetries) {
          throw new BusinessException('系统繁忙，请稍后再试');
        }
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
        continue;
      }
      throw error;
    }
  }
}
```

## 四、前端界面实现

### 4.1 管理后台充值弹窗

```vue
<!-- admin-web/src/views/system/wallet/RechargeDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="创建充值订单"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="客户名称">
        <el-input :value="customerName" disabled />
      </el-form-item>

      <el-form-item label="充值金额" prop="rechargeAmount">
        <el-input-number
          v-model="form.rechargeAmount"
          :min="0"
          :precision="2"
          :step="100"
          @change="calculateTotal"
        />
        <span class="ml-2">元</span>
      </el-form-item>

      <el-form-item label="赠送金额" prop="bonusAmount">
        <el-input-number
          v-model="form.bonusAmount"
          :min="0"
          :precision="2"
          :step="10"
          @change="calculateTotal"
        />
        <span class="ml-2">元</span>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <el-divider />

      <el-form-item>
        <div class="summary">
          <div>实际支付金额：<span class="text-primary">{{ form.rechargeAmount }} 元</span></div>
          <div>实际到账金额：<span class="text-success">{{ totalAmount }} 元</span></div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        确认充值
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { operatorRecharge } from '@/api/system/wallet';
import { ElMessage } from 'element-plus';

const props = defineProps({
  customerId: Number,
  customerName: String
});

const emit = defineEmits(['success']);

const visible = ref(false);
const loading = ref(false);
const formRef = ref();

const form = ref({
  rechargeAmount: 0,
  bonusAmount: 0,
  paymentMethod: 'OPERATOR_DIRECT',
  remarks: ''
});

const rules = {
  rechargeAmount: [
    { required: true, message: '请输入充值金额' },
    { type: 'number', min: 0.01, message: '充值金额必须大于0' }
  ],
  bonusAmount: [
    { type: 'number', min: 0, message: '赠送金额不能为负数' }
  ]
};

const totalAmount = computed(() => {
  return (form.value.rechargeAmount + form.value.bonusAmount).toFixed(2);
});

const handleSubmit = async () => {
  const valid = await formRef.value.validate();
  if (!valid) return;

  loading.value = true;
  try {
    await operatorRecharge(props.customerId, form.value);
    ElMessage.success('充值成功');
    visible.value = false;
    emit('success');
  } catch (error) {
    ElMessage.error(error.message || '充值失败');
  } finally {
    loading.value = false;
  }
};

// 暴露方法供父组件调用
defineExpose({
  open() {
    visible.value = true;
    form.value = {
      rechargeAmount: 0,
      bonusAmount: 0,
      paymentMethod: 'OPERATOR_DIRECT',
      remarks: ''
    };
  }
});
</script>
```

### 4.2 商城前端余额页面

```tsx
// shop-web/src/pages/wallet/index.tsx
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui';
import { getMyWallet, getTransactionList } from '@/services/wallet';

export default function WalletPage() {
  const [activeTab, setActiveTab] = useState('overview');

  const { data: wallet } = useQuery({
    queryKey: ['wallet'],
    queryFn: getMyWallet
  });

  const { data: transactions } = useQuery({
    queryKey: ['transactions'],
    queryFn: () => getTransactionList({ pageNum: 1, pageSize: 20 })
  });

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">我的余额</h1>

      <Card className="mb-6 bg-gradient-to-r from-purple-500 to-blue-500 text-white">
        <div className="p-6">
          <div className="text-sm opacity-90">总余额</div>
          <div className="text-3xl font-bold mt-2">
            ¥ {wallet?.balance || '0.00'}
          </div>
          <div className="grid grid-cols-2 gap-4 mt-6">
            <div>
              <div className="text-sm opacity-90">充值余额</div>
              <div className="text-xl font-semibold">
                ¥ {wallet?.rechargeBalance || '0.00'}
              </div>
            </div>
            <div>
              <div className="text-sm opacity-90">赠送余额</div>
              <div className="text-xl font-semibold">
                ¥ {wallet?.bonusBalance || '0.00'}
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">余额明细</TabsTrigger>
          <TabsTrigger value="transactions">交易记录</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions">
          <TransactionList transactions={transactions?.rows || []} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## 五、并发控制策略（简化版）

### 5.1 核心机制

1. **数据库事务**：所有余额操作都在事务中进行，确保原子性
2. **乐观锁版本号**：使用 `version` 字段防止并发更新冲突
3. **重试机制**：乐观锁冲突时自动重试，最多重试3次

### 5.2 优势

- **简化实现**：去掉了复杂的 Redis 分布式锁
- **高性能**：乐观锁在大多数情况下性能更好
- **易维护**：减少外部依赖，降低系统复杂度

### 5.3 适用场景

- 并发度不是特别高的业务场景
- 冲突概率较低的操作
- 追求简洁性和易维护性

## 六、安全与性能优化

### 6.1 性能优化

1. **索引优化**：为高频查询字段添加索引
2. **触发器优化**：使用触发器自动维护余额总和
3. **查询优化**：避免不必要的关联查询

### 6.2 安全措施

1. **权限控制**：使用 RBAC 权限体系
2. **操作审计**：详细记录所有余额变动
3. **数据校验**：严格验证输入参数

## 七、测试方案

### 7.1 单元测试

```typescript
// 余额充值测试用例
describe('WalletService', () => {
  it('should recharge successfully', async () => {
    const result = await service.operatorRecharge(1, 100, {
      rechargeAmount: 100,
      bonusAmount: 10,
      paymentMethod: 'OPERATOR_DIRECT'
    });

    expect(result.totalAmount).toBe(110);
  });

  it('should handle optimistic lock conflict', async () => {
    // 模拟乐观锁冲突
    const promises = Array(5).fill(0).map(() =>
      service.operatorRecharge(1, 100, {
        rechargeAmount: 10,
        bonusAmount: 0,
        paymentMethod: 'OPERATOR_DIRECT'
      })
    );

    await Promise.all(promises);
    const wallet = await service.getCustomerWallet(100);
    expect(wallet.rechargeBalance).toBe(50);
  });
});
```

### 7.2 集成测试

1. 充值流程测试
2. 消费流程测试
3. 并发场景测试
4. 异常恢复测试

## 八、部署计划

### 8.1 数据库迁移

```bash
# 1. 备份现有数据
mysqldump -u root -p proxy_shop > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行表结构修改
mysql -u root -p proxy_shop < wallet_v2_migration.sql

# 3. 验证数据完整性
mysql -u root -p proxy_shop -e "SELECT COUNT(*) FROM shop_user_wallet; SELECT COUNT(*) FROM shop_transaction;"
```

### 8.2 上线步骤

1. **第一阶段**：内部测试环境验证
2. **第二阶段**：灰度发布，部分用户
3. **第三阶段**：全量发布

### 8.3 回滚方案

```sql
-- 回滚脚本
ALTER TABLE `shop_user_wallet`
DROP COLUMN `recharge_balance`,
DROP COLUMN `bonus_balance`,
DROP COLUMN `version`;

ALTER TABLE `shop_transaction`
DROP COLUMN `recharge_component_amount`,
DROP COLUMN `bonus_component_amount`,
DROP COLUMN `operator_user_id`,
DROP COLUMN `balance_before`,
DROP COLUMN `balance_after`;

DROP TABLE IF EXISTS `shop_wallet_change_log`;
```

## 九、监控与告警

### 9.1 关键指标监控

1. 余额变动频率
2. 乐观锁冲突率
3. 交易成功率
4. 系统响应时间

### 9.2 告警规则

```yaml
# 告警配置示例
alerts:
  - name: high_optimistic_lock_conflict
    condition: conflict_rate > 5%
    action: notify_admin

  - name: large_amount_transaction
    condition: amount > 10000
    action: manual_review

  - name: balance_inconsistency
    condition: balance != recharge_balance + bonus_balance
    action: emergency_alert
```

## 十、总结

### 10.1 主要改进

1. **简化并发控制**：使用数据库事务 + 乐观锁替代分布式锁
2. **角色扩展**：操作员概念替代管理员，支持更多角色
3. **易于维护**：减少外部依赖，降低系统复杂度
4. **性能优化**：乐观锁在低冲突场景下性能更好

### 10.2 注意事项

1. **冲突处理**：需要合理设置重试次数和延迟
2. **数据一致性**：依赖触发器维护余额总和
3. **监控重要**：需要监控乐观锁冲突率
4. **扩展性**：未来如需要更复杂的并发控制，可升级为分布式锁

---

**文档维护**：本文档应随系统演进持续更新，确保与实际实现保持一致。
