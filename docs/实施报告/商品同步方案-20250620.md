
**1. 理解现状和痛点**

* **现状**：
    * `ProductSyncService` 负责从供应商同步产品数据到 `shop_product` 表。
    * `shop_product` 表中已经有 `is_price_manual` 字段来控制价格是否手动设置。
    * `product_config_details` (JSON类型) 存储了一些特定于产品的配置，比如供应商的 `proxyType`, `proxiesFormat`, `purposeWeb`。
    * 同步时，除了价格（根据 `is_price_manual` 判断），其他信息如 `product_name`, `product_desc` 等容易被供应商数据覆盖。
* **痛点**：
    * 运营人员修改的商品名称、描述等优化信息，在下次同步时可能丢失。

**3. 核心解决思路：引入“同步策略”和“数据所有权”概念**

我们的目标是：**允许平台对商品信息进行优化和控制，同时确保关键的、供应商权威的数据（如库存、成本）能够准确同步。**

* **数据所有权划分**：
    * **供应商权威数据**：库存 (`available_count`)、成本价 (`cost_price`, `cost_price_currency`)、供应商产品ID (`external_product_id`)、供应商原始配置参数（如 `purpose_web`, `proxies_format`，这些应存储在 `product_config_details` 中，作为产品的“技术规格”）。这些数据应优先从供应商同步。
    * **平台权威数据**：商品名称 (`product_name`)、产品描述 (`product_desc`)、销售价格 (`price`, `discount_price`, `currency`)、产品图片 (`image_url`)、排序 (`sort_order`)、上架状态 (`status`)、SEO相关字段（如果未来有）。这些数据一旦由平台运营人员修改，应受到保护。

* **引入 `sync_strategy` 字段**：
    在 `shop_product` 表中增加一个字段，例如 `sync_strategy` (VARCHAR 或 ENUM)，用于定义该产品的同步策略。这比为每个字段加 `is_manual` 标志更简洁。
    可选值可以包括：
    * `FULL_SYNC`: 完全以供应商数据为准，覆盖平台上的所有对应字段。适用于初次导入或希望完全重置为供应商数据的情况。
    * `STOCK_AND_COST_ONLY`: **这是关键策略**。只同步供应商的库存和成本价。平台维护的名称、描述、销售价格等信息保持不变。
    * `MANUAL_OVERRIDE` : 不自动同步该产品的内容信息，只同步库存NC_DISABLED`): 完全停止同步此商品。

**4. 详细设计和代码修改建议**

* **数据库修改** (`shop_product` 表):
    1. 添加 `sync_strategy VARCHAR(30) DEFAULT 'PLATFORM_PRIORITY_CONTENT'` COMMENT '同步策略'.
    2. 删除`is_price_manual TINYINT(1) DEFAULT 0` COMMENT '是否手动设置价格'.

* **`ProductSyncService` 修改** (   r`server/src/module/system/products/services/product-sync.service.ts`):
    * 在 `createOrUpdateProductFromInventory` 方法（或类似的核心同步逻辑处）进行修改：

        ```typescript
        // ... 伪代码 ...
        async createOrUpdateProductFromInventory(provider, inventoryItem, ...) {
            // ... 获取供应商数据 mappedSupplierData ...
            let existingProduct = await this.productRepo.findOne({ where: { external_product_id: ..., provider_id: ... } });

            if (existingProduct) {
                // 更新逻辑
                const updatePayload: Partial<Product> = {
                    // 必须同步的字段
                    available_count: mappedSupplierData.stockCount, // 库存
                    cost_price: mappedSupplierData.costPrice, // 成本
                    cost_price_currency: mappedSupplierData.costPriceCurrency,
                    last_sync_time: new Date(),
                    // ... 其他基础信息如 supplier_raw_data_snapshot ...
                };

                // 根据同步策略决定其他字段的更新
                switch (existingProduct.sync_strategy) {
                    case 'FULL_SYNC':
                        updatePayload.product_name = mappedSupplierData.productName;
                        updatePayload.product_desc = mappedSupplierData.productDescription;
                        if (!existingProduct.is_price_manual) { // 仅当价格不是手动设置时
                           updatePayload.price = mappedSupplierData.calculatedPrice; // 平台计算后的售价
                           // updatePayload.discount_price = ...
                        }
                        // ... 其他所有可从供应商映射的字段 ...
                        updatePayload.product_config_details = { // 保留关键技术规格
                            ...(existingProduct.product_config_details || {}),
                            proxyType: mappedSupplierData.proxyType,
                            proxiesFormat: mappedSupplierData.proxiesFormat,
                            purposeWeb: mappedSupplierData.purposeWeb
                        };
                        break;

                    case 'STOCK_AND_COST_ONLY': // 平台内容优先
                        // 只更新库存和成本，其他不动
                        // 销售价格的更新仍由 is_price_manual 控制
                        if (!existingProduct.is_price_manual) {
                           updatePayload.price = mappedSupplierData.calculatedPrice;
                           // updatePayload.discount_price = ...
                        }
                        // 对于 product_config_details，只更新技术规格，不覆盖运营添加的内容
                        updatePayload.product_config_details = {
                            ...(existingProduct.product_config_details || {}), // 保留平台可能已添加的配置
                            proxyType: mappedSupplierData.proxyType,
                            proxiesFormat: mappedSupplierData.proxiesFormat,
                            purposeWeb: mappedSupplierData.purposeWeb
                        };
                        break;

                    case 'MANUAL_OVERRIDE':
                         // 可能只更新库存，其他都不动，或者根据更细致的规则
                         if (!existingProduct.is_price_manual) { // 价格是否手动也受控
                           updatePayload.price = mappedSupplierData.calculatedPrice;
                         }
                        // product_config_details 也可能不更新或选择性更新
                        break;

                    case 'DISABLED':
                        this.logger.log(`Product ID ${existingProduct.product_id} sync is disabled. Skipping.`);
                        return existingProduct; // 不做任何更新
                }
                // 合并更新
                await this.productRepo.update(existingProduct.product_id, updatePayload);
                existingProduct = { ...existingProduct, ...updatePayload };

            } else {
                // 创建新产品逻辑
                const newProductData = {
                    ...mappedSupplierData, // 包含名称、描述等从供应商来的初始值
                    sync_strategy: 'PLATFORM_PRIORITY_CONTENT', // 新产品默认平台内容优先，允许运营修改
                    is_price_manual: false, // 新产品默认自动定价
                    // ... 其他字段 ...
                };
                existingProduct = await this.productRepo.save(newProductData);
            }
            return existingProduct;
        }
        ```

* **Admin Web UI 修改** (`admin-web/src/views/shop/product/edit.vue` 和 `management.vue`):
    1. **编辑页 (`edit.vue`)**：
        * 增加 “同步策略” (`sync_strategy`) 的选择器（下拉框）。
        * 当运营人员修改了受保护的字段（如名称、描述）时，如果当前策略是 `FULL_SYNC`，可以提示用户是否要将策略更改为 `PLATFORM_PRIORITY_CONTENT` 以保护他们的修改。

    2. **管理页 (`management.vue`)**：
        * 表格中增加一列显示当前产品的“同步策略”。
        * 增加批量修改产品“同步策略”的功能。
        * 提供一个“强制从供应商同步”的按钮（选中产品后），该操作会将选中产品的 `sync_strategy` 临时（或永久，根据需求）设置为 `FULL_SYNC`，然后触发一次同步。

* **关于 `product_config_details`**：
    * `purpose_web`, `proxyType`, `proxiesFormat` 这些来自供应商的、定义产品技术规格的参数，应该在首次同步创建产品时，从供应商的 `inventoryItem`（如 `ShopProviderIpInventory`）中提取，并存入 `product_config_details`。
    * 例如：`product_config_details: { "supplierOriginal": { "proxyType": "...", "purposeWeb": "..." }, "platformOverrides": { ... } }`
    * 在开通和续费实例时 (`OrderFulfillmentService`)，应读取这些存储在 `product_config_details` 里的技术规格参数传给供应商API。
    * `ProductSyncService` 在更新时，如果策略不是 `FULL_SYNC`，则不应覆盖 `product_config_details` 中的这些技术规格（除非供应商明确表示该规格已变更，这属于更复杂的场景）。

**问题二：同步规则是否可以随时切换？**

**是的，完全可以，并且这是该方案的一个核心优势。**

* **切换机制**：
    * 运营人员可以在后台管理界面（例如 `admin-web/src/views/shop/product/edit.vue` 或 `management.vue`）随时修改单个或批量修改产品的 `sync_strategy` 字段。
    * 当 `sync_strategy` 发生改变后，下一次该产品的同步任务执行时，就会按照新的策略来进行。

* **切换场景举例**：
    1. **新产品导入**：产品首次从供应商同步到平台时，可以默认设置为 `STOCK_AND_COST_ONLY`。这样，系统会自动填充初始信息，然后运营人员可以开始编辑优化。
    2. **运营优化后保护数据**：运营人员花时间优化了产品A的名称、描述，并将价格设置为手动。此时，他们确保产品A的 `sync_strategy` 是 `STOCK_AND_COST_ONLY`（或者如果他们也想锁定技术参数，就设为 `MANUAL_OVERRIDE`）。这样，下次同步时，这些优化信息不会丢失。
    3. **供应商数据源发生重大变更或修正**：如果发现某个供应商提供的数据更准确了，或者平台上的某些旧信息需要被完全刷新，运营人员可以选择一批产品，将其 `sync_strategy` 临时修改为 `FULL_SYNC`，然后手动触发一次同步（或等待下一次自动同步）。同步完成后，可以将策略再改回 `STOCK_AND_COST_ONLY`。
    4. **特殊产品完全手动管理**：对于某些特殊或定制的产品，可以直接将其 `sync_strategy` 设置为 `MANUAL_OVERRIDE` 或 `DISABLED`，完全由运营人员手动维护。

* **切换的影响**：
    * 从 `STOCK_AND_COST_ONLY` 切换到 `FULL_SYNC`：下次同步时，平台维护的名称、描述等会被供应商数据覆盖。价格如果不是手动 (`is_price_manual = false`) 也会被重新计算和覆盖。
    * 从 `FULL_SYNC` 切换到 `STOCK_AND_COST_ONLY`：下次同步时，只会更新库存、成本价和（如果价格非手动）销售价。当前已在平台上的名称、描述等信息将开始受到保护。
    * 切换到 `MANUAL_OVERRIDE`：下次同步时，产品名称、描述、价格（除非 `is_price_manual=false`）、`product_config_details` 均不会被同步更新，通常只有库存会被更新。
    * 切换到 `DISABLED`：该产品将不再参与自动同步流程。

**实现切换的关键点**：

* `ProductSyncService` 在每次处理一个产品时，**必须先读取该产品当前的 `sync_strategy`**，然后根据策略值来决定哪些字段允许被供应商数据更新。
* 前端界面提供清晰的切换操作，并可能需要对切换的后果进行提示，特别是当从保护性策略切换到 `FULL_SYNC` 时。

**5. 优点**

* **简洁性**：只增加一个 `sync_strategy` 字段，避免了大量 `is_xxx_manual` 标志。
* **灵活性**：未来可以方便地扩展新的同步策略。
* **清晰性**：运营人员对每个产品的同步行为有明确的控制。
* **数据保护**：有效保护运营人员的劳动成果。
* **关键数据同步**：保证了库存、成本等核心数据的准确性。

**6. 实施步骤建议**

1. **数据库变更**：添加 `sync_strategy` 字段到 `shop_product` 表，设置默认值。
2. **后端服务调整**：修改 `ProductSyncService` 的同步逻辑，根据 `sync_strategy` 进行条件更新。
3. **前端UI调整**：
    * 在产品编辑页面添加 `sync_strategy` 选择。
    * 在产品列表页面显示 `sync_strategy`。
    * 实现修改同步策略的交互（例如，编辑受保护字段时提示切换策略）。
    * 实现批量操作功能。
4. **数据迁移（如果需要）**：删除is_price_manual字段,和相关的功能
5. **测试**：充分测试不同同步策略下的行为。

**7. 进一步思考（高级功能）**

* **变更日志**：记录每次同步对产品数据造成的具体变更，特别是当 `sync_strategy` 设置为 `FULL_SYNC` 时的覆盖操作。`shop_product_price_history` 是个好的开始，可以扩展到其他字段。
