# Proxy Shop

一个基于 NestJS + Vue + React 的代理服务电商平台。

## 快速开始

### Worktree 开发环境设置

1. **初始设置**（新 worktree 只需运行一次）

   ```bash
   node scripts/setup-worktree-ports.js 100
   ```

   - 自动创建端口配置（偏移量 100：Server 32180, Admin 8988, Shop 32170）
   - 自动安装所有项目依赖（如果需要）

2. **启动所有服务**
   - VSCode: 按 `Ctrl+Shift+B` → 选择 "🚀 Start All Services (Worktree)"
   - 或者分别启动：

     ```bash
     cd server && npm run dev:worktree
     cd admin-web && npm run dev:worktree
     cd shop-web && npm run dev:worktree
     ```

3. **访问地址**（默认偏移量 100）
   - Server API: <http://localhost:32180>
   - Admin Panel: <http://localhost:8988>
   - Shop Frontend: <http://localhost:32170>

### 常规开发（不使用 worktree）

```bash
cd server && npm run dev
cd admin-web && npm run dev
cd shop-web && npm run dev
```

## 项目结构

- `server/` - NestJS 后端 API
- `admin-web/` - Vue.js 管理后台
- `shop-web/` - React 客户商城
- `scripts/` - 工具脚本
- `docs/` - 项目文档

## 端口说明

| 服务 | 默认端口 | Worktree 端口（偏移量 n） |
|------|---------|------------------------|
| Server | 32080 | 32080 + n |
| Admin | 8888 | 8888 + n |
| Shop | 32070 | 32070 + n |

## 🚀 部署和构建

### 快速构建（开发测试）

```bash
# 构建所有项目
python3 scripts/quick-build.py --projects all

# 构建单个项目
python3 scripts/quick-build.py --projects server
python3 scripts/quick-build.py --projects admin-web
python3 scripts/quick-build.py --projects shop-web
```

### 生产部署

```bash
# 1. 使用脚本管理器（推荐新手）
python3 scripts/script-manager.py --interactive

# 2. 直接使用部署管理器
python3 scripts/deploy-manager.py check              # 环境检查
python3 scripts/deploy-manager.py deploy --projects all  # 全栈部署

# 3. 创建版本部署包
python3 scripts/deploy-manager.py deploy --projects all --output "v1.2.0"
```

### 部署后验证

```bash
# 健康检查
node scripts/health-check.js localhost     # 本地环境
node scripts/health-check.js your-domain.com  # 生产环境
```

## 📊 代码报告生成

### 生成项目代码报告

使用 repomix 工具生成项目的代码分析报告：

```bash
# 生成所有模块的报告
node scripts/generate_reports/index.js

# 生成报告并复制到剪切板
node scripts/generate_reports/index.js -c

# 只生成特定模块的报告
node scripts/generate_reports/index.js -m server
node scripts/generate_reports/index.js -m server,admin-web

# 生成报告并附加静态文件（如数据库结构）
node scripts/generate_reports/index.js -a

# 只处理静态文件
node scripts/generate_reports/index.js -s -c

# 只生成特定模块的报告, 附加的静态文件, 使用剪切板
node scripts/generate_reports/index.js -m server -a -c

# 生成server模块报告，附加静态文件，并在完成后打开输出目录
node scripts/generate_reports/index.js -m server -a -o
```

### 报告输出

- 报告文件保存在项目根目录的 `repomix_outputs/` 文件夹
- 可用模块：`server`, `admin-web`, `shop-web`, `all-projects`
- 自动包含数据库结构文件等静态资源

## 更多信息

- 详细部署指南：`scripts/README.md`
- Worktree 配置说明：`docs/worktree端口管理方案-20250625.md`
- 项目开发指南：`CLAUDE.md`

## Serena 的主要文件搜索功能包括

  1. 按文件名搜索 (find_file) - 支持通配符 * 和 ?
  2. 按内容搜索 (search_for_pattern) - 支持正则表达式搜索文件内容
  3. 符号搜索 (find_symbol) - 搜索类、函数、方法等代码符号
  4. 引用搜索 (find_referencing_symbols) - 查找符号的引用位置
  5. 目录浏览 (list_dir) - 列出目录内容
  6. 符号概览 (get_symbols_overview) - 获取文件/目录的符号概览
