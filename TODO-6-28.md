---

### **数据库备份与恢复执行方案**

**版本**: 1.0
**日期**: 2024-05-24
**方案负责人**: SuperAssistant

#### 1. 方案概述

本方案旨在为您的在线交易系统建立一个自动化、安全且经济高效的数据库灾备系统。方案采用业界推荐的**3-2-1备份原则**（3份数据，2种不同介质，1份异地存储），将生产数据库（运行于 Docker）的备份数据安全地存储在 Cloudflare R2 对象存储上。

**核心组件:**

* **数据库**: 您现有的 MySQL 8.0 (Dockerized)。
* **备份工具**: `databack/mysql-backup-s3` (Dockerized)，一个轻量级、自动化的备份工具。
* **存储目标**: Cloudflare R2，一个 S3 兼容的、无出口费用的云对象存储。

**关键特性:**

* **自动化备份**: 无需人工干预，按预定计划自动执行。
* **异地容灾**: 备份存储于独立的云服务，有效应对服务器级别的灾难。
* **时间点恢复 (PITR)**: 通过备份二进制日志 (Binlog)，可将数据恢复到故障发生前的最后一分钟，最大限度减少交易数据丢失。
* **成本效益**: Cloudflare R2 无数据出口费用，大幅降低数据恢复成本。
* **空间管理**: 备份文件和数据库日志均有自动清理机制，防止存储空间无限增长。

---

#### 2. 准备清单 (Prerequisites & Checklist)

在开始实施前，请确保您已准备好以下信息和访问权限。请将收集到的信息填写在此清单中，以便后续步骤使用。

**所需资料:**

| 类别             | 项目                       | 您的信息填写处                             | 备注                                         |
| :--------------- | :------------------------- | :--------------------------------------- | :------------------------------------------- |
| **Cloudflare R2**  | **账户 ID (Account ID)**     | `<您的R2账户ID>`                           | 登录Cloudflare后，在R2概览页面可找到。       |
|                  | **存储桶名称 (Bucket Name)** | `your-project-db-backups`                | 建议使用此名称，或您自定义的名称。           |
|                  | **API 访问密钥 ID**        | `<您的R2访问密钥ID>`                       | 创建API令牌后生成。                          |
|                  | **API 私有访问密钥**       | `<您的R2私有访问密钥>`                     | **仅显示一次，请务必安全保存！**             |
|                  | **R2 Endpoint URL**        | `https://<您的R2账户ID>.r2.cloudflarestorage.com` | 将`<您的R2账户ID>`替换为实际值。             |
| **MySQL 数据库** | **数据库容器名**           | `<您的MySQL容器名>`                        | 通过 `docker ps` 查看。                      |
|                  | **数据库服务名 (网络内)**  | `mysql`                                  | 在`docker-compose.yml`中定义的服务名。         |
|                  | **数据库名称**             | `proxy_shop`                             | 您项目使用的数据库名。                       |
|                  | **备份专用用户名**         | `backupuser`                             | 推荐使用此名称。                             |
|                  | **备份专用用户密码**       | `<您为backupuser设置的强密码>`             | 请设置一个强密码并记录。                     |
| **生产服务器**     | **Docker & Docker Compose**| -                                        | 确保已安装并可正常使用。                     |

---

#### 3. 实施步骤

##### **阶段一：云端准备 (Cloudflare R2 设置)**

1. **登录 Cloudflare 控制台**，导航至 "R2"。
2. **创建存储桶**:
    * 点击 "创建存储桶"。
    * **存储桶名称**: 输入您在清单中规划的名称 (例如: `your-project-db-backups`)。
    * 点击 "创建存储桶"。
3. **创建 API 令牌**:
    * 在 R2 概览页面右侧，点击 "管理 R2 API 令牌"。
    * 点击 "创建 API 令牌"。
    * **权限**: 选择 **编辑 (Read & Write)**。
    * 点击 "创建 API 令牌"。
4. **安全保存凭证**: 系统会显示您的 `访问密钥 ID` 和 `私有访问密钥`。**请立即将它们复制并保存到安全的密码管理器中**，并将它们填写到上方的准备清单中。

##### **阶段二：数据库准备 (MySQL 数据库配置)**

1. **启用 Binlog**:
    * 这是实现时间点恢复的**强制要求**。
    * 在您的 `docker-compose.yml` 文件中，找到您的 MySQL 服务，在 `command` 或 `environment` 部分添加 Binlog 相关配置。推荐在 `command` 中添加，以确保配置生效。

2. **创建备份专用用户**:
    * 连接到您的 MySQL 数据库容器。
    * 执行以下 SQL 命令，创建一个权限受限的专用用户，以提高安全性。

        ```sql
        -- 请将 'YourStrongPasswordHere' 替换为您在清单中规划的强密码
        CREATE USER 'backupuser'@'%' IDENTIFIED BY 'YourStrongPasswordHere';

        -- 授予备份所需的最小权限
        GRANT SELECT, LOCK TABLES, SHOW VIEW, RELOAD, REPLICATION CLIENT, EVENT, TRIGGER ON *.* TO 'backupuser'@'%';

        -- 刷新权限使之生效
        FLUSH PRIVILEGES;
        ```

##### **阶段三：部署备份服务**

1. **编辑 `docker-compose.yml`**:
    * 在您的项目根目录下，打开 `docker-compose.yml` 文件。
    * 将下面 **“6. 最终交付物”** 中提供的完整 `docker-compose.yml` 内容复制或整合到您现有的文件中。
    * 仔细检查并**将所有 `<...>` 占位符替换为您在准备清单中收集到的真实信息**。
    * **特别注意**：对于交易系统，已将 `BINLOG_SCHEDULE` 优化为**每5分钟一次**，这是推荐的最佳实践。

---

#### 4. 部署与验证

1. **启动服务**: 在 `docker-compose.yml` 所在目录，执行命令以应用更改并启动新的备份服务。

    ```bash
    docker-compose up -d
    ```

2. **手动触发首次备份**: 为立即验证所有配置是否正确，请手动执行一次备份。

    ```bash
    # 将 <您的MySQL备份容器名> 替换为实际名称，如 your-mysql-backup-container
    docker exec -it <您的MySQL备份容器名> backup
    ```

3. **检查日志**: 查看备份容器的实时日志，确认没有错误。

    ```bash
    # 将 <您的MySQL备份容器名> 替换为实际名称
    docker logs -f <您的MySQL备份容器名>
    ```

    您应该能看到类似 `Uploading backup to S3...` 和 `Backup complete` 的成功日志。按 `Ctrl+C` 退出日志查看。

4. **在 Cloudflare R2 中验证**: 再次登录 R2 控制台，进入您的存储桶。您应该能看到一个名为 `database/` 的文件夹，里面存放着刚刚生成的 `proxy_shop-xxxx.sql.gz` 备份文件。

---

#### 5. 维护与恢复指南

##### **恢复流程 (紧急情况使用)**

1. **下载备份**: 从 R2 控制台下载最新的完整备份文件 (`.sql.gz`) 和所有在该备份之后生成的 Binlog 文件。
2. **解压备份**: `gunzip your-backup-file.sql.gz`
3. **恢复完整备份**:

    ```bash
    # 将 <您的MySQL容器名> 等占位符替换为实际值
    docker exec -i <您的MySQL容器名> mysql -u<用户名> -p<密码> <数据库名> < your-backup-file.sql
    ```

4. **应用 Binlog**: (如果需要恢复到特定时间点)

    ```bash
    # 对每个 binlog 文件按时间顺序执行
    docker exec -i <您的MySQL容器名> mysqlbinlog /path/to/binlog.00000X | mysql -u<用户名> -p<密码> <数据库名>
    ```

##### **维护计划**

* **恢复演练 (重要)**: 建议**每季度**进行一次完整的恢复演练，将备份恢复到一个临时的、隔离的数据库中，以确保备份文件的有效性和您对恢复流程的熟悉度。
* **监控告警**: 设置一个简单的监控，如果 R2 存储桶在25小时内没有收到新的备份文件，则发送邮件或通知告警。

---

#### 6. 最终交付物 (Final Deliverable)

以下是为您准备好的、可以直接使用的 `docker-compose.yml` 配置。请复制并根据您的实际情况替换占位符。

```yaml
version: '3.8'

services:
  # 您的现有MySQL服务 (示例)
  mysql:
    image: mysql:8.0
    container_name: <您的MySQL容器名> # 例如: proxy_shop_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: <您的MySQL root密码>
      MYSQL_DATABASE: proxy_shop
    volumes:
      - mysql_data:/var/lib/mysql
    # 关键：添加 command 以启用 binlog 和设置过期策略
    command:
      - '--server-id=1'
      - '--log-bin=/var/lib/mysql/mysql-bin'
      - '--expire_logs_days=7' # 在服务器上保留7天的binlog，自动清理
      - '--binlog-format=ROW'
    networks:
      - your-app-network

  # ... 您其他的应用服务，例如您的 NestJS 后端 ...

  # 新增的备份服务
  mysql-backup:
    image: databack/mysql-backup-s3:latest
    container_name: <您的MySQL备份容器名> # 例如: proxy_shop_mysql_backup
    restart: always
    depends_on:
      - mysql
    environment:
      # --- MySQL 连接配置 ---
      MYSQL_HOST: mysql # 使用docker-compose的服务名
      MYSQL_DATABASE: proxy_shop
      MYSQL_USER: backupuser
      MYSQL_PASSWORD: <您为backupuser设置的强密码>

      # --- Cloudflare R2 (S3) 配置 ---
      S3_ENDPOINT: https://<您的R2账户ID>.r2.cloudflarestorage.com
      S3_REGION: auto
      S3_ACCESS_KEY_ID: <您的R2访问密钥ID>
      S3_SECRET_ACCESS_KEY: <您的R2私有访问密钥>
      S3_BUCKET: your-project-db-backups
      S3_PATH: 'database/'

      # --- 备份计划与选项 ---
      SCHEDULE: "0 3 * * *" # 每天凌晨3点执行一次完整备份
      BACKUP_FILENAME: 'proxy_shop-%Y-%m-%d-%H-%M-%S.sql.gz'
      MYSQLDUMP_OPTS: "--single-transaction --routines --triggers --events" # --single-transaction 对交易系统至关重要

      # --- 时间点恢复 (PITR) 配置 - 交易系统推荐 ---
      BACKUP_BINLOG: "true"
      BINLOG_SCHEDULE: "*/5 * * * *" # 每5分钟备份一次binlog，大大缩短RPO

      # --- 清理策略 ---
      MAX_BACKUPS: "14" # 在R2中保留最近14天的完整备份

    networks:
      - your-app-network

networks:
  your-app-network:
    driver: bridge

volumes:
  mysql_data:

```

## 服务器现状调查报告

**调查时间**: 2025-06-28 20:06:06 CST
**调查执行人**: SuperClaude
**服务器信息**: 雨云-8c16g-hk (instance-5pDEYep5)

### 1. 系统基础环境

* **操作系统**: Debian 11 (Docker环境)
* **Docker版本**: 28.0.4
* **Docker Compose**: 已经安装
* **磁盘空间**: 79GB总容量，已使用20GB，可用57GB (26%使用率)
* **数据库数据大小**: 956MB

### 2. MySQL数据库当前配置状态

#### 2.1 容器状态

* **容器名称**: `proxy-shop-db-mysql-8`

* **镜像版本**: mysql:8.0
* **运行状态**: ✅ 正常运行 (Up 2 weeks, healthy)
* **端口映射**: 0.0.0.0:37443->3306/tcp

#### 2.2 数据库配置

* **数据库名称**: `proxy_shop` ✅

* **表数量**: 59个表（应该没有那么多个)
* **认证插件**: mysql_native_password
* **字符集**: utf8mb4/utf8mb4_unicode_ci

#### 2.3 关键配置检查结果

| 配置项 | 当前状态 | 备份要求 | 状态 |
|--------|----------|----------|------|
| **Binary Log (Binlog)** | ✅ 已启用 (log_bin=ON) | 必需 | ✅ 符合要求 |
| **Binlog格式** | ✅ ROW格式 | 推荐 | ✅ 符合要求 |
| **Binlog过期清理** | ❌ expire_logs_days=0 | 需要设置 | ⚠️ 需要配置 |
| **服务器ID** | ❌ 未明确配置 | 需要设置 | ⚠️ 需要配置 |

### 3. 备份系统现状

#### 3.1 当前备份状态

* **自动备份系统**: ❌ 不存在

* **备份容器**: ❌ 未部署
* **计划任务**: ❌ 无相关crontab
* **手动备份**: ❌ 未发现备份文件

#### 3.2 用户权限状态

* **backupuser**: ❌ 不存在 (需要创建)

* **当前用户**: root, dev_user_8

### 4. 环境变量配置

当前.env配置与文档提供信息对比：

| 项目 | .env配置 | 提供信息 | 状态 |
|------|----------|----------|------|
| MySQL密码 | UPWEOgIvx9vL9QLvzmJL | UPWEOgIvx9vL9QLvzmJL | ✅ 一致 |
| 数据库名 | dev_database_8 | proxy_shop | ❌ 不匹配 |
| 端口 | 37443 | 37443 | ✅ 一致 |

**注意**: 实际使用的数据库是 `proxy_shop`，但env配置显示为 `dev_database_8`

### 5. 实施准备度评估

#### 5.1 已具备条件 ✅

* [x] Docker环境正常

* [x] MySQL 8.0运行正常
* [x] Binlog已启用
* [x] 充足的磁盘空间
* [x] Cloudflare R2凭证已准备
* [x] 网络连接正常

#### 5.2 需要配置的项目 ⚠️

* [ ] 设置binlog过期清理策略 (expire_logs_days)

* [ ] 配置MySQL服务器ID
* [ ] 创建备份专用用户 (backupuser)
* [ ] 部署mysql-backup-s3容器
* [ ] 更新docker-compose.yml配置

#### 5.3 风险评估 🔥

* **高风险**: 当前无任何备份机制，数据完全无保护

* **中风险**: Binlog未设置过期清理，可能导致磁盘空间不足
* **低风险**: 环境配置文件与实际使用有差异

### 6. 下一步建议

1. **紧急**: 立即实施备份系统部署
2. **优先**: 配置binlog清理策略，防止磁盘空间耗尽
3. **次要**: 统一环境配置与实际数据库名称

### 7. 实施准备清单更新

基于调查结果，更新准备清单：

| 类别 | 项目 | 实际值 | 状态 |
|------|------|---------|------|
| **Cloudflare R2** | 账户ID | cd6de4177085516ade50d541ff506b0b | ✅ 已获取 |
| | 存储桶名称 | proxy-shop-prod-backups | ✅ 已获取 |
| | API访问密钥ID | dfd2dfd7df736a6f24dc29d97d487589 | ✅ 已获取 |
| | API私有访问密钥 | 994766fe7be98ddb6d1dd47bc781116eb5069423e30adbdb5d46e6a55d15e833 | ✅ 已获取 |
| **MySQL数据库** | 容器名 | proxy-shop-db-mysql-8 | ✅ 已确认 |
| | 数据库名称 | proxy_shop | ✅ 已确认 |
| | 网络名称 | proxy-shop-network | ✅ 已确认 |

**调查结论**: 服务器环境基本满足备份系统部署要求，建议立即开始实施阶段。
